{"name": "ibc-final-dashboard", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:web": "turbo -F web dev", "db:push": "turbo -F web db:push", "db:studio": "turbo -F web db:studio", "db:generate": "turbo -F web db:generate", "db:migrate": "turbo -F web db:migrate", "export:sheets": "bunx tsx scripts/google-sheets-export.ts"}, "dependencies": {"@clerk/nextjs": "^6.31.6", "@clerk/types": "^4.83.0", "convex": "^1.18.0", "googleapis": "^159.0.0", "svix": "^1.76.1"}, "devDependencies": {"shadcn": "^3.3.1", "turbo": "^2.5.4"}, "packageManager": "bun@1.2.15"}