# Repository Guidelines

## Project Structure & Module Organization
- Turborepo workspace with Bun tooling; primary Next.js app lives in `apps/web`.
- UI routes sit in `apps/web/src/app`, shared components under `apps/web/src/components`, and server utilities in `apps/web/src/lib/server`.
- Tests reside in `apps/web/test` (e.g., `format.test.ts`); Tailwind globals live in `apps/web/src/index.css`; static assets in `apps/web/public`.
- Keep environment templates in `.env.example` (root) and `apps/web/.env.example` up to date when adding configuration.

## Build, Test, and Development Commands
- `bun run dev` (or `bun run dev:web`) spins up the local Next.js dev server via Turbo.
- `bun run build` triggers the workspace build, including `next build` for the web app.
- `bun run check-types` performs a repository-wide `tsc --noEmit` type check.
- `bun -C apps/web run lint` runs the web lint rules; `bun -C apps/web test` executes all Bun-based tests under `apps/web/test`.

## Coding Style & Naming Conventions
- Code in TypeScript + React with 2-space indentation; favor Tailwind CSS v4 utility classes and `class-variance-authority` for variants.
- Name components in PascalCase (`TestimonialForm.tsx`), hooks `useThing.ts`, and utilities with camelCase in `src/lib/**`.
- Place page entry points at `src/app/**/page.tsx`; colocate small reusable UI in `src/components/ui/`.

## Testing Guidelines
- Use Bun test runner; create unit tests as `*.test.ts` within `apps/web/test`.
- Keep tests deterministic, covering data formatting, guards, and server helpers; avoid network calls.
- Run `bun -C apps/web test` locally before pushing and ensure new utilities ship with matching coverage.

## Commit & Pull Request Guidelines
- Follow Conventional Commit messages (`feat:`, `fix:`, optional scopes like `fix(media-metrics): …`).
- PRs should explain the change, link relevant issues, add screenshots for UI work, and call out database or schema updates.
- Document manual verification steps and included tests in the PR description.

## Security & Configuration Tips
- Never commit secrets; copy `.env.example` files to `.env.local` for local runs.
- Validate environment variables through `src/lib/security/env-validation.ts` and reuse sanitization helpers in `src/lib/security/**` when handling user input.
