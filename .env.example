# Database
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-role-key

# Google Sheets Integration (Optional - use either this OR CSV_FILE_PATH)
GOOGLE_SHEETS_ID=1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms
GOOGLE_SHEETS_TAB=Sheet1
GOOGLE_SERVICE_ACCOUNT_EMAIL=<EMAIL>
GOOGLE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----"

# CSV File Path (Alternative to Google Sheets)
CSV_FILE_PATH=/Users/<USER>/to/your/file.csv

# Vercel Cron Jobs
CRON_SECRET=your-secure-random-string-for-cron-auth

# Auth/Security
BETTER_AUTH_SECRET=your-auth-secret
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_CONVEX_URL=https://your-project.convex.cloud
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_yourPublishableKey
CLERK_SECRET_KEY=sk_test_yourSecretKey
CLERK_JWT_ISSUER_DOMAIN=https://your-app.clerk.accounts.dev
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_FALLBACK_REDIRECT_URL=/dashboard

# Optional: Additional services
BRAVE_API_KEY=your-brave-search-api-key
OPENAI_API_KEY=your-openai-api-key
