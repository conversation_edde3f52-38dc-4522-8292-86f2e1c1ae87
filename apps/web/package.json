{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@tanstack/react-form": "^1.12.3", "@uploadthing/react": "^7.3.2", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.26.2", "csv-parse": "^6.1.0", "date-fns": "^3.6.0", "dotenv": "^17.2.1", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "googleapis": "^156.0.0", "lucide-react": "^0.487.0", "next": "15.3.0", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-day-picker": "^9.1.4", "react-dom": "^19.0.0", "recharts": "^3.2.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.6", "uploadthing": "^7.7.3", "zod": "^4.0.5"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4.1.11", "typescript": "^5", "@tanstack/react-query-devtools": "^5.80.5"}}