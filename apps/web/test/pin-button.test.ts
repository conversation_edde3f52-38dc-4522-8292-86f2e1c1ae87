import type { Id } from "@/../../../convex/_generated/dataModel";
import { getPinnedGroupIdsForUser } from "@/components/pin-button";

describe("getPinnedGroupIdsForUser", () => {
  test("returns empty set when user is missing", () => {
    const result = getPinnedGroupIdsForUser(
      [
        { _id: "group_1" as Id<"groups">, user_id: "user_1" },
      ],
      null
    );

    expect(Array.from(result)).toEqual([]);
  });

  test("filters groups by matching user_id", () => {
    const groups = [
      { _id: "group_1" as Id<"groups">, user_id: "user_1" },
      { _id: "group_2" as Id<"groups">, user_id: "user_2" },
      { _id: "group_3" as Id<"groups">, user_id: "user_1" },
    ];

    const result = getPinnedGroupIdsForUser(groups, { user_id: "user_1" });

    expect(Array.from(result)).toEqual(["group_1", "group_3"]);
  });

  test("ignores groups without identifiers", () => {
    const groups = [
      { _id: "group_1" as Id<"groups">, user_id: "user_1" },
      { user_id: "user_1" },
    ];

    const result = getPinnedGroupIdsForUser(groups as any[], { user_id: "user_1" });

    expect(Array.from(result)).toEqual(["group_1"]);
  });
});
