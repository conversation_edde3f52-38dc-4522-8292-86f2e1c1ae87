import { formatNumber } from "@/lib/utils/format";

describe('formatNumber', () => {
  test('formats numbers correctly', () => {
    expect(formatNumber(0)).toBe('0');
    expect(formatNumber(999)).toBe('999');
    expect(formatNumber(1000)).toBe('1.0K');
    expect(formatNumber(15349)).toBe('15.3K');
    expect(formatNumber(999999)).toBe('1000.0K');
    expect(formatNumber(1_000_000)).toBe('1.0M');
    expect(formatNumber(Number.NaN)).toBe('—');
  });
});

