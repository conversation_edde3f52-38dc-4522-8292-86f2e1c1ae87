"use client";

import { useEffect, useMemo, useState } from "react";
import { useQuery } from "convex/react";
import { useRouter, useSearchParams } from "next/navigation";
import { api } from "@/../../../convex/_generated/api";
import { TwitterContentCard } from "./TwitterContentCard";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Twitter } from "lucide-react";
import { SectionHeaderSkeleton } from "@/components/skeletons/SectionHeaderSkeleton";
import { TwitterCardSkeleton } from "@/components/skeletons/TwitterCardSkeleton";
import type {
  TwitterContentData
} from "@/lib/utils/twitter";

// Extended interface that includes all Convex content fields
interface ConvexContentData extends TwitterContentData {
  content_types?: string[];
  content_categories?: string[];
}
import { 
  isTwitterContent 
} from "@/lib/utils/twitter";

interface TwitterContentGridProps {
  className?: string;
  // Optional external filters from main search bar
  externalSearchQuery?: string;
  externalContentTypes?: string[];
  externalCategories?: string[];
  externalSortBy?: "impressions" | "date";
  externalSortOrder?: "desc" | "asc";
}

type SortOption = 'impressions' | 'likes' | 'retweets' | 'date' | 'engagement';
type FilterOption = 'all' | 'tweet' | 'twitter_space' | 'twitter_thread' | 'retweet';

export function TwitterContentGrid({
  className = "",
  externalSearchQuery,
  externalContentTypes,
  externalCategories,
  externalSortBy,
  externalSortOrder
}: TwitterContentGridProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Initialize state from URL params when no external filters are provided
  const [searchQuery, setSearchQuery] = useState(() =>
    externalSearchQuery !== undefined ? "" : (searchParams.get("twitter_search") || "")
  );
  const [sortBy, setSortBy] = useState<SortOption>(() =>
    externalSortBy !== undefined ? 'impressions' : ((searchParams.get("twitter_sort") as SortOption) || 'impressions')
  );
  const [filterBy, setFilterBy] = useState<FilterOption>(() =>
    externalContentTypes !== undefined ? 'all' : ((searchParams.get("twitter_filter") as FilterOption) || 'all')
  );
  const [page, setPage] = useState(1);
  const [displayCount, setDisplayCount] = useState(6);
  const limit = 9;

  // Determine if we should use external filters
  const hasExternalFilters = Boolean(
    externalSearchQuery !== undefined ||
    externalContentTypes !== undefined ||
    externalCategories !== undefined ||
    externalSortBy !== undefined ||
    externalSortOrder !== undefined
  );

  // Use external filters when available, otherwise use internal state
  const effectiveSearchQuery = hasExternalFilters ? (externalSearchQuery || "") : searchQuery;
  const effectiveSortBy = hasExternalFilters ? (externalSortBy || "impressions") : sortBy;
  const effectiveSortOrder = hasExternalFilters ? (externalSortOrder || "desc") : "desc";

  // Accumulated items across pages for "Load more"
  const [items, setItems] = useState<ConvexContentData[]>([]);
  const [total, setTotal] = useState<number>(0);

  // Sync URL parameters when internal state changes (only when no external filters)
  useEffect(() => {
    if (hasExternalFilters) return; // Don't sync URL when using external filters

    const params = new URLSearchParams(searchParams.toString());

    // Update Twitter-specific params
    if (searchQuery.trim()) {
      params.set("twitter_search", searchQuery);
    } else {
      params.delete("twitter_search");
    }

    if (sortBy !== 'impressions') {
      params.set("twitter_sort", sortBy);
    } else {
      params.delete("twitter_sort");
    }

    if (filterBy !== 'all') {
      params.set("twitter_filter", filterBy);
    } else {
      params.delete("twitter_filter");
    }

    const nextQuery = params.toString();
    const currentQuery = searchParams.toString();

    if (nextQuery !== currentQuery) {
      const newUrl = nextQuery ? `${window.location.pathname}?${nextQuery}` : window.location.pathname;
      router.replace(newUrl, { scroll: false });
    }
  }, [searchQuery, sortBy, filterBy, hasExternalFilters, router, searchParams]);

  // Sync state with URL changes (when no external filters)
  useEffect(() => {
    if (hasExternalFilters) return;

    const urlSearch = searchParams.get("twitter_search") || "";
    const urlSort = (searchParams.get("twitter_sort") as SortOption) || 'impressions';
    const urlFilter = (searchParams.get("twitter_filter") as FilterOption) || 'all';

    if (urlSearch !== searchQuery) setSearchQuery(urlSearch);
    if (urlSort !== sortBy) setSortBy(urlSort);
    if (urlFilter !== filterBy) setFilterBy(urlFilter);
  }, [searchParams, hasExternalFilters]);

  // Fetch paginated twitter content (spaces + tweets)
  const twitterQuery = useQuery(api.content.getAllTwitterContent, { page, limit });
  const dashboardSettingsQuery = useQuery(api.dashboardSettings.getDashboardSettings);
  const pinnedContentIds = useQuery(api.groups.getPinnedContentIds, {});

  // Merge pages as user loads more
  useEffect(() => {
    if (!twitterQuery) return; // still loading
    setTotal(twitterQuery.total || 0);
    if (twitterQuery?.data) {
      setItems(prev => {
        // Avoid duplicates when Convex revalidates
        const seen = new Set(prev.map(i => i._id));
        const next = [...prev];
        for (const it of twitterQuery.data as any[]) {
          if (!seen.has(it._id)) next.push(it);
        }
        return next;
      });
    }
  }, [twitterQuery?.data, twitterQuery?.total]);

  // Filter and sort content
  const pinnedIdSet = useMemo(() => new Set(pinnedContentIds || []), [pinnedContentIds]);

  const filteredAndSortedContent = useMemo(() => {
    if (!Array.isArray(items)) return [];

    let filtered = items
      .filter((content: any) => isTwitterContent(content))
      .filter((content: ConvexContentData) => {
        // Search filter
        if (effectiveSearchQuery.trim()) {
          const query = effectiveSearchQuery.toLowerCase();
          const title = content.content_title?.toLowerCase() || '';
          const description = content.content_description?.toLowerCase() || '';
          const account = content.content_account?.join(' ').toLowerCase() || '';

          if (!title.includes(query) && !description.includes(query) && !account.includes(query)) {
            return false;
          }
        }

        // External content type filters (when using main search bar)
        if (hasExternalFilters && externalContentTypes && externalContentTypes.length > 0) {
          const matchesType = externalContentTypes.some(filterType => {
            switch (filterType) {
              case "spaces":
                return content.twitter_content_type === "space";
              case "tweets":
                return content.twitter_content_type === "tweet";
              case "twitter":
                return content.content_link?.includes('x.com') ||
                       content.content_link?.includes('twitter.com') ||
                       (content.twitter_impressions && content.twitter_impressions > 0);
              default:
                return content.content_types?.includes(filterType);
            }
          });
          if (!matchesType) return false;
        }
        // Internal Twitter-specific type filter (when no external filters)
        else if (!hasExternalFilters && filterBy !== 'all') {
          const t = (content.twitter_content_type || '').toLowerCase();
          const normalized = t === 'space' ? 'twitter_space' : t === 'thread' ? 'twitter_thread' : t;
          if (normalized !== filterBy) return false;
        }

        // External category filters (when using main search bar)
        if (hasExternalFilters && externalCategories && externalCategories.length > 0) {
          const matchesCategory = externalCategories.some(category =>
            content.content_categories?.includes(category)
          );
          if (!matchesCategory) return false;
        }

        return true;
      });

    // Sort content
    filtered.sort((a: ConvexContentData, b: ConvexContentData) => {
      if (hasExternalFilters) {
        // Use external sorting
        if (effectiveSortBy === "date") {
          const dateA = a.content_created_date || 0;
          const dateB = b.content_created_date || 0;
          return effectiveSortOrder === "asc" ? dateA - dateB : dateB - dateA;
        } else {
          const impressionsA = a.twitter_impressions || 0;
          const impressionsB = b.twitter_impressions || 0;
          return effectiveSortOrder === "asc" ? impressionsA - impressionsB : impressionsB - impressionsA;
        }
      } else {
        // Use internal sorting
        switch (sortBy) {
          case 'impressions':
            return (b.twitter_impressions || 0) - (a.twitter_impressions || 0);
          case 'likes':
            return (b.twitter_likes || 0) - (a.twitter_likes || 0);
          case 'retweets':
            return (b.twitter_retweets || 0) - (a.twitter_retweets || 0);
          case 'date':
            return (b.content_created_date || 0) - (a.content_created_date || 0);
          case 'engagement':
            const getEngagement = (content: ConvexContentData) => {
              const impressions = content.twitter_impressions || 1;
              const likes = content.twitter_likes || 0;
              const retweets = content.twitter_retweets || 0;
              return (likes + retweets) / impressions;
            };
            return getEngagement(b) - getEngagement(a);
          default:
            return 0;
        }
      }
    });

    return filtered;
  }, [items, effectiveSearchQuery, effectiveSortBy, effectiveSortOrder, sortBy, filterBy, hasExternalFilters, externalContentTypes, externalCategories]);

  // Stats overview removed per design feedback

  const handleRefresh = () => {
    // This will trigger a re-fetch of the data
    window.location.reload();
  };

  if (twitterQuery === undefined || dashboardSettingsQuery === undefined) {
    return (
      <Card className={`p-8 ${className}`}>
        <div className="space-y-6">
          <SectionHeaderSkeleton />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-7 xl:gap-8">
            {Array.from({ length: 6 }).map((_, i) => (
              <TwitterCardSkeleton key={i} />
            ))}
          </div>
        </div>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Twitter className="h-6 w-6 text-primary" />
            <h2 className="text-2xl font-bold">Twitter Spaces & Tweets</h2>
          </div>
          <Badge variant="secondary" className="ml-2">
            {filteredAndSortedContent.length} items
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="hidden md:block text-sm text-muted-foreground mr-2">
            Page {page} of {Math.max(1, Math.ceil(total / limit))} • {total} items
          </div>
        </div>
      </div>

      {/* Filters panel - only show when no external filters are active */}
      {!hasExternalFilters && (
        <div className="flex flex-wrap items-center gap-3">
          {/* Search */}
          <div className="relative flex-1 min-w-[200px] max-w-md">
            <input
              type="text"
              placeholder="Search Twitter content..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-4 py-2 text-sm border border-border/40 rounded-xl bg-background/60 focus:outline-none focus:border-primary/60 focus:ring-2 focus:ring-primary/20"
            />
          </div>

          {/* Filter by type */}
          <select
            value={filterBy}
            onChange={(e) => setFilterBy(e.target.value as FilterOption)}
            className="px-3 py-2 text-sm border border-border/40 rounded-xl bg-background/60 focus:outline-none focus:border-primary/60"
          >
            <option value="all">All Types</option>
            <option value="tweet">Tweets</option>
            <option value="twitter_space">Spaces</option>
            <option value="twitter_thread">Threads</option>
            <option value="retweet">Retweets</option>
          </select>

          {/* Sort by */}
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as SortOption)}
            className="px-3 py-2 text-sm border border-border/40 rounded-xl bg-background/60 focus:outline-none focus:border-primary/60"
          >
            <option value="impressions">By Impressions</option>
            <option value="likes">By Likes</option>
            <option value="retweets">By Retweets</option>
            <option value="date">By Date</option>
            <option value="engagement">By Engagement</option>
          </select>

          {/* Clear filters */}
          {(searchQuery || filterBy !== 'all' || sortBy !== 'impressions') && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setSearchQuery("");
                setFilterBy('all');
                setSortBy('impressions');
              }}
              className="text-muted-foreground hover:text-foreground"
            >
              Clear
            </Button>
          )}
        </div>
      )}

      {/* Content Grid */}
      {filteredAndSortedContent.length === 0 ? (
        <Card className="p-12">
          <div className="text-center">
            <Twitter className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No Twitter content found</h3>
            <p className="text-muted-foreground">
              {searchQuery || filterBy !== 'all'
                ? "Try adjusting your search or filter criteria."
                : "Twitter content will appear here once added to your database."
              }
            </p>
          </div>
        </Card>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-7 xl:gap-8">
            {filteredAndSortedContent.length > displayCount - 1 ? (
              <>
                {filteredAndSortedContent.slice(0, displayCount - 1).map((content: ConvexContentData, index: number) => (
                  <TwitterContentCard
                    key={content._id ?? index}
                    content={content}
                    dashboardSettings={dashboardSettingsQuery}
                    isPinned={Boolean(content._id && pinnedIdSet.has(content._id as any))}
                  />
                ))}
                <button
                  type="button"
                  onClick={() => {
                    console.log('Load more clicked:', {
                      currentDisplayCount: displayCount,
                      filteredContentLength: filteredAndSortedContent.length,
                      totalItems: total,
                      currentPage: page
                    });

                    // Always add 6 more to display count
                    setDisplayCount(prev => prev + 6);

                    // If we need more data from server and haven't reached total
                    const willNeedMoreData = displayCount + 6 > filteredAndSortedContent.length;
                    if (willNeedMoreData && items.length < total) {
                      console.log('Fetching next page:', page + 1);
                      setPage(prev => prev + 1);
                    }
                  }}
                  className="group relative flex h-full min-h-[240px] flex-col items-center justify-center gap-4 overflow-hidden rounded-3xl border border-dashed border-border/50 bg-gradient-to-br from-background/85 via-background/65 to-primary/15 p-6 text-center transition-all duration-500 hover:-translate-y-2 hover:border-primary/60 hover:shadow-[0_22px_45px_rgba(15,15,45,0.18)]"
                >
                  <span className="text-xs font-semibold uppercase tracking-[0.4em] text-muted-foreground/70">
                    Load More
                  </span>
                  <span className="text-4xl font-bold text-primary transition-transform duration-500 group-hover:scale-110">
                    +6
                  </span>
                  <p className="max-w-[220px] text-sm text-muted-foreground">
                    Unlock six more tweets & spaces with a single tap.
                  </p>
                  <div
                    aria-hidden
                    className="pointer-events-none absolute inset-0 opacity-0 transition-opacity duration-500 group-hover:opacity-100"
                    style={{ background: "radial-gradient(circle at center, rgba(34,197,94,0.22), transparent 65%)" }}
                  />
                </button>
              </>
            ) : (
              filteredAndSortedContent.slice(0, displayCount).map((content: ConvexContentData, index: number) => (
                <TwitterContentCard
                  key={content._id ?? index}
                  content={content}
                  dashboardSettings={dashboardSettingsQuery}
                  isPinned={Boolean(content._id && pinnedIdSet.has(content._id as any))}
                />
              ))
            )}
          </div>
        </>
      )}
    </div>
  );
}
