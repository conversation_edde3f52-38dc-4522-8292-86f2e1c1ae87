"use client";

import { useEffect, useMemo, useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@/../../../convex/_generated/api";
import { TwitterContentCard } from "./TwitterContentCard";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Search, Filter } from "lucide-react";
import { SectionHeaderSkeleton } from "@/components/skeletons/SectionHeaderSkeleton";
import { CardSkeleton } from "@/components/skeletons/CardSkeleton";
import type { TwitterContentData } from "@/lib/utils/twitter";

interface FilteredContentGridProps {
  searchQuery: string;
  contentTypes: string[];
  categories: string[];
  sortBy: "impressions" | "date";
  sortOrder: "desc" | "asc";
  className?: string;
}

export function FilteredContentGrid({
  searchQuery,
  contentTypes,
  categories,
  sortBy,
  sortOrder,
  className = ""
}: FilteredContentGridProps) {
  const [page, setPage] = useState(1);
  const [allItems, setAllItems] = useState<any[]>([]);
  const limit = 12;

  // Fetch filtered content with pagination
  const filteredQuery = useQuery(api.content.getFilteredContent, {
    search: searchQuery,
    contentTypes,
    categories,
    sortBy,
    sortOrder,
    page,
    limit
  });

  const dashboardSettingsQuery = useQuery(api.dashboardSettings.getDashboardSettings);
  const pinnedContentIds = useQuery(api.groups.getPinnedContentIds, {});

  const pinnedIdSet = useMemo(() => new Set(pinnedContentIds || []), [pinnedContentIds]);

  // Reset pagination when filters change
  useEffect(() => {
    setPage(1);
    setAllItems([]);
  }, [searchQuery, contentTypes, categories, sortBy, sortOrder]);

  // Accumulate items for "Load more" functionality
  useEffect(() => {
    if (!filteredQuery?.data) return;

    if (page === 1) {
      // First page - replace all items
      setAllItems(filteredQuery.data);
    } else {
      // Additional pages - append items
      setAllItems(prev => {
        const seen = new Set(prev.map(item => item._id));
        const newItems = filteredQuery.data.filter((item: any) => !seen.has(item._id));
        return [...prev, ...newItems];
      });
    }
  }, [filteredQuery?.data, page]);

  const handleLoadMore = () => {
    setPage(prev => prev + 1);
  };

  const isLoading = filteredQuery === undefined || dashboardSettingsQuery === undefined;
  const hasResults = allItems.length > 0;
  const canLoadMore = filteredQuery && allItems.length < filteredQuery.total;
  const isLoadingMore = page > 1 && filteredQuery === undefined;

  const getDisplayTitle = () => {
    const filters = [];

    if (contentTypes.length > 0) {
      const typeLabels = contentTypes.map(type => {
        const typeMap: Record<string, string> = {
          twitter: "Twitter",
          spaces: "Spaces",
          tweets: "Tweets",
          marketing: "Twitter", // Marketing content is Twitter content
          presskit: "Press Kit",
          incubation: "Incubation",
          testimonials: "Testimonials"
        };
        return typeMap[type] || type.charAt(0).toUpperCase() + type.slice(1);
      });
      filters.push(...typeLabels);
    }

    if (categories.length > 0) {
      const categoryLabels = categories.map(cat => cat.toUpperCase());
      filters.push(...categoryLabels);
    }

    if (searchQuery) {
      return `Search: "${searchQuery}"`;
    }

    if (filters.length > 0) {
      return `Filtered: ${filters.join(" • ")}`;
    }

    return "All Content";
  };

  if (isLoading) {
    return (
      <Card className={`p-8 ${className}`}>
        <div className="space-y-6">
          <SectionHeaderSkeleton />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <CardSkeleton key={i} />
            ))}
          </div>
        </div>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {searchQuery ? (
              <Search className="h-6 w-6 text-primary" />
            ) : (
              <Filter className="h-6 w-6 text-primary" />
            )}
            <h2 className="text-2xl font-bold">{getDisplayTitle()}</h2>
          </div>
          {filteredQuery && (
            <Badge variant="secondary" className="ml-2">
              {filteredQuery.total} items
            </Badge>
          )}
        </div>

        {filteredQuery && (
          <div className="hidden md:block text-sm text-muted-foreground">
            Showing {allItems.length} of {filteredQuery.total} items
          </div>
        )}
      </div>

      {/* Content Grid */}
      {!hasResults ? (
        <Card className="p-12">
          <div className="text-center">
            {searchQuery ? (
              <Search className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            ) : (
              <Filter className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            )}
            <h3 className="text-lg font-medium mb-2">No content found</h3>
            <p className="text-muted-foreground">
              {searchQuery
                ? `No results found for "${searchQuery}". Try adjusting your search or filters.`
                : "No content matches the selected filters. Try adjusting your filter criteria."
              }
            </p>
          </div>
        </Card>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {allItems.map((content: TwitterContentData) => (
              <TwitterContentCard
                key={content._id}
                content={content}
                dashboardSettings={dashboardSettingsQuery}
                isPinned={Boolean(content._id && pinnedIdSet.has(content._id as any))}
              />
            ))}
          </div>

          {/* Load More Button */}
          {canLoadMore && (
            <div className="flex justify-center pt-4">
              <Button
                variant="default"
                onClick={handleLoadMore}
                disabled={isLoadingMore}
                className="min-w-[160px]"
              >
                {isLoadingMore ? "Loading..." : "Load More"}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
