"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@/../../../convex/_generated/api";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  ExternalLink, 
  Quote,
  Star,
  Calendar,
  Building2,
  Image as ImageIcon,
  ChevronLeft,
  ChevronRight,
  X
} from "lucide-react";

interface TestimonialCardProps {
  testimonial: any;
  className?: string;
}

export function TestimonialCard({ testimonial, className = "" }: TestimonialCardProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showLightbox, setShowLightbox] = useState(false);
  const [imageError, setImageError] = useState<string[]>([]);

  // Get image URLs for screenshots
  const screenshotUrls = testimonial.screenshot_urls?.map((storageId: string) => 
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useQuery(api.files.getFileUrl, { storageId: storageId as any })
  ).filter(Boolean) || [];

  const validScreenshots = screenshotUrls.filter((url: any, index: number) => 
    url && !imageError.includes(testimonial.screenshot_urls[index])
  );

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  const handleImageError = (storageId: string) => {
    setImageError(prev => [...prev, storageId]);
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => 
      prev === validScreenshots.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? validScreenshots.length - 1 : prev - 1
    );
  };

  const openLightbox = (index: number) => {
    setCurrentImageIndex(index);
    setShowLightbox(true);
  };

  return (
    <>
      <Card 
        className={`group relative overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-[1.02] bg-gradient-to-br from-background/80 via-background/90 to-background/95 backdrop-blur-sm border border-border/50 ${className}`}
      >
        {/* Glass effect overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-transparent pointer-events-none" />
        
        <div className="p-6 relative z-10">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1 min-w-0">
              {/* Client info */}
              <div className="flex items-center gap-2 mb-2">
                <Building2 className="h-4 w-4 text-muted-foreground" />
                <span className="font-semibold text-foreground">
                  {testimonial.content_account?.[0] || "Anonymous Client"}
                </span>
              </div>
              
              {/* Project title */}
              <h3 className="font-semibold text-lg leading-tight text-foreground group-hover:text-primary transition-colors">
                {testimonial.content_title || "Untitled Project"}
              </h3>
              
              {/* Rating */}
              {testimonial.content_views > 0 && (
                <div className="flex items-center gap-1 mt-1">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star 
                      key={i}
                      className={`h-3 w-3 ${
                        i < testimonial.content_views 
                          ? 'fill-yellow-400 text-yellow-400' 
                          : 'text-muted-foreground'
                      }`}
                    />
                  ))}
                </div>
              )}
            </div>
            
            {/* External link button */}
            {testimonial.content_link && (
              <Button
                size="sm"
                variant="ghost"
                className="shrink-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={(e) => {
                  e.stopPropagation();
                  window.open(testimonial.content_link, '_blank', 'noopener,noreferrer');
                }}
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Testimonial text */}
          <div className="relative mb-4">
            <Quote className="absolute -top-1 -left-1 h-8 w-8 text-primary/20 fill-current" />
            <blockquote className="pl-8 text-foreground/90 italic leading-relaxed">
              {testimonial.content_description || "No testimonial provided."}
            </blockquote>
          </div>

          {/* Screenshots */}
          {validScreenshots.length > 0 && (
            <div className="mb-4">
              <div className="relative aspect-video rounded-lg overflow-hidden bg-muted">
                {validScreenshots[currentImageIndex] && (
                  <img
                    src={validScreenshots[currentImageIndex]}
                    alt={`${testimonial.content_title} screenshot`}
                    className="w-full h-full object-cover cursor-pointer transition-transform hover:scale-105"
                    onClick={() => openLightbox(currentImageIndex)}
                    onError={() => handleImageError(testimonial.screenshot_urls[currentImageIndex])}
                  />
                )}
                
                {/* Image navigation */}
                {validScreenshots.length > 1 && (
                  <>
                    <Button
                      size="sm"
                      variant="secondary"
                      className="absolute left-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => {
                        e.stopPropagation();
                        prevImage();
                      }}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="secondary"
                      className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => {
                        e.stopPropagation();
                        nextImage();
                      }}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                    
                    {/* Image indicator dots */}
                    <div className="absolute bottom-2 left-1/2 -translate-x-1/2 flex gap-1">
                      {validScreenshots.map((_: any, index: number) => (
                        <button
                          key={index}
                          className={`w-2 h-2 rounded-full transition-colors ${
                            index === currentImageIndex 
                              ? 'bg-white' 
                              : 'bg-white/50'
                          }`}
                          onClick={(e) => {
                            e.stopPropagation();
                            setCurrentImageIndex(index);
                          }}
                        />
                      ))}
                    </div>
                  </>
                )}
                
                {/* Screenshot count badge */}
                <Badge 
                  variant="secondary" 
                  className="absolute top-2 right-2 text-xs flex items-center gap-1"
                >
                  <ImageIcon className="h-3 w-3" />
                  {validScreenshots.length}
                </Badge>
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="flex items-center justify-between pt-4 border-t border-border/50">
            {/* Date */}
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Calendar className="h-3 w-3" />
              {formatDate(testimonial.content_created_date)}
            </div>
            
            {/* Project value */}
            {testimonial.twitter_impressions > 0 && (
              <Badge variant="outline" className="text-xs">
                ${testimonial.twitter_impressions.toLocaleString()} value
              </Badge>
            )}
          </div>
        </div>
        
        {/* Hover gradient effect */}
        <div className="absolute inset-0 bg-gradient-to-t from-primary/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
      </Card>

      {/* Lightbox */}
      {showLightbox && validScreenshots.length > 0 && (
        <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
          <div className="relative max-w-4xl max-h-full">
            <Button
              size="sm"
              variant="secondary"
              className="absolute -top-12 right-0 z-10"
              onClick={() => setShowLightbox(false)}
            >
              <X className="h-4 w-4" />
            </Button>
            
            <img
              src={validScreenshots[currentImageIndex]}
              alt={`${testimonial.content_title} screenshot`}
              className="max-w-full max-h-full object-contain rounded-lg"
            />
            
            {validScreenshots.length > 1 && (
              <>
                <Button
                  size="lg"
                  variant="secondary"
                  className="absolute left-4 top-1/2 -translate-y-1/2"
                  onClick={prevImage}
                >
                  <ChevronLeft className="h-6 w-6" />
                </Button>
                <Button
                  size="lg"
                  variant="secondary"
                  className="absolute right-4 top-1/2 -translate-y-1/2"
                  onClick={nextImage}
                >
                  <ChevronRight className="h-6 w-6" />
                </Button>
              </>
            )}
          </div>
        </div>
      )}
    </>
  );
}