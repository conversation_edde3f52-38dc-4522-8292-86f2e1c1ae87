"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PinButton } from "@/components/pin-button";
import { ExternalLink, Mic2 } from "lucide-react";
import type { 
  TwitterContentData, 
  DashboardSettings
} from "@/lib/utils/twitter";
import {
  getTwitterContentTypeDisplay,
  formatContentDate,
  truncateTitle,
  extractHashtags,
  getTwitterDomain,
  getContentTypeEmoji,
  getVisibleMetrics
} from "@/lib/utils/twitter";

interface TwitterContentCardProps {
  content: TwitterContentData;
  dashboardSettings: DashboardSettings;
  className?: string;
  isPinned?: boolean;
}

export function TwitterContentCard({
  content,
  dashboardSettings,
  className = "",
  isPinned = false,
}: TwitterContentCardProps) {
  const hashtags = extractHashtags(content.content_title || content.content_description);
  const metrics = getVisibleMetrics(content, dashboardSettings);
  const emoji = getContentTypeEmoji(content.twitter_content_type);
  
  const handleViewClick = () => {
    if (content.content_link) {
      window.open(content.content_link, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <Card className={`bg-white border border-gray-200 rounded-2xl p-5 sm:p-6 hover:shadow-md transition-shadow duration-200 overflow-hidden h-full flex flex-col ${className}`}>
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-start justify-between gap-3 min-h-[48px] sm:min-h-[56px]">
          <div className="flex items-start gap-2 flex-1">
            {emoji && <span className="text-lg leading-none mt-0.5">{emoji}</span>}
            <h3 className="font-semibold text-[17px] sm:text-lg text-gray-900 leading-snug flex-1 line-clamp-2 break-words">
              {content.content_title || 'Untitled Content'}
            </h3>
          </div>
          {isPinned && (
            <Badge
              variant="secondary"
              className="text-[11px] sm:text-xs font-semibold bg-emerald-50 text-emerald-600 border border-emerald-100"
            >
              Pinned
            </Badge>
          )}
        </div>
        {hashtags.length > 0 ? (
          <div className="flex flex-wrap items-center gap-2 min-h-[24px]">
            {hashtags.map((hashtag, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="text-[11px] sm:text-xs font-medium bg-blue-50 text-blue-600 hover:bg-blue-100"
              >
                {hashtag}
              </Badge>
            ))}
          </div>
        ) : (
          // Reserve vertical space to anchor metrics row across cards
          <div className="min-h-[24px]" />
        )}
      </div>

      {/* Account */}
      {content.content_account && content.content_account.length > 0 ? (
        <div className="pt-2">
          <div className="flex items-center gap-2 flex-wrap min-h-[24px]">
            <span className="text-sm text-gray-600 font-medium whitespace-nowrap">
              @{content.content_account[0]}
            </span>
            <Badge variant="outline" className="text-[11px] sm:text-xs flex items-center gap-1">
              {getTwitterContentTypeDisplay(content.twitter_content_type).toLowerCase()}
              {(content.twitter_content_type === 'twitter_space' || content.twitter_content_type === 'space') && (
                <Mic2 className="h-3.5 w-3.5" />
              )}
            </Badge>
          </div>
        </div>
      ) : (
        <div className="pt-2 min-h-[24px]" />
      )}

      {/* Metrics Row - responsive with subtle separators and anchored height */}
      <div className={`mt-3 mb-4 ${metrics.length > 0 ? '' : 'min-h-[56px]'}`}>
        {metrics.length > 0 && (
          <div
            className={
              metrics.length === 1
                ? 'flex justify-center'
                : metrics.length === 2
                ? 'grid grid-cols-2 divide-x divide-gray-200/70 dark:divide-gray-800'
                : 'grid grid-cols-3 divide-x divide-gray-200/70 dark:divide-gray-800'
            }
          >
            {metrics.map((metric) => (
              <div key={metric.label} className="text-center px-3 sm:px-4">
                {/* anchor height so numbers align across cards */}
                <div className="min-h-[52px] sm:min-h-[58px] flex flex-col items-center justify-start">
                  <div className="text-[11px] sm:text-xs text-gray-600 font-medium mb-1">{metric.label}</div>
                  <div className={`font-bold text-base sm:text-lg ${metric.color || 'text-gray-900'}`}>{metric.value}</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer with Date and View Button */}
      <div className={`flex items-center gap-2 mt-auto pt-2 ${dashboardSettings.content_created_date_visible ? 'justify-between' : 'justify-end'}`}>
        {dashboardSettings.content_created_date_visible && (
          <div className="text-xs text-gray-500">
            {content.content_created_date
              ? new Date(content.content_created_date).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric'
                })
              : 'Unknown date'
            }
          </div>
        )}

        <div className="flex items-center gap-2 shrink-0">
          {content._id && (
            <PinButton
              contentId={content._id}
              contentTitle={content.content_title}
              contentUrl={content.content_link}
              isPinned={isPinned}
            />
          )}
          <Button 
            size="sm" 
            className="bg-yellow-400 hover:bg-yellow-500 text-black font-medium px-3 py-1 h-8 text-xs rounded-md"
            onClick={handleViewClick}
          >
            View
            <ExternalLink className="h-3 w-3 ml-1" />
          </Button>
        </div>
      </div>
    </Card>
  );
}
