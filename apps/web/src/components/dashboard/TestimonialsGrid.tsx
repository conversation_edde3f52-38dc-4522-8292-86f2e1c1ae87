"use client";

import { useState, useMemo, useEffect } from "react";
import { useQuery } from "convex/react";
import { useRouter, useSearchParams } from "next/navigation";
import { api } from "@/../../../convex/_generated/api";
import { TestimonialCard } from "./TestimonialCard";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Search, 
  Filter, 
  Grid3X3, 
  List,
  ChevronLeft,
  ChevronRight,
  X
} from "lucide-react";
import { SectionHeaderSkeleton } from "@/components/skeletons/SectionHeaderSkeleton";
import { TestimonialCardSkeleton } from "@/components/skeletons/TestimonialCardSkeleton";

interface TestimonialsGridProps {
  className?: string;
  showFilters?: boolean;
  showSearch?: boolean;
  showPagination?: boolean;
  itemsPerPage?: number;
  viewMode?: "grid" | "list";
}

export function TestimonialsGrid({
  className = "",
  showFilters = true,
  showSearch = true,
  showPagination = true,
  itemsPerPage = 12,
  viewMode: initialViewMode = "grid"
}: TestimonialsGridProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [searchQuery, setSearchQuery] = useState(() => searchParams.get("q") || "");
  const [selectedClient, setSelectedClient] = useState<string | null>(() => searchParams.get("client") || null);
  const [currentPage, setCurrentPage] = useState(() => parseInt(searchParams.get("page") || "1"));
  const [viewMode, setViewMode] = useState<"grid" | "list">(initialViewMode);

  // Sync URL parameters when state changes
  useEffect(() => {
    const params = new URLSearchParams();
    const trimmedQuery = searchQuery.trim();

    if (trimmedQuery) params.set("q", trimmedQuery);
    if (selectedClient) params.set("client", selectedClient);
    if (currentPage > 1) params.set("page", currentPage.toString());

    const nextQuery = params.toString();
    const currentQuery = searchParams.toString();

    if (nextQuery !== currentQuery) {
      router.replace(nextQuery ? `${window.location.pathname}?${nextQuery}` : window.location.pathname, { scroll: false });
    }
  }, [searchQuery, selectedClient, currentPage, router, searchParams]);

  // Sync state with URL changes
  useEffect(() => {
    const urlQuery = searchParams.get("q") || "";
    const urlClient = searchParams.get("client") || null;
    const urlPage = parseInt(searchParams.get("page") || "1");

    if (urlQuery !== searchQuery) setSearchQuery(urlQuery);
    if (urlClient !== selectedClient) setSelectedClient(urlClient);
    if (urlPage !== currentPage) setCurrentPage(urlPage);
  }, [searchParams]);
  
  // Fetch testimonials data
  const testimonials = useQuery(api.testimonials.getTestimonials, {
    page: currentPage,
    limit: itemsPerPage
  });

  // Filter and search testimonials
  const filteredTestimonials = useMemo(() => {
    if (!testimonials?.data) return [];
    
    let filtered = testimonials.data;
    
    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(testimonial => 
        testimonial.content_title?.toLowerCase().includes(query) ||
        testimonial.content_description?.toLowerCase().includes(query) ||
        testimonial.content_account?.some((account: string) => 
          account.toLowerCase().includes(query)
        )
      );
    }
    
    // Apply client filter
    if (selectedClient) {
      filtered = filtered.filter(testimonial =>
        testimonial.content_account?.includes(selectedClient)
      );
    }
    
    return filtered;
  }, [testimonials?.data, searchQuery, selectedClient]);

  // Get unique clients for filter
  const uniqueClients = useMemo(() => {
    if (!testimonials?.data) return [];
    
    const clients = new Set<string>();
    testimonials.data.forEach(testimonial => {
      testimonial.content_account?.forEach((account: string) => {
        clients.add(account);
      });
    });
    
    return Array.from(clients).sort();
  }, [testimonials?.data]);

  // Loading state
  if (!testimonials) {
    return (
      <div className={`space-y-6 ${className}`}>
        <SectionHeaderSkeleton />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: itemsPerPage }).map((_, i) => (
            <TestimonialCardSkeleton key={i} />
          ))}
        </div>
      </div>
    );
  }

  const totalPages = Math.ceil((testimonials.total || 0) / itemsPerPage);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with search and filters */}
      <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-semibold text-foreground">
            Client Testimonials
          </h2>
          <Badge variant="outline" className="text-sm">
            {testimonials.total || 0} total
          </Badge>
        </div>
        
        <div className="flex items-center gap-2 w-full lg:w-auto">
          {/* Search */}
          {showSearch && (
            <div className="relative flex-1 lg:w-80">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search testimonials..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
              {searchQuery && (
                <Button
                  size="sm"
                  variant="ghost"
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 p-0"
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>
          )}
          
          {/* View mode toggle */}
          <div className="flex border rounded-md">
            <Button
              size="sm"
              variant={viewMode === "grid" ? "default" : "ghost"}
              className="rounded-r-none"
              onClick={() => setViewMode("grid")}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant={viewMode === "list" ? "default" : "ghost"}
              className="rounded-l-none"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Client filter */}
      {showFilters && uniqueClients.length > 0 && (
        <div className="flex items-center gap-2 flex-wrap">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">Filter by client:</span>
          
          <Button
            size="sm"
            variant={selectedClient === null ? "default" : "outline"}
            onClick={() => setSelectedClient(null)}
            className="h-8"
          >
            All Clients
          </Button>
          
          {uniqueClients.slice(0, 8).map(client => (
            <Button
              key={client}
              size="sm"
              variant={selectedClient === client ? "default" : "outline"}
              onClick={() => setSelectedClient(client)}
              className="h-8"
            >
              {client}
              {selectedClient === client && (
                <X className="ml-1 h-3 w-3" />
              )}
            </Button>
          ))}
          
          {uniqueClients.length > 8 && (
            <Badge variant="secondary" className="text-xs">
              +{uniqueClients.length - 8} more
            </Badge>
          )}
        </div>
      )}

      {/* Results count */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <span>
          Showing {filteredTestimonials.length} of {testimonials.total || 0} testimonials
          {searchQuery && ` for "${searchQuery}"`}
          {selectedClient && ` from ${selectedClient}`}
        </span>
      </div>

      {/* Testimonials grid/list */}
      {filteredTestimonials.length === 0 ? (
        <div className="text-center py-12">
          <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
            <Search className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold mb-2">No testimonials found</h3>
          <p className="text-muted-foreground mb-4">
            {searchQuery || selectedClient 
              ? "Try adjusting your search or filter criteria"
              : "No client testimonials have been added yet"
            }
          </p>
          {(searchQuery || selectedClient) && (
            <Button
              variant="outline"
              onClick={() => {
                setSearchQuery("");
                setSelectedClient(null);
              }}
            >
              Clear filters
            </Button>
          )}
        </div>
      ) : (
        <div className={
          viewMode === "grid" 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            : "space-y-4"
        }>
          {filteredTestimonials.map((testimonial) => (
            <TestimonialCard
              key={testimonial._id}
              testimonial={testimonial}
              className={viewMode === "list" ? "max-w-none" : ""}
            />
          ))}
        </div>
      )}

      {/* Pagination */}
      {showPagination && totalPages > 1 && (
        <div className="flex items-center justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            disabled={currentPage === 1}
            onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Previous
          </Button>
          
          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(7, totalPages) }, (_, i) => {
              const pageNum = i + 1;
              if (totalPages <= 7) {
                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(pageNum)}
                    className="w-10"
                  >
                    {pageNum}
                  </Button>
                );
              }
              
              // Complex pagination logic for many pages
              let displayPage = pageNum;
              if (currentPage > 4 && totalPages > 7) {
                if (i < 3) displayPage = currentPage - 2 + i;
                else if (i > 3) displayPage = totalPages - 6 + i;
                else return <span key="ellipsis" className="px-2">...</span>;
              }
              
              return (
                <Button
                  key={displayPage}
                  variant={currentPage === displayPage ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPage(displayPage)}
                  className="w-10"
                >
                  {displayPage}
                </Button>
              );
            })}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            disabled={currentPage === totalPages}
            onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
          >
            Next
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      )}
    </div>
  );
}
