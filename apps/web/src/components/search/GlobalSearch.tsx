"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useRouter } from "next/navigation";
import { Search } from "lucide-react";
import { SearchBar } from "@/components/search-bar";
import { FilteredContentGrid } from "@/components/dashboard/FilteredContentGrid";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Portal } from "@/components/ui/portal";

interface Filters {
  contentTypes: string[];
  categories: string[];
  sortBy: string;
  sortOrder: string;
}

interface QuickLink {
  label: string;
  description: string;
  contentTypes?: string[];
  categories?: string[];
  searchQuery?: string;
  sortBy?: "impressions" | "date";
  sortOrder?: "desc" | "asc";
}

const DEFAULT_FILTERS: Filters = {
  contentTypes: [],
  categories: [],
  sortBy: "impressions",
  sortOrder: "desc",
};

const QUICK_LINKS: QuickLink[] = [
  {
    label: "Mario interviews",
    description: "Spaces featuring <PERSON> and headline guests",
    contentTypes: ["spaces"],
    searchQuery: "<PERSON>",
    sortBy: "date",
  },
  {
    label: "Top sponsored shows",
    description: "Highest impression brand partnerships this quarter",
    contentTypes: ["spaces"],
    categories: ["sponsored"],
    sortBy: "impressions",
  },
  {
    label: "Press releases",
    description: "Grab media kits and coverage-ready announcements",
    contentTypes: ["presskit"],
    sortBy: "date",
  },
  {
    label: "Marketing wins",
    description: "Case studies and campaign threads",
    contentTypes: ["marketing"],
    categories: ["ai", "defi"],
  },
];

export function GlobalSearch() {
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState<Filters>({ ...DEFAULT_FILTERS });
  const [recentQueries, setRecentQueries] = useState<string[]>([]);

  const toggle = useCallback(() => {
    setOpen((prev) => !prev);
  }, []);

  useEffect(() => {
    if (typeof window === "undefined") return;
    try {
      const stored = window.localStorage.getItem("ibc-recent-searches");
      if (stored) {
        const parsed = JSON.parse(stored);
        if (Array.isArray(parsed)) {
          setRecentQueries(parsed.filter((value) => typeof value === "string"));
        }
      }
    } catch (error) {
      console.warn("Failed to load recent searches", error);
    }
  }, []);

  const rememberQuery = useCallback((raw: string) => {
    const trimmed = raw.trim();
    if (!trimmed) return;

    setRecentQueries((prev) => {
      const next = [trimmed, ...prev.filter((item) => item.toLowerCase() !== trimmed.toLowerCase())].slice(0, 6);
      if (typeof window !== "undefined") {
        window.localStorage.setItem("ibc-recent-searches", JSON.stringify(next));
      }
      return next;
    });
  }, []);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key.toLowerCase() === "k") {
        event.preventDefault();
        setOpen((prev) => !prev);
      }

      if (event.key === "Escape") {
        setOpen(false);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);

  useEffect(() => {
    if (open) {
      const previous = document.body.style.overflow;
      document.body.style.overflow = "hidden";
      return () => {
        document.body.style.overflow = previous;
      };
    }

    return undefined;
  }, [open]);

  const handleSearch = (query: string) => setSearchQuery(query);

  const handleFilter = (filterType: string, values: string[]) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: values,
    }));
  };

  const handleSort = (sortBy: string, sortOrder?: string) => {
    setFilters((prev) => ({
      ...prev,
      sortBy,
      sortOrder: sortOrder || prev.sortOrder,
    }));
  };

  const applyQuickLink = (link: QuickLink) => {
    setSearchQuery(link.searchQuery ?? "");
    setFilters({
      contentTypes: link.contentTypes ?? [],
      categories: link.categories ?? [],
      sortBy: link.sortBy ?? "impressions",
      sortOrder: link.sortOrder ?? "desc",
    });
    if (link.searchQuery) {
      rememberQuery(link.searchQuery);
    }
    setOpen(true);
  };

  const hasActiveFilters = useMemo(() => {
    return (
      searchQuery.trim().length > 0 ||
      filters.contentTypes.length > 0 ||
      filters.categories.length > 0 ||
      filters.sortBy !== DEFAULT_FILTERS.sortBy ||
      filters.sortOrder !== DEFAULT_FILTERS.sortOrder
    );
  }, [filters, searchQuery]);

  const canApplyToDashboard = hasActiveFilters;

  const applyToDashboard = useCallback(() => {
    const params = new URLSearchParams();
    const trimmed = searchQuery.trim();
    if (trimmed) params.set("q", trimmed);
    if (filters.contentTypes.length > 0) params.set("types", filters.contentTypes.join(","));
    if (filters.categories.length > 0) params.set("categories", filters.categories.join(","));
    if (filters.sortBy !== DEFAULT_FILTERS.sortBy) params.set("sort", filters.sortBy);
    if (filters.sortOrder !== DEFAULT_FILTERS.sortOrder) params.set("order", filters.sortOrder);

    rememberQuery(trimmed);
    const queryString = params.toString();
    router.push(queryString ? `/dashboard?${queryString}` : "/dashboard");
    setOpen(false);
  }, [filters, rememberQuery, router, searchQuery]);

  useEffect(() => {
    if (!open || !canApplyToDashboard) return;

    const handleMetaEnter = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key === "Enter") {
        event.preventDefault();
        applyToDashboard();
      }
    };

    window.addEventListener("keydown", handleMetaEnter);
    return () => window.removeEventListener("keydown", handleMetaEnter);
  }, [applyToDashboard, canApplyToDashboard, open]);

  const loadRecent = (query: string) => {
    setSearchQuery(query);
    rememberQuery(query);
  };

  const showResults = canApplyToDashboard;

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setOpen(true)}
        className="relative z-[1000] hidden sm:inline-flex items-center gap-2 rounded-xl border-border/50 bg-background/70 px-4 py-2"
      >
        <Search className="h-4 w-4" />
        <span>Search</span>
        <Badge variant="secondary" className="ml-2 hidden items-center gap-1 rounded-md px-2 py-1 text-[11px] font-medium sm:flex">
          ⌘K
        </Badge>
      </Button>

      <Button
        variant="ghost"
        size="icon"
        onClick={() => setOpen(true)}
        className="relative z-[1000] sm:hidden"
        aria-label="Open search"
      >
        <Search className="h-5 w-5" />
      </Button>

      {open && (
        <Portal>
          <div className="fixed inset-0 z-[2147483647] isolate flex flex-col overflow-auto bg-background/95 backdrop-blur">
            <div className="mx-auto w-full max-w-6xl px-4 py-10 sm:py-14">
              <div className="flex items-center justify-between gap-4 pb-6">
                <div className="space-y-1">
                  <h2 className="text-2xl font-semibold text-foreground/95">Global Search</h2>
                  <p className="text-sm text-muted-foreground">
                    Search across testimonials, spaces, tweets, marketing assets, and press releases.
                  </p>
                </div>
                <Button variant="ghost" onClick={toggle}>
                  Close
                </Button>
              </div>

              <div className="glass-effect rounded-3xl border border-border/30 p-6 sm:p-8 lg:p-10 shadow-2xl">
                <div className="space-y-8">
                  <SearchBar
                    onSearch={handleSearch}
                    onFilter={handleFilter}
                    onSort={handleSort}
                    filters={filters}
                    searchQuery={searchQuery}
                    placeholder="Search everything..."
                    autoFocus
                    onSubmitQuery={rememberQuery}
                  />

                  <div className="flex flex-wrap gap-3">
                    {QUICK_LINKS.map((link) => (
                      <button
                        key={link.label}
                        type="button"
                        className="group rounded-full border border-border/40 bg-muted/40 px-4 py-2 text-left text-sm transition hover:border-primary/40 hover:bg-primary/10"
                        onClick={() => applyQuickLink(link)}
                      >
                        <div className="font-semibold text-foreground/90 group-hover:text-primary">
                          {link.label}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {link.description}
                        </div>
                      </button>
                    ))}
                    {hasActiveFilters && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSearchQuery("");
                          setFilters({ ...DEFAULT_FILTERS });
                        }}
                      >
                        Clear filters
                      </Button>
                    )}
                  </div>

                  {recentQueries.length > 0 && (
                    <div className="flex flex-wrap items-center gap-2 text-xs text-muted-foreground">
                      <span className="font-semibold uppercase tracking-[0.28em] text-muted-foreground/70">
                        Recent
                      </span>
                      {recentQueries.map((item) => (
                        <button
                          key={item}
                          type="button"
                          className="rounded-full border border-border/40 bg-background/80 px-3 py-1 font-medium tracking-[0.18em] transition hover:border-primary/40 hover:text-primary"
                          onClick={() => loadRecent(item)}
                        >
                          {item}
                        </button>
                      ))}
                    </div>
                  )}

                  <div className="flex flex-wrap items-center justify-between gap-3">
                    <p className="text-xs text-muted-foreground">
                      {showResults
                        ? "Results update automatically as you adjust filters."
                        : "Start typing or pick a quick link to explore the content library."}
                    </p>
                    {canApplyToDashboard && (
                      <Button size="sm" onClick={applyToDashboard} className="gap-2">
                        Open on dashboard
                      </Button>
                    )}
                  </div>

                  {showResults ? (
                    <FilteredContentGrid
                      searchQuery={searchQuery}
                      contentTypes={filters.contentTypes}
                      categories={filters.categories}
                      sortBy={filters.sortBy as "impressions" | "date"}
                      sortOrder={filters.sortOrder as "desc" | "asc"}
                      className="bg-background/70"
                    />
                  ) : (
                    <div className="rounded-2xl border border-dashed border-border/40 bg-background/70 px-6 py-10 text-center text-sm text-muted-foreground">
                      Use filters or shortcuts above to preview results, or hit ⌘ + Enter to jump directly to the dashboard view.
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </Portal>
      )}
    </>
  );
}
