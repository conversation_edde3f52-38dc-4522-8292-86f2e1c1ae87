"use client";

import { useEffect, useMemo, useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@/../../../convex/_generated/api";
import type { MarketingCaseStudy } from "@/types/marketing";
import { CaseStudyChartCard } from "@/components/marketing/CaseStudyChartCard";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";

interface MarketingCaseStudiesGridProps {
  className?: string;
  initialPageSize?: number;
  showLoadMore?: boolean;
}

export function MarketingCaseStudiesGrid({
  className = "",
  initialPageSize = 6,
  showLoadMore = true,
}: MarketingCaseStudiesGridProps) {
  const [page, setPage] = useState(1);
  const [items, setItems] = useState<MarketingCaseStudy[]>([]);
  const limit = initialPageSize;

  const query = useQuery(api.marketingCaseStudies.listCaseStudies, {
    page,
    limit,
    includeUnpublished: false,
  });

  const isLoading = query === undefined;
  const total = query?.total ?? 0;
  const totalPages = query?.totalPages ?? 1;

  useEffect(() => {
    if (!query?.data) return;

    if (page === 1) {
      setItems(query.data as MarketingCaseStudy[]);
      return;
    }

    setItems((prev) => {
      const merged = [...prev, ...(query.data as MarketingCaseStudy[])];
      const deduped = Array.from(new Map(merged.map((item) => [item._id, item])).values());
      return deduped;
    });
  }, [query?.data, page]);

  const skeletonCount = useMemo(() => Math.max(initialPageSize, 3), [initialPageSize]);

  return (
    <div className={`space-y-8 ${className}`}>
      {isLoading && items.length === 0 ? (
        <div className="grid gap-8 md:grid-cols-2">
          {Array.from({ length: skeletonCount }).map((_, index) => (
            <Skeleton key={index} className="h-[420px] w-full rounded-3xl" />
          ))}
        </div>
      ) : items.length > 0 ? (
        <div className="grid gap-8 md:grid-cols-2 xl:grid-cols-3">
          {items.map((item) => (
            <CaseStudyChartCard key={item._id} caseStudy={item} />
          ))}
        </div>
      ) : (
        <div className="rounded-3xl border border-dashed border-border/50 bg-background/70 px-10 py-16 text-center">
          <h3 className="text-lg font-semibold text-foreground/90">No case studies yet</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            Publish case studies from the admin panel to showcase performance here.
          </p>
        </div>
      )}

      {showLoadMore && totalPages > page && (
        <div className="flex justify-center pt-4">
          <Button
            variant="outline"
            size="lg"
            disabled={isLoading}
            onClick={() => setPage((prev) => Math.min(totalPages, prev + 1))}
          >
            Load more case studies
          </Button>
        </div>
      )}

      {items.length > 0 && (
        <p className="text-center text-sm text-muted-foreground">
          Showing {items.length} of {total} case studies
        </p>
      )}
    </div>
  );
}
