"use client";

import Link from "next/link";
import { useMemo } from "react";
import { useQuery } from "convex/react";

import { api } from "@/../../../convex/_generated/api";
import { CaseStudyChartCard } from "@/components/marketing/CaseStudyChartCard";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

const FALLBACK_COUNT = 3;
const DISPLAY_COUNT = 3;

export function CaseStudiesSection() {
  const caseStudies = useQuery(api.marketingCaseStudies.listCaseStudies, {
    page: 1,
    limit: 6,
    includeUnpublished: false,
  });

  const isLoading = caseStudies === undefined;

  const content = useMemo(() => {
    if (isLoading) {
      return (
        <div className="grid gap-8 md:grid-cols-2">
          {Array.from({ length: FALLBACK_COUNT }).map((_, index) => (
            <div key={index} className="rounded-3xl border border-border/40 bg-background/80 p-8">
              <div className="mb-6 space-y-3">
                <Skeleton className="h-5 w-32 rounded-full" />
                <Skeleton className="h-8 w-48 rounded-full" />
                <Skeleton className="h-4 w-full rounded-full" />
                <Skeleton className="h-4 w-4/6 rounded-full" />
              </div>
              <Skeleton className="h-[220px] w-full rounded-2xl" />
              <div className="mt-6 flex items-center justify-between">
                <Skeleton className="h-10 w-36 rounded-full" />
                <Skeleton className="h-10 w-40 rounded-full" />
              </div>
            </div>
          ))}
        </div>
      );
    }

    if (!caseStudies?.data?.length) {
      return (
        <div className="flex flex-col items-center gap-6 rounded-3xl border border-dashed border-border/60 bg-background/60 px-10 py-16 text-center">
          <h3 className="text-2xl font-semibold text-foreground/90">Case studies coming soon</h3>
          <p className="max-w-2xl text-sm text-muted-foreground">
            We are actively curating success stories from our portfolio. Check back shortly or subscribe to our newsletter for launch updates.
          </p>
          <Button
            variant="outline"
            className="group border-border/60 bg-background px-6 py-5 text-sm font-semibold uppercase tracking-[0.18em]"
          >
            Join Waitlist
            <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
          </Button>
        </div>
      );
    }

    return (
      <div className="grid gap-8 lg:grid-cols-3">
        {caseStudies.data.slice(0, DISPLAY_COUNT).map((item) => (
          <CaseStudyChartCard key={item._id} caseStudy={item} />
        ))}
      </div>
    );
  }, [caseStudies, isLoading]);

  return (
    <section id="case-studies" className="content-container py-24">
      <div className="glass-effect card-hover rounded-3xl border border-border/40 p-8 sm:p-12 lg:p-16">
        <div className="mx-auto flex w-full max-w-5xl flex-col items-center gap-6 text-center">
          <span className="rounded-full border border-border/40 bg-muted/20 px-4 py-2 text-xs font-semibold uppercase tracking-[0.4em] text-muted-foreground/80">
            Marketing Case Studies
          </span>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold leading-tight text-foreground/95">
            Proof of Momentum Across Every Growth Channel
          </h2>
          <p className="max-w-3xl text-base text-muted-foreground/85">
            Explore interactive growth stories from leading Web3 launches. Each case study highlights the trajectory across Telegram, Discord, X, and more—backed by realtime metrics tracked through our media engine.
          </p>
        </div>

        <div className="mt-14">{content}</div>

        <div className="mt-12 flex flex-col items-center justify-between gap-4 sm:flex-row">
          <p className="text-sm text-muted-foreground">
            Showing {Math.min(DISPLAY_COUNT, caseStudies?.data?.length ?? 0)} of {caseStudies?.total ?? 0} case studies
          </p>
          <Link href="/marketing">
            <Button size="lg" className="flex items-center gap-2">
              View all case studies
              <ArrowRight className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
