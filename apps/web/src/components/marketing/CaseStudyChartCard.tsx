"use client";

import * as React from "react";
import Link from "next/link";
import {
  Area,
  AreaChart,
  CartesianGrid,
  ResponsiveContainer,
  XAxis,
} from "recharts";
import { ArrowRight } from "lucide-react";

import { Card, CardContent, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import type { ChartConfig } from "@/components/ui/chart";
import {
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { ChartPoint, MarketingCaseStudy } from "@/types/marketing";
import { cn } from "@/lib/utils";

type TimeRangeOption = "90d" | "30d" | "7d" | "all";

interface CaseStudyChartCardProps {
  caseStudy: MarketingCaseStudy;
  className?: string;
}

type ChartRow = {
  date: string;
  [seriesId: string]: string | number;
};

export function CaseStudyChartCard({ caseStudy, className }: CaseStudyChartCardProps) {
  const [timeRange, setTimeRange] = React.useState<TimeRangeOption>("90d");
  const chartId = React.useId();

  const { mergedData, latestTimestamp } = React.useMemo(() => {
    const dataMap = new Map<string, ChartRow>();
    let maxTimestamp = 0;

    caseStudy.chart_series.forEach((series) => {
      series.points.forEach((point) => {
        const row = dataMap.get(point.date) ?? { date: point.date };
        row[series.id] = point.value;
        dataMap.set(point.date, row);

        const timestamp = toTimestamp(point.date);
        if (timestamp > maxTimestamp) {
          maxTimestamp = timestamp;
        }
      });
    });

    const sorted = Array.from(dataMap.values()).sort((a, b) => toTimestamp(a.date) - toTimestamp(b.date));

    return { mergedData: sorted, latestTimestamp: maxTimestamp };
  }, [caseStudy.chart_series]);

  const filteredData = React.useMemo(() => {
    if (timeRange === "all" || !latestTimestamp) {
      return mergedData;
    }

    const days = parseInt(timeRange.replace("d", ""), 10);
    const cutoff = latestTimestamp - days * 24 * 60 * 60 * 1000;

    return mergedData.filter((entry) => toTimestamp(String(entry.date)) >= cutoff);
  }, [mergedData, latestTimestamp, timeRange]);

  const chartConfig = React.useMemo<ChartConfig>(() => {
    return caseStudy.chart_series.reduce<ChartConfig>((acc, series, index) => {
      const resolvedColor = resolveColor(series.color_token, index);
      acc[series.id] = {
        label: series.label,
        color: resolvedColor,
      };
      return acc;
    }, {});
  }, [caseStudy.chart_series]);

  const legendItems = caseStudy.legend ??
    caseStudy.chart_series.map((series, index) => ({
      label: series.label,
      color_token: series.color_token ?? fallbackToken(index),
      stroke_color: series.stroke_color,
    }));

  return (
    <Card
      className={cn(
        "relative flex h-full flex-col overflow-hidden border border-border/50 bg-gradient-to-br from-background/90 via-background/80 to-accent/10 shadow-[0_8px_32px_rgba(15,15,45,0.08)]",
        "before:absolute before:inset-0 before:-z-10 before:bg-[radial-gradient(circle_at_top,_rgba(0,199,41,0.08),_transparent_65%)]",
        className,
      )}
    >
      <CardHeader className="flex flex-col gap-4 border-b border-border/40 pb-6">
        <div className="flex flex-col gap-2">
          <CardTitle className="text-2xl font-semibold tracking-tight text-foreground/95">
            {caseStudy.title}
          </CardTitle>
          {caseStudy.summary && (
            <p className="max-w-2xl text-sm text-muted-foreground/90">{caseStudy.summary}</p>
          )}
        </div>

        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex flex-wrap items-center gap-2 text-xs font-semibold uppercase tracking-[0.18em] text-muted-foreground/80">
            <span>Case Study Metrics</span>
            <span className="hidden h-4 w-px bg-border/60 sm:block" aria-hidden="true" />
            <span className="rounded-full bg-muted/40 px-3 py-1 text-[11px] font-medium text-foreground/80">
              {formatDateRange(mergedData)}
            </span>
          </div>

          <Select value={timeRange} onValueChange={(value: TimeRangeOption) => setTimeRange(value)}>
            <SelectTrigger className="w-[160px] rounded-lg border-border/60 bg-background/60 text-foreground">
              <SelectValue placeholder="Last 90 days" />
            </SelectTrigger>
            <SelectContent className="rounded-xl border-border/60 bg-background/95 backdrop-blur">
              <SelectItem value="90d" className="rounded-lg">Last 90 days</SelectItem>
              <SelectItem value="30d" className="rounded-lg">Last 30 days</SelectItem>
              <SelectItem value="7d" className="rounded-lg">Last 7 days</SelectItem>
              <SelectItem value="all" className="rounded-lg">All time</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent className="flex flex-1 flex-col gap-6 p-0">
        <div className="relative px-4 py-6 sm:px-8">
          <ChartContainer config={chartConfig} className="h-[260px] bg-transparent p-0 shadow-none ring-0">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={filteredData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                <defs>
                  {caseStudy.chart_series.map((series, index) => {
                    const gradientId = `${chartId}-${series.id}-gradient`;
                    const color = resolveColor(series.color_token, index);
                    const stroke = resolveStroke(series, index);
                    const stopOpacity = series.fill_opacity ?? 0.22;

                    return (
                      <linearGradient key={gradientId} id={gradientId} x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor={stroke ?? color} stopOpacity={Math.min(0.85, stopOpacity * 3)} />
                        <stop offset="95%" stopColor={color} stopOpacity={stopOpacity / 2} />
                      </linearGradient>
                    );
                  })}
                </defs>
                <CartesianGrid vertical={false} strokeDasharray="4 8" stroke="rgba(148, 163, 184, 0.25)" />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={12}
                  minTickGap={32}
                  tickFormatter={(value) => formatXAxisLabel(String(value))}
                  stroke="rgba(148, 163, 184, 0.65)"
                  fontSize={12}
                />
                <ChartTooltip
                  cursor={{ strokeDasharray: "4 4", stroke: "rgba(148, 163, 184, 0.35)" }}
                  content={
                    <ChartTooltipContent
                      indicator="dot"
                      labelFormatter={(value) => formatTooltipLabel(String(value))}
                    />
                  }
                />
                {caseStudy.chart_series.map((series, index) => {
                  const gradientId = `${chartId}-${series.id}-gradient`;
                  const stroke = resolveStroke(series, index);
                  const fillColor = `url(#${gradientId})`;

                  return (
                    <Area
                      key={series.id}
                      type="monotone"
                      dataKey={series.id}
                      strokeWidth={2.8}
                      stroke={stroke}
                      fill={fillColor}
                      fillOpacity={1}
                      activeDot={{ r: 5, strokeWidth: 0 }}
                      connectNulls
                    />
                  );
                })}
                <ChartLegend
                  verticalAlign="bottom"
                  align="left"
                  content={<ChartLegendContent className="pt-4" />}
                />
              </AreaChart>
            </ResponsiveContainer>
          </ChartContainer>

          <div className="mt-4 hidden flex-wrap gap-3 sm:flex">
            {legendItems.map((item, index) => (
              <span
                key={`${item.label}-${index}`}
                className="flex items-center gap-2 rounded-full border border-border/60 bg-background/80 px-3 py-1 text-xs font-medium uppercase tracking-[0.12em] text-muted-foreground/80"
              >
                <span
                  className="size-2.5 rounded-full"
                  style={{ backgroundColor: resolveColor(item.color_token, index) }}
                />
                {item.label}
              </span>
            ))}
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex flex-col gap-4 border-t border-border/40 bg-muted/10 px-6 py-5 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-wrap items-center gap-2 text-xs uppercase tracking-[0.18em] text-muted-foreground/70">
          {caseStudy.tags?.map((tag) => (
            <span key={tag} className="rounded-full bg-muted/60 px-3 py-1 text-[11px] font-medium text-foreground/70">
              {tag}
            </span>
          ))}
        </div>

        <Button
          size="lg"
          asChild
          className="group relative overflow-hidden rounded-xl border border-ibc-green/20 bg-gradient-to-br from-ibc-green/85 via-ibc-green to-ibc-green-dark px-6 py-5 text-sm font-semibold uppercase tracking-[0.25em] text-white shadow-[0_10px_30px_rgba(0,199,41,0.35)] transition duration-300 hover:scale-[1.02] hover:shadow-[0_16px_40px_rgba(0,199,41,0.45)]"
        >
          <Link href={caseStudy.cta_url} target="_blank" rel="noopener noreferrer">
            <span className="flex items-center gap-3">
              {caseStudy.cta_label || "Explore Case Study"}
              <ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
            </span>
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}

function fallbackToken(index: number) {
  return `chart-${(index % 5) + 1}`;
}

function resolveColor(token: string | undefined, index: number): string {
  if (!token) {
    return `var(--chart-${(index % 5) + 1})`;
  }

  const trimmed = token.trim();
  if (trimmed.startsWith("var(")) return trimmed;
  if (trimmed.startsWith("--")) return `var(${trimmed})`;
  if (trimmed.startsWith("#") || trimmed.startsWith("rgb")) return trimmed;
  return `var(--${trimmed})`;
}

function resolveStroke(series: MarketingCaseStudy["chart_series"][number], index: number) {
  if (series.stroke_color) {
    return resolveColor(series.stroke_color, index);
  }
  return resolveColor(series.color_token, index);
}

function toTimestamp(value: string): number {
  const parsed = Date.parse(value);
  if (!Number.isNaN(parsed)) {
    return parsed;
  }
  return Number.isFinite(Number(value)) ? Number(value) : 0;
}

function formatXAxisLabel(value: string) {
  const timestamp = Date.parse(value);
  if (Number.isNaN(timestamp)) {
    return value;
  }
  return new Intl.DateTimeFormat("en-US", { month: "short", day: "numeric" }).format(new Date(timestamp));
}

function formatTooltipLabel(value: string) {
  const timestamp = Date.parse(value);
  if (Number.isNaN(timestamp)) {
    return value;
  }
  return new Intl.DateTimeFormat("en-US", { month: "long", day: "numeric", year: "numeric" }).format(new Date(timestamp));
}

function formatDateRange(data: ChartRow[]): string {
  if (!data.length) return "No data";
  const first = toTimestamp(String(data[0].date));
  const last = toTimestamp(String(data[data.length - 1].date));

  if (!first || !last) return "Custom";

  const formatter = new Intl.DateTimeFormat("en-US", { month: "short", day: "numeric" });
  return `${formatter.format(first)} — ${formatter.format(last)}`;
}

