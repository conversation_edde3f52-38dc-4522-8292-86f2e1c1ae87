"use client";

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface ClerkErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

export class ClerkErrorBoundary extends React.Component<
  { children: React.ReactNode },
  ClerkErrorBoundaryState
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): ClerkErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Clerk Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center p-4">
          <Card className="max-w-md w-full p-6 text-center space-y-4">
            <div className="text-red-500 text-lg font-semibold">
              🔐 Authentication Error
            </div>
            <div className="text-sm text-muted-foreground">
              {this.state.error?.message === 'Clerk: Failed to load Clerk' ? (
                <>
                  <p className="mb-2">Clerk failed to load. This is usually due to:</p>
                  <ul className="text-left space-y-1 mb-4">
                    <li>• Domain not configured in Clerk dashboard</li>
                    <li>• Browser blocking Clerk's scripts</li>
                    <li>• Network/firewall restrictions</li>
                  </ul>
                  <div className="bg-yellow-50 p-3 rounded text-yellow-800 mb-4">
                    <strong>Quick Fix:</strong> Add <code>http://localhost:*</code> to your Clerk dashboard under Domain → Development settings
                  </div>
                </>
              ) : (
                <p>Authentication system encountered an error: {this.state.error?.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Button 
                onClick={() => window.location.reload()} 
                className="w-full"
              >
                Reload Page
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  // Clear browser storage
                  localStorage.clear();
                  sessionStorage.clear();
                  window.location.reload();
                }}
                className="w-full"
              >
                Clear Cache & Reload
              </Button>
            </div>
            <div className="text-xs text-muted-foreground">
              Environment: {process.env.NODE_ENV} | 
              Port: {typeof window !== 'undefined' ? window.location.port : 'unknown'}
            </div>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}