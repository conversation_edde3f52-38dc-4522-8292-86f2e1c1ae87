"use client";

import { useClerk, useUser } from "@clerk/nextjs";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import { LogIn, LogOut, User, Settings } from "lucide-react";
import Image from "next/image";

export function AuthButton() {
  const { signOut, openSignIn } = useClerk();
  const { user, isLoaded } = useUser();
  
  const handleSignOut = () => {
    signOut();
    toast.success("Successfully signed out!");
  };

  if (!isLoaded) {
    return (
      <div className="w-10 h-10 rounded-full bg-muted animate-pulse" />
    );
  }

  if (!user) {
    return (
      <Button size="icon" variant="ghost" onClick={() => openSignIn()} className="rounded-full">
        <LogIn className="h-5 w-5" />
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full w-10 h-10 p-0 overflow-hidden ring-2 ring-transparent hover:ring-primary/20 transition-all duration-200"
        >
          {user.imageUrl ? (
            <Image
              src={user.imageUrl}
              alt={user.firstName || 'Profile'}
              width={40}
              height={40}
              className="w-full h-full object-cover rounded-full"
            />
          ) : (
            <User className="h-5 w-5" />
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="z-[2147483650]">
        <DropdownMenuItem disabled>
          <User className="h-4 w-4 mr-2" />
          {user.emailAddresses[0]?.emailAddress}
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleSignOut}>
          <LogOut className="h-4 w-4 mr-2" />
          Sign Out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}