"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { useAuth as useClerkAuth, useUser } from "@clerk/nextjs";
import { api } from "@/../../../convex/_generated/api";

interface User {
  id: string;
  email: string;
  full_name?: string;
  role: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  login: async () => {},
  logout: () => {},
});

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { isSignedIn, isLoaded } = useClerkAuth();
  const { user: clerkUser } = useUser();
  const [user, setUser] = useState<User | null>(null);
  
  // Get user from Convex database
  const convexUser = useQuery(api.auth.getCurrentUser, {});
  
  // Mutation to create/update user in Convex
  const createOrUpdateUser = useMutation(api.auth.createOrUpdateUser);

  useEffect(() => {
    if (isLoaded && isSignedIn && clerkUser) {
      // Sync Clerk user with Convex database
      const syncUser = async () => {
        try {
          console.log("AuthProvider: Syncing user with Convex", {
            clerkUserId: clerkUser.id,
            email: clerkUser.primaryEmailAddress?.emailAddress
          });

          await createOrUpdateUser({
            user_id: clerkUser.id,
            email: clerkUser.primaryEmailAddress?.emailAddress || "",
            full_name: clerkUser.fullName || undefined
          });

          console.log("AuthProvider: User sync completed successfully");
        } catch (error) {
          console.error("AuthProvider: Error syncing user with Convex:", error);
        }
      };

      // Add a small delay to ensure Convex is ready
      const timeoutId = setTimeout(syncUser, 100);
      return () => clearTimeout(timeoutId);
    }
  }, [isLoaded, isSignedIn, clerkUser, createOrUpdateUser]);

  useEffect(() => {
    console.log("AuthProvider: User state update", {
      hasConvexUser: !!convexUser,
      convexUserId: convexUser?.user_id,
      hasClerkUser: !!clerkUser,
      clerkUserId: clerkUser?.id
    });

    if (convexUser && clerkUser) {
      setUser({
        id: convexUser.user_id,
        email: convexUser.email,
        full_name: convexUser.full_name,
        role: convexUser.role
      });
      console.log("AuthProvider: User set successfully", convexUser.user_id);
    } else {
      setUser(null);
      console.log("AuthProvider: User cleared");
    }
  }, [convexUser, clerkUser]);

  const login = async (email: string) => {
    // Login is handled by Clerk components
    // This function is kept for compatibility but shouldn't be used
    console.warn("Use Clerk SignIn component for authentication");
  };

  const logout = () => {
    // Logout is handled by Clerk
    // This function is kept for compatibility but shouldn't be used
    console.warn("Use Clerk SignOut component for logout");
  };

  const loading = !isLoaded || (isSignedIn && convexUser === undefined);

  return (
    <AuthContext.Provider value={{ user, loading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
