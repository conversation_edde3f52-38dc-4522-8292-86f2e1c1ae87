"use client";

import Link from "next/link";
import { useMemo } from "react";
import { useQuery } from "convex/react";
import { api } from "@/../../../convex/_generated/api";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Quote, ArrowRight } from "lucide-react";

const DISPLAY_COUNT = 4;

interface TestimonialPreviewCardProps {
  testimonial: any;
}

function formatTestimonialDate(timestamp?: number) {
  if (!timestamp) return null;
  try {
    return new Date(timestamp).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric"
    });
  } catch (error) {
    console.error("Failed to format testimonial date", error);
    return null;
  }
}

function TestimonialPreviewCard({ testimonial }: TestimonialPreviewCardProps) {
  const content = testimonial?.content_description || "No testimonial provided.";
  const account = testimonial?.content_account?.[0];
  const title = testimonial?.content_title || account || "Anonymous";
  const dateLabel = formatTestimonialDate(testimonial?.content_created_date);

  return (
    <Card className="relative h-full overflow-hidden border border-border/40 bg-background/85 p-6 backdrop-blur">
      <Quote className="absolute -top-3 -left-3 h-10 w-10 text-primary/15" aria-hidden />
      <div className="relative flex h-full flex-col gap-4">
        <div className="space-y-1">
          <p className="text-sm uppercase tracking-[0.24em] text-muted-foreground/70">
            {account ? `@${account}` : "Client"}
          </p>
          <h3 className="text-xl font-semibold leading-tight text-foreground/95">
            {title}
          </h3>
        </div>

        <p className="line-clamp-5 text-base leading-relaxed text-muted-foreground">
          {content}
        </p>

        <div className="mt-auto flex items-center justify-between text-sm text-muted-foreground/80">
          {dateLabel ? <span>{dateLabel}</span> : <span>Latest win</span>}
          {testimonial?.content_link && (
            <Link
              href={testimonial.content_link}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary transition-colors hover:text-primary/80"
            >
              View story
            </Link>
          )}
        </div>
      </div>
    </Card>
  );
}

export function TestimonialsPreview() {
  const testimonials = useQuery(api.content.getTestimonials, {
    page: 1,
    limit: DISPLAY_COUNT * 2
  });

  const isLoading = testimonials === undefined;

  const previewItems = useMemo(() => {
    if (!testimonials?.data) return [];
    return testimonials.data.slice(0, DISPLAY_COUNT);
  }, [testimonials?.data]);

  return (
    <section className="content-container py-16 sm:py-20">
      <div className="glass-effect card-hover rounded-3xl border border-border/30 px-6 py-12 sm:px-10 lg:px-16">
        <div className="flex flex-col gap-6 text-center">
          <span className="mx-auto rounded-full border border-border/40 bg-muted/20 px-4 py-2 text-xs font-semibold uppercase tracking-[0.38em] text-muted-foreground/80">
            Testimonials
          </span>
          <h2 className="text-3xl sm:text-4xl font-bold leading-tight text-foreground/95">
            Trusted by founders, funds, and launch teams worldwide
          </h2>
          <p className="mx-auto max-w-3xl text-base text-muted-foreground">
            Real stories from teams who scaled faster with IBC's media engine and growth playbooks.
          </p>
        </div>

        <div className="mt-12 grid gap-6 md:grid-cols-2 xl:grid-cols-4">
          {isLoading
            ? Array.from({ length: DISPLAY_COUNT }).map((_, index) => (
              <Card key={index} className="h-full border border-border/30 bg-background/70 p-6">
                <div className="flex h-full flex-col gap-4">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-6 w-48" />
                  <Skeleton className="h-24 w-full" />
                  <div className="mt-auto flex justify-between">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                </div>
              </Card>
            ))
            : previewItems.length > 0
              ? previewItems.map((testimonial) => (
                <TestimonialPreviewCard key={testimonial._id} testimonial={testimonial} />
              ))
              : (
                <Card className="col-span-full border border-dashed border-border/50 bg-background/70 p-10 text-center">
                  <h3 className="text-lg font-semibold text-foreground/90">Testimonials coming soon</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    Publish your first testimonial from the admin panel to highlight customer wins here.
                  </p>
                </Card>
              )}
        </div>

        <div className="mt-10 flex flex-col items-center justify-between gap-4 sm:flex-row">
          <p className="text-sm text-muted-foreground">
            Showing {previewItems.length || (isLoading ? DISPLAY_COUNT : 0)} of {testimonials?.total ?? 0} testimonials
          </p>
          <div className="flex flex-wrap items-center gap-3">
            <Link href="/testimonials">
              <Button size="lg" className="flex items-center gap-2">
                View all testimonials
                <ArrowRight className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
