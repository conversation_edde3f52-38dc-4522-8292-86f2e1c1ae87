"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@/../../../convex/_generated/api";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowRight, BarChart3, Radio, Twitter } from "lucide-react";

const INITIAL_VISIBLE = 5;
const PAGE_SIZE = 5;

function resolveTypeBadge(type?: string) {
  if (!type) return { label: "Twitter", icon: <Twitter className="h-3.5 w-3.5" /> };
  const normalized = type.toLowerCase();
  if (normalized.includes("space")) {
    return { label: "Space", icon: <Radio className="h-3.5 w-3.5" /> };
  }
  if (normalized.includes("thread")) {
    return { label: "Thread", icon: <BarChart3 className="h-3.5 w-3.5" /> };
  }
  return { label: "Tweet", icon: <Twitter className="h-3.5 w-3.5" /> };
}

function formatNumber(value?: number) {
  if (value == null) return "—";
  if (value >= 1_000_000) return `${(value / 1_000_000).toFixed(1)}M`;
  if (value >= 1_000) return `${Math.round(value / 1_000)}K`;
  return value.toString();
}

function formatDate(timestamp?: number) {
  if (!timestamp) return "";
  return new Date(timestamp).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric"
  });
}

export function TwitterHighlights() {
  const [page, setPage] = useState(1);
  const [items, setItems] = useState<any[]>([]);

  const twitterContent = useQuery(api.content.getAllTwitterContent, {
    page,
    limit: PAGE_SIZE,
  });

  const isLoading = twitterContent === undefined;
  const total = twitterContent?.total ?? 0;

  useEffect(() => {
    if (!twitterContent?.data) return;

    setItems((prev) => {
      if (page === 1) {
        return twitterContent.data;
      }

      const seen = new Set(prev.map((item) => item._id));
      const merged = [...prev];
      twitterContent.data.forEach((item: any) => {
        if (!seen.has(item._id)) {
          merged.push(item);
        }
      });
      return merged;
    });
  }, [page, twitterContent?.data]);

  const showLoadMore = total > items.length;

  const handleLoadMore = () => {
    setPage((prev) => prev + 1);
  };

  return (
    <section className="content-container py-16 sm:py-20">
      <div className="glass-effect card-hover rounded-3xl border border-border/30 px-6 py-12 sm:px-10 lg:px-16">
        <div className="flex flex-col gap-6 text-center">
          <span className="mx-auto rounded-full border border-border/40 bg-muted/20 px-4 py-2 text-xs font-semibold uppercase tracking-[0.38em] text-muted-foreground/80">
            Twitter & Spaces
          </span>
          <h2 className="text-3xl sm:text-4xl font-bold leading-tight text-foreground/95">
            Follow the hottest tweets & spaces
          </h2>
          <p className="mx-auto max-w-3xl text-base text-muted-foreground">
            Five fresh drops from our media network—mixing live spaces, threads, and viral moments. Tap in and load more whenever you want a deeper scroll.
          </p>
        </div>

        <div className="mt-12 grid gap-6 lg:grid-cols-2">
          {isLoading && items.length === 0
            ? Array.from({ length: INITIAL_VISIBLE }).map((_, index) => (
              <Card key={index} className="border border-border/30 bg-background/70 p-6 animate-pulse">
                <div className="flex h-full flex-col gap-4">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-20 w-full" />
                  <div className="mt-auto flex justify-between">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-10 w-28" />
                  </div>
                </div>
              </Card>
            ))
            : items.map((content, index) => {
              const badge = resolveTypeBadge(content.twitter_content_type);
              const impressions = formatNumber(content.twitter_impressions);
              const dateLabel = formatDate(content.content_created_date);

              return (
                <Card
                  key={content._id ?? index}
                  className="group relative flex h-full flex-col justify-between gap-5 overflow-hidden rounded-3xl border border-border/35 bg-gradient-to-br from-background/90 via-background/80 to-background/60 p-6 transition-all duration-500 ease-[cubic-bezier(0.16,1,0.3,1)] hover:-translate-y-2 hover:border-primary/40 hover:shadow-[0_22px_45px_rgba(15,15,45,0.18)]"
                >
                  <div className="pointer-events-none absolute inset-0 opacity-0 transition-opacity duration-500 group-hover:opacity-100" style={{ background: "radial-gradient(circle at top, rgba(34,197,94,0.15), transparent 65%)" }} />

                  <div className="relative space-y-4">
                    <div className="flex flex-wrap items-center gap-3 text-sm text-muted-foreground">
                      {content.content_account?.[0] && (
                        <span className="font-semibold text-foreground/90">@{content.content_account[0]}</span>
                      )}
                      <Badge variant="secondary" className="flex items-center gap-1 rounded-full border border-border/40 bg-background/70 px-3 py-1 text-xs uppercase tracking-wide">
                        {badge.icon}
                        {badge.label}
                      </Badge>
                      {dateLabel && <span className="text-xs text-muted-foreground/70">{dateLabel}</span>}
                    </div>

                    <h3 className="text-xl font-semibold leading-snug text-foreground/95 transition-colors duration-500 group-hover:text-primary">
                      {content.content_title || "Untitled broadcast"}
                    </h3>

                    {content.content_description && (
                      <p className="line-clamp-3 text-sm text-muted-foreground/90">
                        {content.content_description}
                      </p>
                    )}
                  </div>

                  <div className="relative flex items-center justify-between">
                    <div className="flex flex-col text-xs uppercase tracking-[0.28em] text-muted-foreground/80">
                      <span>Impressions</span>
                      <span className="text-2xl font-bold tracking-normal text-primary">
                        {impressions}
                      </span>
                    </div>
                    {content.content_link && (
                      <Link
                        href={content.content_link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center gap-2 text-sm font-semibold text-primary transition-all duration-300 hover:gap-3"
                      >
                        View on X
                        <ArrowRight className="h-4 w-4" />
                      </Link>
                    )}
                  </div>
                </Card>
              );
            })}
        </div>

        <div className="mt-10 flex flex-col items-center justify-between gap-4 sm:flex-row">
          <p className="text-sm text-muted-foreground">
            Showing {items.length} of {total} highlights
          </p>
          <div className="flex flex-wrap items-center gap-3">
            {showLoadMore && (
              <Button
                variant="outline"
                size="lg"
                className="relative overflow-hidden rounded-full border-border/30 bg-background/80 px-6 py-2 font-semibold transition-all duration-300 hover:-translate-y-1 hover:border-primary/50 hover:text-primary"
                onClick={handleLoadMore}
              >
                <span className="relative">Load more</span>
              </Button>
            )}
            <Link href="/twitter">
              <Button size="lg" className="flex items-center gap-2 transition-all duration-300 hover:gap-3">
                View all content
                <ArrowRight className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
