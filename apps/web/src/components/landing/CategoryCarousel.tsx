"use client";

import Link from "next/link";
import { useCallback } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { ArrowLeft, ArrowRight, Rocket } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

const CATEGORIES = [
  {
    title: "Twitter Spaces",
    description: "Live shows, AMAs, and breaking news broadcasts reaching millions weekly.",
    href: "/twitter",
    accent: "bg-chart-1/15 text-chart-1",
  },
  {
    title: "Marketing Case Studies",
    description: "Interactive growth reports from portfolio launches across Telegram, Discord, and X.",
    href: "/marketing",
    accent: "bg-chart-2/15 text-chart-2",
  },
  {
    title: "Press Releases",
    description: "Media-ready kits and coverage to fuel partnership and investor outreach.",
    href: "/press",
    accent: "bg-chart-3/15 text-chart-3",
  },
  {
    title: "Testimonials",
    description: "Founder and partner proof points documenting scaled communities and deal velocity.",
    href: "/testimonials",
    accent: "bg-primary/15 text-primary",
  },
];

export function CategoryCarousel() {
  const [emblaRef, emblaApi] = useEmblaCarousel({ align: "start", loop: true, skipSnaps: false });

  const scrollPrev = useCallback(() => {
    emblaApi?.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    emblaApi?.scrollNext();
  }, [emblaApi]);

  return (
    <section className="content-container py-16 sm:py-20">
      <div className="glass-effect rounded-3xl border border-border/30 px-6 py-10 sm:px-10 sm:py-14">
        <div className="flex flex-col gap-6 text-center">
          <span className="mx-auto inline-flex items-center gap-2 rounded-full border border-border/40 bg-muted/20 px-4 py-2 text-xs font-semibold uppercase tracking-[0.32em] text-muted-foreground/80">
            <Rocket className="h-4 w-4" />
            Explore More
          </span>
          <h2 className="text-3xl sm:text-4xl font-bold leading-tight text-foreground/95">
            Choose your growth lane
          </h2>
          <p className="mx-auto max-w-3xl text-base text-muted-foreground">
            Jump into curated views of the IBC ecosystem - from the biggest daily spaces to marketing benchmarks and press coverage.
          </p>
        </div>

        <div className="relative mt-12">
          <div className="overflow-hidden" ref={emblaRef}>
            <div className="flex gap-6">
              {CATEGORIES.map((category) => (
                <Card
                  key={category.title}
                  className="min-w-[260px] flex-1 basis-full sm:basis-[calc(50%_-_18px)] lg:basis-[calc(33%_-_16px)] xl:basis-[calc(25%_-_18px)] border border-border/40 bg-background/85 p-6 backdrop-blur"
                >
                  <div className={`inline-flex items-center rounded-full px-3 py-1 text-xs font-semibold uppercase tracking-[0.28em] ${category.accent}`}>
                    {category.title}
                  </div>
                  <p className="mt-4 text-sm text-muted-foreground leading-relaxed">
                    {category.description}
                  </p>
                  <div className="mt-6">
                    <Link href={category.href}>
                      <Button variant="link" className="px-0 text-base font-semibold">
                        View {category.title.toLowerCase()}
                      </Button>
                    </Link>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          <div className="pointer-events-none absolute inset-y-0 left-0 w-20 bg-gradient-to-r from-background to-transparent" aria-hidden />
          <div className="pointer-events-none absolute inset-y-0 right-0 w-20 bg-gradient-to-l from-background to-transparent" aria-hidden />

          <div className="mt-8 flex items-center justify-center gap-4">
            <Button variant="outline" size="icon" className="rounded-full" onClick={scrollPrev} aria-label="Previous">
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="rounded-full" onClick={scrollNext} aria-label="Next">
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
