"use client";

import { ConvexReactClient } from "convex/react";
import { ConvexProviderWithClerk } from "convex/react-clerk";
import { Clerk<PERSON>rov<PERSON>, useAuth } from "@clerk/nextjs";
import { Toaster } from "./ui/sonner";
import { AuthProvider } from "./auth/auth-provider";
import { ClerkErrorBoundary } from "./auth/clerk-error-boundary";

const convexUrl = process.env.NEXT_PUBLIC_CONVEX_URL;
const clerkPublishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;

if (!convexUrl) {
  throw new Error(
    "Missing NEXT_PUBLIC_CONVEX_URL environment variable. " +
    "Please add it to your .env.local file."
  );
}

if (!clerkPublishableKey) {
  throw new Error(
    "Missing NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY environment variable. " +
    "Please add it to your .env.local file."
  );
}

const convex = new ConvexReactClient(convexUrl);

const signInUrl = process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || "/sign-in";
const signUpUrl = process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || "/sign-up";
const fallbackRedirectUrl = process.env.NEXT_PUBLIC_CLERK_FALLBACK_REDIRECT_URL || "/dashboard";

// Debug logging for development
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 [CLERK DEBUG] Configuration:', {
    publishableKey: clerkPublishableKey ? `${clerkPublishableKey.substring(0, 15)}...` : 'MISSING',
    convexUrl: convexUrl ? `${convexUrl.substring(0, 20)}...` : 'MISSING',
    currentUrl: typeof window !== 'undefined' ? window.location.href : 'SSR',
    signInUrl,
    signUpUrl,
    fallbackRedirectUrl,
  });
}

export default function Providers({
  children
}: {
  children: React.ReactNode
}) {
  return (
    <ClerkErrorBoundary>
      <ClerkProvider
        publishableKey={clerkPublishableKey}
        appearance={{
          baseTheme: undefined,
          variables: {
            colorPrimary: "#10b981", // Emerald green for IBC branding
          }
        }}
        signInUrl={signInUrl}
        signUpUrl={signUpUrl}
        signInFallbackRedirectUrl={fallbackRedirectUrl}
      >
        <ConvexProviderWithClerk client={convex} useAuth={useAuth}>
          <AuthProvider>
            {children}
            <Toaster richColors />
          </AuthProvider>
        </ConvexProviderWithClerk>
      </ClerkProvider>
    </ClerkErrorBoundary>
  );
}
