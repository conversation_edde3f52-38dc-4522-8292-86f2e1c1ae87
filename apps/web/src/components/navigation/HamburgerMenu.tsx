"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useQuery } from "convex/react";
import { api } from "@/../../../convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import { Portal } from "@/components/ui/portal";
import { AuthButton } from "@/components/auth/auth-button";
import { useUser, useClerk } from "@clerk/nextjs";
import Image from "next/image";
import { toast } from "sonner";
import { LogOut, MoreVertical, LogIn } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Menu,
  X,
  Twitter,
  TrendingUp,
  FileText,
  Heart,
  BarChart3,
  Folder,
  Shield
} from "lucide-react";

interface HamburgerMenuProps {
  className?: string;
}

export function HamburgerMenu({ className = "" }: HamburgerMenuProps) {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();
  const user = useQuery(api.auth.getCurrentUser, {});
  const userProfile = useQuery(api.auth.getUserProfile, {});
  const { user: clerkUser, isLoaded } = useUser();
  const { signOut, openSignIn } = useClerk();

  const handleSignOut = () => {
    signOut();
    toast.success("Successfully signed out!");
  };

  const isAdmin = userProfile?.role === 'admin';
  const isLandingPage = pathname === '/';
  const isDashboardPage = pathname === '/dashboard';

  // Close menu when clicking outside or pressing escape
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') setIsOpen(false);
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
      return () => {
        document.removeEventListener('keydown', handleEscape);
        document.body.style.overflow = '';
      };
    }
  }, [isOpen]);

  // Close menu when route changes
  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  const navigationLinks = [
    { href: "/twitter", label: "Twitter", icon: <Twitter className="h-5 w-5" /> },
    { href: "/marketing", label: "Marketing", icon: <TrendingUp className="h-5 w-5" /> },
    { href: "/press", label: "Press", icon: <FileText className="h-5 w-5" /> },
    { href: "/testimonials", label: "Testimonials", icon: <Heart className="h-5 w-5" /> },
  ];

  const conditionalLinks = [];

  // Add dashboard link if not on landing page
  if (!isLandingPage) {
    conditionalLinks.push({
      href: "/dashboard",
      label: "Dashboard",
      icon: <BarChart3 className="h-5 w-5" />
    });
  }

  // Add groups link if user exists and not on dashboard
  if (user && !isDashboardPage) {
    conditionalLinks.push({
      href: "/groups",
      label: "Groups",
      icon: <Folder className="h-5 w-5" />
    });
  }

  // Add admin link if user is admin
  if (isAdmin) {
    conditionalLinks.push({
      href: "/admin",
      label: "Admin",
      icon: <Shield className="h-5 w-5" />
    });
  }

  const allLinks = [...navigationLinks, ...conditionalLinks];

  return (
    <>
      {/* Hamburger Button */}
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setIsOpen(!isOpen)}
        className={`relative z-[1000] transition-all duration-300 hover:bg-background/20 ${className}`}
        aria-label="Toggle navigation menu"
      >
        <div className="relative w-6 h-6">
          {/* Animated hamburger icon */}
          <span
            className={`absolute block w-6 h-0.5 bg-current transition-all duration-300 ease-in-out ${
              isOpen ? 'rotate-45 top-3' : 'top-1'
            }`}
          />
          <span
            className={`absolute block w-6 h-0.5 bg-current transition-all duration-300 ease-in-out top-3 ${
              isOpen ? 'opacity-0' : 'opacity-100'
            }`}
          />
          <span
            className={`absolute block w-6 h-0.5 bg-current transition-all duration-300 ease-in-out ${
              isOpen ? '-rotate-45 top-3' : 'top-5'
            }`}
          />
        </div>
      </Button>

      {/* Menu Overlay */}
      {isOpen && (
        <Portal>
          <div className="fixed inset-0 z-[2147483640] flex">
            {/* Backdrop */}
            <div
              className="flex-1 bg-black/50 backdrop-blur-sm"
              onClick={() => setIsOpen(false)}
            />

            {/* Menu Panel */}
            <div
              className={`w-80 bg-background/95 backdrop-blur-xl border-l border-border/30 shadow-2xl transform transition-transform duration-300 ease-in-out ${
                isOpen ? 'translate-x-0' : 'translate-x-full'
              }`}
            >
              <div className="flex flex-col h-full">
                {/* Header with Profile */}
                <div className="p-6 border-b border-border/30">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-lg font-semibold text-foreground/95">Navigation</h2>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setIsOpen(false)}
                      className="text-muted-foreground hover:text-foreground"
                    >
                      <X className="h-5 w-5" />
                    </Button>
                  </div>

                  {/* Profile Section - Only show when authenticated */}
                  {isLoaded && clerkUser && (
                    <div className="p-4 rounded-xl bg-muted/20 border border-border/30">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <button className="flex items-center gap-3 w-full text-left hover:bg-muted/30 rounded-lg p-2 transition-colors cursor-pointer">
                            {clerkUser.imageUrl ? (
                              <Image
                                src={clerkUser.imageUrl}
                                alt={clerkUser.firstName || 'Profile'}
                                width={48}
                                height={48}
                                className="w-12 h-12 object-cover rounded-full ring-2 ring-primary/20"
                              />
                            ) : (
                              <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                                <span className="text-lg font-semibold text-primary">
                                  {clerkUser.firstName?.[0] || clerkUser.emailAddresses[0]?.emailAddress[0] || '?'}
                                </span>
                              </div>
                            )}
                            <div className="flex flex-col min-w-0 flex-1">
                              <span className="font-semibold text-foreground/95 truncate">
                                {clerkUser.firstName && clerkUser.lastName
                                  ? `${clerkUser.firstName} ${clerkUser.lastName}`
                                  : clerkUser.firstName || clerkUser.lastName || 'User'
                                }
                              </span>
                              <span className="text-sm text-muted-foreground truncate">
                                {clerkUser.emailAddresses[0]?.emailAddress}
                              </span>
                            </div>
                          </button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="z-[2147483650]">
                          <DropdownMenuItem disabled>
                            <span className="text-sm">{clerkUser.emailAddresses[0]?.emailAddress}</span>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={handleSignOut}>
                            <LogOut className="h-4 w-4 mr-2" />
                            Sign Out
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  )}
                </div>

                {/* Navigation Links */}
                <nav className="flex-1 p-6">
                  <div className="space-y-2">
                    {allLinks.map((link, index) => (
                      <Link
                        key={link.href}
                        href={link.href}
                        className={`group flex items-center gap-4 p-3 rounded-xl transition-all duration-200 hover:bg-primary/10 hover:text-primary ${
                          pathname === link.href ? 'bg-primary/15 text-primary' : 'text-foreground/80'
                        }`}
                        style={{
                          animationDelay: `${index * 50}ms`,
                          animation: isOpen ? 'slideInRight 0.3s ease-out forwards' : 'none'
                        }}
                      >
                        <div className="transition-transform duration-200 group-hover:scale-110">
                          {link.icon}
                        </div>
                        <span className="font-medium">{link.label}</span>
                        {pathname === link.href && (
                          <div className="ml-auto w-2 h-2 bg-primary rounded-full" />
                        )}
                      </Link>
                    ))}
                  </div>
                </nav>
              </div>
            </div>
          </div>
        </Portal>
      )}

      {/* Keyframes for animations */}
      <style jsx>{`
        @keyframes slideInRight {
          from {
            opacity: 0;
            transform: translateX(20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
      `}</style>
    </>
  );
}