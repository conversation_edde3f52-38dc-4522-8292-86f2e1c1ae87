"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { useUser } from "@clerk/nextjs";
import { api } from "@/../../../convex/_generated/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export function AuthDebugPanel() {
  const [isOpen, setIsOpen] = useState(false);
  const { user: clerkUser, isLoaded } = useUser();
  
  const getCurrentUserQuery = useQuery(api.auth.getCurrentUser, {});
  const getUserProfileQuery = useQuery(api.auth.getUserProfile, {});

  const checkClientSession = () => {
    // For Clerk, we just display the current user state
    console.log("Clerk user state:", {
      user: clerkUser,
      isLoaded
    });
  };

  if (!isOpen) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button 
          onClick={() => setIsOpen(true)}
          variant="outline"
          size="sm"
          className="bg-yellow-100 border-yellow-300 text-yellow-800 hover:bg-yellow-200"
        >
          🐛 Debug Auth
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-96 overflow-y-auto">
      <Card className="bg-yellow-50 border-yellow-300">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm text-yellow-800">🐛 Auth Debug Panel</CardTitle>
            <Button 
              onClick={() => setIsOpen(false)}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
            >
              ✕
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3 text-xs">
          {/* Clerk User State */}
          <div>
            <h4 className="font-semibold text-yellow-800">Clerk User (Client)</h4>
            <div className="bg-white p-2 rounded border">
              <Badge variant={clerkUser ? "default" : "destructive"}>
                {clerkUser ? "✅ User Found" : "❌ No User"}
              </Badge>
              {clerkUser && (
                <div className="mt-1">
                  <div>Email: {clerkUser.emailAddresses[0]?.emailAddress}</div>
                  <div>ID: {clerkUser.id}</div>
                  <div>Full Name: {clerkUser.fullName}</div>
                </div>
              )}
              <div>Loaded: {isLoaded ? "Yes" : "No"}</div>
            </div>
          </div>

          {/* Convex getCurrentUser */}
          <div>
            <h4 className="font-semibold text-yellow-800">Convex getCurrentUser</h4>
            <div className="bg-white p-2 rounded border">
              <Badge variant={getCurrentUserQuery ? "default" : "destructive"}>
                {getCurrentUserQuery ? "✅ User Found" : "❌ No User"}
              </Badge>
              {getCurrentUserQuery && (
                <div className="mt-1">
                  <div>Email: {getCurrentUserQuery.email}</div>
                  <div>ID: {getCurrentUserQuery._id}</div>
                  <div>User ID: {getCurrentUserQuery.user_id}</div>
                </div>
              )}
              <div>Loading: {getCurrentUserQuery === undefined ? "Yes" : "No"}</div>
            </div>
          </div>

          {/* Convex getUserProfile */}
          <div>
            <h4 className="font-semibold text-yellow-800">Convex getUserProfile</h4>
            <div className="bg-white p-2 rounded border">
              <Badge variant={getUserProfileQuery ? "default" : "destructive"}>
                {getUserProfileQuery ? "✅ Profile Found" : "❌ No Profile"}
              </Badge>
              {getUserProfileQuery && (
                <div className="mt-1">
                  <div>Email: {getUserProfileQuery.email}</div>
                  <div>Role: <Badge variant={getUserProfileQuery.role === 'admin' ? 'default' : 'secondary'}>{getUserProfileQuery.role}</Badge></div>
                  <div>Name: {getUserProfileQuery.full_name}</div>
                </div>
              )}
              <div>Loading: {getUserProfileQuery === undefined ? "Yes" : "No"}</div>
            </div>
          </div>

          {/* Client Session Check */}
          <div>
            <div className="flex items-center justify-between">
              <h4 className="font-semibold text-yellow-800">Clerk Session</h4>
              <Button onClick={checkClientSession} size="sm" variant="outline" className="h-6 text-xs">
                Log State
              </Button>
            </div>
            <div className="bg-white p-2 rounded border">
              <div>User ID: {clerkUser?.id || "None"}</div>
              <div>Email: {clerkUser?.emailAddresses[0]?.emailAddress || "None"}</div>
              <div>Is Loaded: {isLoaded ? "✅ Yes" : "❌ No"}</div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            <Button 
              onClick={() => window.location.reload()}
              size="sm" 
              variant="outline"
              className="text-xs"
            >
              Reload Page
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
