"use client";

import { use<PERSON><PERSON>back, use<PERSON>em<PERSON>, useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { toast } from "sonner";
import { format } from "date-fns";
import { Plus, X, Copy, Trash2, <PERSON>rkles, UploadCloud, Gauge, CalendarDays, CalendarIcon } from "lucide-react";

import { api } from "@/../../../convex/_generated/api";
import type { Id } from "@/../../../convex/_generated/dataModel";
import type { ChartSeriesVariant, MarketingCaseStudy } from "@/types/marketing";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Skeleton } from "@/components/ui/skeleton";
import { Checkbox } from "@/components/ui/checkbox";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { CaseStudyChartCard } from "@/components/marketing/CaseStudyChartCard";

type ChartPointFormState = {
  date: string;
  value: string;
};

type ChartSeriesFormState = {
  id: string;
  label: string;
  color_token: string;
  stroke_color: string;
  variant: ChartSeriesVariant;
  fill_opacity: string;
  points: ChartPointFormState[];
};

type CaseStudyFormState = {
  slug: string;
  title: string;
  summary: string;
  cta_label: string;
  cta_url: string;
  display_order: string;
  tags: string[];
  is_featured: boolean;
  is_published: boolean;
  chart_series: ChartSeriesFormState[];
};

const DEFAULT_LABELS = ["Telegram Growth", "Discord Growth", "X Growth"];
const DEFAULT_CASE_STUDY_BASE_URL = "https://ibc.ventures/case-studies";

const createInitialSeries = (count = 2): ChartSeriesFormState[] => {
  const today = new Date();
  const makeDate = (offset: number) => {
    const d = new Date(today);
    d.setDate(d.getDate() - offset);
    return d.toISOString().slice(0, 10);
  };

  return Array.from({ length: count }).map((_, index) => ({
    id: `series-${index + 1}`,
    label: DEFAULT_LABELS[index] ?? `Series ${index + 1}`,
    color_token: `chart-${(index % 5) + 1}`,
    stroke_color: "",
    variant: "line",
    fill_opacity: "0.3",
    points: [
      { date: makeDate(21), value: "120" },
      { date: makeDate(14), value: "180" },
      { date: makeDate(7), value: "240" },
      { date: makeDate(0), value: "310" },
    ],
  }));
};

const createInitialFormState = (): CaseStudyFormState => ({
  slug: "",
  title: "",
  summary: "",
  cta_label: "View Case Study",
  cta_url: "",
  display_order: "0",
  tags: [],
  is_featured: false,
  is_published: true,
  chart_series: createInitialSeries(),
});

export function CaseStudyManagementTab() {
  const [showForm, setShowForm] = useState(false);
  const [formState, setFormState] = useState<CaseStudyFormState>(() => createInitialFormState());
  const [editingId, setEditingId] = useState<Id<"marketing_case_studies"> | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [slugDirty, setSlugDirty] = useState(false);
  const [ctaUrlDirty, setCtaUrlDirty] = useState(false);
  const [csvDrafts, setCsvDrafts] = useState<Record<string, string>>({});
  const [csvHeaders, setCsvHeaders] = useState<Record<string, string[]>>({});
  const [selectedCsvHeaders, setSelectedCsvHeaders] = useState<Record<string, string | null>>({});

  const caseStudiesQuery = useQuery(api.marketingCaseStudies.listCaseStudies, {
    page: 1,
    limit: 20,
    includeUnpublished: true,
  });
  const metadataQuery = useQuery(api.admin.getMetadataOptions, {});

  const createMutation = useMutation(api.marketingCaseStudies.createCaseStudy);
  const updateMutation = useMutation(api.marketingCaseStudies.updateCaseStudy);
  const deleteMutation = useMutation(api.marketingCaseStudies.deleteCaseStudy);

  const categoryOptions = useMemo(() => metadataQuery?.content_categories ?? [], [metadataQuery]);
  const metadataLoading = metadataQuery === undefined;

  const resetForm = useCallback(() => {
    setFormState(createInitialFormState());
    setSlugDirty(false);
    setCtaUrlDirty(false);
    setCsvDrafts({});
    setCsvHeaders({});
    setSelectedCsvHeaders({});
    setEditingId(null);
  }, []);

  const handleToggleForm = () => {
    if (showForm) {
      resetForm();
    }
    setShowForm((prev) => !prev);
  };

  const handleSeriesChange = <K extends keyof ChartSeriesFormState>(
    index: number,
    key: K,
    value: ChartSeriesFormState[K],
  ) => {
    setFormState((prev) => {
      const nextSeries = [...prev.chart_series];
      nextSeries[index] = { ...nextSeries[index], [key]: value } as ChartSeriesFormState;
      return { ...prev, chart_series: nextSeries };
    });
  };

  const handlePointChange = (seriesIndex: number, pointIndex: number, key: keyof ChartPointFormState, value: string) => {
    setFormState((prev) => {
      const nextSeries = [...prev.chart_series];
      const nextPoints = [...nextSeries[seriesIndex].points];
      nextPoints[pointIndex] = { ...nextPoints[pointIndex], [key]: value };
      nextSeries[seriesIndex] = { ...nextSeries[seriesIndex], points: nextPoints };
      return { ...prev, chart_series: nextSeries };
    });
  };

  const handleAddSeries = () => {
    setFormState((prev) => ({
      ...prev,
      chart_series: [...prev.chart_series, createInitialSeries(1)[0]],
    }));
  };

  const handleRemoveSeries = (index: number) => {
    setFormState((prev) => ({
      ...prev,
      chart_series: prev.chart_series.filter((_, i) => i !== index),
    }));
    setCsvDrafts((prev) => {
      const next: Record<string, string> = {};
      Object.entries(prev).forEach(([key, value]) => {
        const numericKey = Number(key);
        if (Number.isNaN(numericKey)) return;
        if (numericKey < index) {
          next[key] = value;
        } else if (numericKey > index) {
          next[(numericKey - 1).toString()] = value;
        }
      });
      return next;
    });
    setCsvHeaders((prev) => {
      const next: Record<string, string[]> = {};
      Object.entries(prev).forEach(([key, value]) => {
        const numericKey = Number(key);
        if (Number.isNaN(numericKey)) return;
        if (numericKey < index) {
          next[key] = value;
        } else if (numericKey > index) {
          next[(numericKey - 1).toString()] = value;
        }
      });
      return next;
    });
    setSelectedCsvHeaders((prev) => {
      const next: Record<string, string | null> = {};
      Object.entries(prev).forEach(([key, value]) => {
        const numericKey = Number(key);
        if (Number.isNaN(numericKey)) return;
        if (numericKey < index) {
          next[key] = value;
        } else if (numericKey > index) {
          next[(numericKey - 1).toString()] = value;
        }
      });
      return next;
    });
  };

  const handleAddPoint = (seriesIndex: number) => {
    setFormState((prev) => {
      const nextSeries = [...prev.chart_series];
      const lastDate = nextSeries[seriesIndex].points.at(-1)?.date ?? new Date().toISOString().slice(0, 10);
      const nextDate = shiftDate(lastDate, 7);
      nextSeries[seriesIndex] = {
        ...nextSeries[seriesIndex],
        points: [...nextSeries[seriesIndex].points, { date: nextDate, value: "0" }],
      };
      return { ...prev, chart_series: nextSeries };
    });
  };

  const handleRemovePoint = (seriesIndex: number, pointIndex: number) => {
    setFormState((prev) => {
      const nextSeries = [...prev.chart_series];
      const points = nextSeries[seriesIndex].points.filter((_, idx) => idx !== pointIndex);
      nextSeries[seriesIndex] = { ...nextSeries[seriesIndex], points };
      return { ...prev, chart_series: nextSeries };
    });
  };

  const handleTitleChange = (value: string) => {
    setFormState((prev) => {
      const nextTitle = value;
      const nextSlug = slugDirty ? prev.slug : slugify(nextTitle);
      const nextCtaUrl = ctaUrlDirty ? prev.cta_url : buildCaseStudyUrl(nextSlug, prev.cta_url);

      return {
        ...prev,
        title: nextTitle,
        slug: nextSlug,
        cta_url: nextCtaUrl,
      };
    });
  };

  const handleSlugChange = (value: string) => {
    setSlugDirty(true);
    const sanitized = slugify(value);
    setFormState((prev) => ({
      ...prev,
      slug: sanitized,
      cta_url: ctaUrlDirty ? prev.cta_url : buildCaseStudyUrl(sanitized, prev.cta_url),
    }));
  };

  const handleCtaUrlChange = (value: string) => {
    setCtaUrlDirty(true);
    setFormState((prev) => ({
      ...prev,
      cta_url: value,
    }));
  };

  const toggleTag = (tag: string, checked: boolean) => {
    setFormState((prev) => {
      const nextTags = new Set(prev.tags.map((entry) => entry.toLowerCase()));
      if (checked) {
        nextTags.add(tag.toLowerCase());
      } else {
        nextTags.delete(tag.toLowerCase());
      }
      return { ...prev, tags: Array.from(nextTags) };
    });
  };

  const handleCsvDraftChange = (seriesIndex: number, value: string) => {
    const key = seriesIndex.toString();
    setCsvDrafts((prev) => ({ ...prev, [key]: value }));

    if (!value.trim()) {
      setCsvHeaders((prev) => {
        const { [key]: _, ...rest } = prev;
        return rest;
      });
      setSelectedCsvHeaders((prev) => {
        const { [key]: _, ...rest } = prev;
        return rest;
      });
      return;
    }

    const headers = extractCsvHeaders(value);
    setCsvHeaders((prev) => ({ ...prev, [key]: headers }));
    setSelectedCsvHeaders((prev) => {
      const current = prev[key];
      if (current && headers.some((header) => header === current)) {
        return prev;
      }
      return { ...prev, [key]: headers[0] ?? null };
    });
  };

  const handleCsvImport = (seriesIndex: number) => {
    const key = seriesIndex.toString();
    const draft = csvDrafts[key];
    if (!draft || !draft.trim()) {
      toast.error("Paste CSV data before converting.");
      return;
    }

    const availableHeaders = csvHeaders[key] ?? [];
    const preferredHeader = selectedCsvHeaders[key] ?? availableHeaders[0] ?? null;
    if (!preferredHeader) {
      toast.error("Select a metric column before converting.");
      return;
    }

    const { points, errors, headerUsed } = parseCsvPoints(draft, { header: preferredHeader });
    if (errors.length > 0) {
      const [first, ...rest] = errors;
      const message = rest.length > 0 ? `${first} (+${rest.length} more issue${rest.length === 1 ? "" : "s"})` : first;
      console.error("CSV import errors", errors);
      toast.error(message);
      return;
    }

    if (points.length === 0) {
      toast.error("CSV import did not contain any data rows.");
      return;
    }

    setFormState((prev) => {
      const nextSeries = [...prev.chart_series];
      const updatedSeries = { ...nextSeries[seriesIndex], points };
      if (
        headerUsed &&
        (!updatedSeries.label.trim() || /^Series \d+$/i.test(updatedSeries.label.trim()))
      ) {
        updatedSeries.label = headerUsed;
      }
      nextSeries[seriesIndex] = updatedSeries;
      return { ...prev, chart_series: nextSeries };
    });

    setCsvDrafts((prev) => ({ ...prev, [key]: "" }));
    setCsvHeaders((prev) => {
      const { [key]: _, ...rest } = prev;
      return rest;
    });
    setSelectedCsvHeaders((prev) => {
      const { [key]: _, ...rest } = prev;
      return rest;
    });

    toast.success(
      `Imported ${points.length} data point${points.length === 1 ? "" : "s"}${headerUsed ? ` for ${headerUsed}` : ""}`,
    );
  };

  const handleCsvHeaderSelect = (seriesIndex: number, header: string) => {
    setSelectedCsvHeaders((prev) => ({ ...prev, [seriesIndex.toString()]: header }));
  };

  const loadCaseStudyIntoForm = (caseStudy: MarketingCaseStudy) => {
    setFormState({
      slug: caseStudy.slug,
      title: caseStudy.title,
      summary: caseStudy.summary ?? "",
      cta_label: caseStudy.cta_label ?? "",
      cta_url: caseStudy.cta_url,
      display_order: caseStudy.display_order?.toString() ?? "0",
      tags: (caseStudy.tags ?? []).map((tag) => tag.toLowerCase()),
      is_featured: caseStudy.is_featured ?? false,
      is_published: caseStudy.is_published ?? true,
      chart_series: caseStudy.chart_series.map((series) => ({
        id: series.id,
        label: series.label,
        color_token: series.color_token ?? "",
        stroke_color: series.stroke_color ?? "",
        variant: series.variant ?? "line",
        fill_opacity: series.fill_opacity?.toString() ?? "0.3",
        points: series.points.map((point) => ({
          date: normalizeDate(point.date),
          value: point.value.toString(),
        })),
      })),
    });
    setEditingId(caseStudy._id);
    setSlugDirty(false);
    setCtaUrlDirty(false);
    setCsvDrafts({});
    setCsvHeaders({});
    setSelectedCsvHeaders({});
    setShowForm(true);
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!formState.title.trim()) {
      toast.error("Project title is required");
      return;
    }

    if (!formState.slug.trim()) {
      toast.error("Slug is required");
      return;
    }

    if (!formState.cta_url.trim()) {
      toast.error("Case study link is required");
      return;
    }

    const chartSeriesPayload = [];
    for (const series of formState.chart_series) {
      if (!series.id.trim() || !series.label.trim()) {
        toast.error("Each series must have an ID and label");
        return;
      }

      if (!series.points.length) {
        toast.error(`Series ${series.label} must have at least one data point`);
        return;
      }

      const points = [];
      for (const point of series.points) {
        if (!point.date) {
          toast.error("All data points must include a date");
          return;
        }
        if (point.value === "" || Number.isNaN(Number(point.value))) {
          toast.error("All data points must include a numeric value");
          return;
        }

        points.push({
          date: normalizeDate(point.date),
          value: Number(point.value),
        });
      }

      chartSeriesPayload.push({
        id: series.id.trim(),
        label: series.label.trim(),
        color_token: series.color_token.trim() || undefined,
        stroke_color: series.stroke_color.trim() || undefined,
        variant: series.variant,
        fill_opacity: series.fill_opacity ? Number(series.fill_opacity) : undefined,
        points,
      });
    }

    const displayOrder = formState.display_order.trim() ? Number(formState.display_order) : undefined;
    if (displayOrder !== undefined && Number.isNaN(displayOrder)) {
      toast.error("Display order must be a number");
      return;
    }

    const tagsArray = formState.tags.map((tag) => tag.trim()).filter(Boolean);

    setIsSubmitting(true);

    try {
      if (editingId) {
        await updateMutation({
          id: editingId,
          slug: formState.slug.trim(),
          title: formState.title.trim(),
          summary: formState.summary.trim() || undefined,
          cta_label: formState.cta_label.trim() || undefined,
          cta_url: formState.cta_url.trim(),
          chart_series: chartSeriesPayload,
          tags: tagsArray,
          display_order: displayOrder,
          is_featured: formState.is_featured,
          is_published: formState.is_published,
        });
        toast.success("Case study updated successfully");
      } else {
        await createMutation({
          slug: formState.slug.trim(),
          title: formState.title.trim(),
          summary: formState.summary.trim() || undefined,
          cta_label: formState.cta_label.trim() || undefined,
          cta_url: formState.cta_url.trim(),
          chart_series: chartSeriesPayload,
          tags: tagsArray,
          display_order: displayOrder,
          is_featured: formState.is_featured,
          is_published: formState.is_published,
        });
        toast.success("Case study created successfully");
      }

      resetForm();
      setShowForm(false);
    } catch (error) {
      console.error("Failed to save case study", error);
      toast.error(error instanceof Error ? error.message : "Failed to save case study");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (id: Id<"marketing_case_studies">) => {
    try {
      await deleteMutation({ id });
      toast.success("Case study deleted successfully");
      if (editingId === id) {
        resetForm();
        setShowForm(false);
      }
    } catch (error) {
      console.error("Failed to delete case study", error);
      toast.error("Failed to delete case study");
    }
  };

  const handleTogglePublished = async (caseStudy: MarketingCaseStudy) => {
    try {
      const nextPublished = !(caseStudy.is_published ?? true);
      await updateMutation({
        id: caseStudy._id,
        is_published: nextPublished,
      });
      toast.success(`Case study ${nextPublished ? "published" : "unpublished"}`);
    } catch (error) {
      toast.error("Failed to update publish status");
    }
  };

  const handleToggleFeatured = async (caseStudy: MarketingCaseStudy) => {
    try {
      await updateMutation({
        id: caseStudy._id,
        is_featured: !(caseStudy.is_featured ?? false),
      });
      toast.success("Case study feature flag updated");
    } catch (error) {
      toast.error("Failed to update featured status");
    }
  };

  const isLoading = caseStudiesQuery === undefined;

  return (
    <div className="space-y-10">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold">Case Studies Management</h2>
          <p className="text-sm text-muted-foreground">
            Publish, edit, and visualize marketing growth case studies powering the landing page section.
          </p>
        </div>
        <Button onClick={handleToggleForm} className="flex items-center gap-2">
          {showForm ? <X className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
          {showForm ? "Cancel" : "Add Case Study"}
        </Button>
      </div>

      {showForm && (
        <Card className="border border-border/40 bg-background/80 p-6 shadow-lg">
          <form onSubmit={handleSubmit} className="space-y-8">
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="title">Project Title</Label>
                <Input
                  id="title"
                  value={formState.title}
                  onChange={(event) => handleTitleChange(event.target.value)}
                  placeholder="Multibank"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="slug">Slug</Label>
                <Input
                  id="slug"
                  value={formState.slug}
                  onChange={(event) => handleSlugChange(event.target.value)}
                  placeholder="multibank"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="ctaUrl">Case Study URL</Label>
                <Input
                  id="ctaUrl"
                  type="url"
                  value={formState.cta_url}
                  onChange={(event) => handleCtaUrlChange(event.target.value)}
                  placeholder="https://ibc.ventures/case-studies/multibank"
                  required
                />
                <p className="text-xs text-muted-foreground">
                  We prefill this from the project title—override it if you need a custom destination.
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="ctaLabel">CTA Button Label</Label>
                <Input
                  id="ctaLabel"
                  value={formState.cta_label}
                  onChange={(event) => setFormState((prev) => ({ ...prev, cta_label: event.target.value }))}
                  placeholder="View Case Study"
                />
              </div>

              <div className="md:col-span-2 space-y-2">
                <Label htmlFor="summary">Summary</Label>
                <Textarea
                  id="summary"
                  value={formState.summary}
                  onChange={(event) => setFormState((prev) => ({ ...prev, summary: event.target.value }))}
                  placeholder="Short marketing impact snapshot for this launch."
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="displayOrder">Display Order</Label>
                <Input
                  id="displayOrder"
                  value={formState.display_order}
                  onChange={(event) => setFormState((prev) => ({ ...prev, display_order: event.target.value }))}
                  placeholder="0"
                />
                <p className="text-xs text-muted-foreground">Lower numbers appear first on the marketing page.</p>
              </div>
              <div className="md:col-span-2 space-y-3">
                <Label>Tags</Label>
                {metadataLoading ? (
                  <Skeleton className="h-10 w-full rounded-lg" />
                ) : categoryOptions.length > 0 ? (
                  <div className="grid gap-2 sm:grid-cols-2 lg:grid-cols-3">
                    {categoryOptions.map((category) => {
                      const normalized = category.toLowerCase();
                      const isChecked = formState.tags.includes(normalized);
                      return (
                        <label
                          key={category}
                          className="flex items-center gap-2 rounded-xl border border-border/40 bg-background/60 px-3 py-2 text-sm capitalize"
                        >
                          <Checkbox
                            checked={isChecked}
                            onCheckedChange={(checked) => toggleTag(category, checked === true)}
                          />
                          <span className="text-sm capitalize">{category.replace(/-/g, " ")}</span>
                        </label>
                      );
                    })}
                  </div>
                ) : (
                  <div className="rounded-xl border border-dashed border-border/50 bg-muted/10 p-4 text-sm text-muted-foreground">
                    Configure categories from the Metadata tab to enable tagging options.
                  </div>
                )}
                <p className="text-xs text-muted-foreground">Select marketing categories so this entry reuses the shared taxonomy.</p>
              </div>

              <div className="flex items-center justify-between rounded-2xl border border-border/40 bg-muted/10 px-4 py-3">
                <div>
                  <Label className="text-sm font-semibold">Mark as Featured</Label>
                  <p className="text-xs text-muted-foreground">Pinned to the top of the marketing section.</p>
                </div>
                <Switch
                  checked={formState.is_featured}
                  onCheckedChange={(value) => setFormState((prev) => ({ ...prev, is_featured: value }))}
                />
              </div>

              <div className="flex items-center justify-between rounded-2xl border border-border/40 bg-muted/10 px-4 py-3">
                <div>
                  <Label className="text-sm font-semibold">Publish Immediately</Label>
                  <p className="text-xs text-muted-foreground">Uncheck to keep as draft while iterating.</p>
                </div>
                <Switch
                  checked={formState.is_published}
                  onCheckedChange={(value) => setFormState((prev) => ({ ...prev, is_published: value }))}
                />
              </div>
            </div>

            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Gauge className="h-5 w-5 text-ibc-green" />
                  Chart Series
                </h3>
                <Button type="button" variant="outline" size="sm" onClick={handleAddSeries} className="flex items-center gap-2">
                  <Plus className="h-4 w-4" /> Add Series
                </Button>
              </div>

              <div className="space-y-6">
                {formState.chart_series.map((series, seriesIndex) => (
                  <Card key={series.id} className="space-y-4 border border-border/40 bg-background/60 p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="rounded-full bg-ibc-green/10 p-2">
                          <Sparkles className="h-4 w-4 text-ibc-green" />
                        </div>
                        <div>
                          <p className="text-sm font-semibold text-foreground/80">Series {seriesIndex + 1}</p>
                          <p className="text-xs text-muted-foreground">Configure the dataset & styling for this line.</p>
                        </div>
                      </div>
                      {formState.chart_series.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="text-destructive hover:text-destructive"
                          onClick={() => handleRemoveSeries(seriesIndex)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid gap-4 md:grid-cols-2">
                      <div className="space-y-2">
                        <Label>ID</Label>
                        <Input
                          value={series.id}
                          onChange={(event) => handleSeriesChange(seriesIndex, "id", event.target.value)}
                          placeholder="telegram"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Label</Label>
                        <Input
                          value={series.label}
                          onChange={(event) => handleSeriesChange(seriesIndex, "label", event.target.value)}
                          placeholder="Telegram Growth"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Color Token</Label>
                        <Input
                          value={series.color_token}
                          onChange={(event) => handleSeriesChange(seriesIndex, "color_token", event.target.value)}
                          placeholder="chart-1 or #00c729"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Stroke Override</Label>
                        <Input
                          value={series.stroke_color}
                          onChange={(event) => handleSeriesChange(seriesIndex, "stroke_color", event.target.value)}
                          placeholder="Optional stroke color token"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Variant</Label>
                        <select
                          className="w-full rounded-lg border border-border/50 bg-background px-3 py-2 text-sm"
                          value={series.variant}
                          onChange={(event) => handleSeriesChange(seriesIndex, "variant", event.target.value as ChartSeriesVariant)}
                        >
                          <option value="line">Line</option>
                          <option value="area">Area</option>
                        </select>
                      </div>
                      <div className="space-y-2">
                        <Label>Fill Opacity</Label>
                        <Input
                          value={series.fill_opacity}
                          onChange={(event) => handleSeriesChange(seriesIndex, "fill_opacity", event.target.value)}
                          placeholder="0.3"
                        />
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-semibold text-muted-foreground flex items-center gap-2">
                          <CalendarDays className="h-4 w-4" />
                          Data Points
                        </h4>
                        <Button type="button" variant="outline" size="sm" onClick={() => handleAddPoint(seriesIndex)} className="flex items-center gap-1">
                          <Plus className="h-3 w-3" /> Add Point
                        </Button>
                      </div>

                      <div className="space-y-2">
                        {series.points.map((point, pointIndex) => (
                          <div key={`${series.id}-point-${pointIndex}`} className="grid gap-3 rounded-xl border border-border/40 bg-background/70 p-3 md:grid-cols-[minmax(0,1fr)_minmax(0,1fr)_auto]">
                            <div className="space-y-1">
                              <Label className="text-xs uppercase tracking-wide text-muted-foreground">Date</Label>
                          <ChartDatePicker
                            value={point.date}
                            onChange={(nextDate) => handlePointChange(seriesIndex, pointIndex, "date", nextDate)}
                          />
                        </div>
                        <div className="space-y-1">
                          <Label className="text-xs uppercase tracking-wide text-muted-foreground">
                            {series.label || "Value"}
                          </Label>
                          <Input
                            type="number"
                            value={point.value}
                            onChange={(event) => handlePointChange(seriesIndex, pointIndex, "value", event.target.value)}
                          />
                            </div>
                            {series.points.length > 1 && (
                              <Button
                                type="button"
                                variant="ghost"
                                size="icon"
                                className="mt-5 text-destructive"
                                onClick={() => handleRemovePoint(seriesIndex, pointIndex)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>

                      <div className="space-y-2">
                        <Label
                          htmlFor={`series-${seriesIndex}-csv`}
                          className="text-xs uppercase tracking-wide text-muted-foreground"
                        >
                          Import CSV
                        </Label>
                        <Textarea
                          id={`series-${seriesIndex}-csv`}
                          value={csvDrafts[seriesIndex.toString()] ?? ""}
                          onChange={(event) => handleCsvDraftChange(seriesIndex, event.target.value)}
                          placeholder={`date,value\n01-08-2024,120\n08-08-2024,180`}
                          rows={4}
                        />
                        {(csvHeaders[seriesIndex.toString()]?.length ?? 0) > 0 && (
                          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                            <div className="flex items-center gap-2 text-xs uppercase tracking-wide text-muted-foreground">
                              <CalendarIcon className="h-3 w-3" /> Metric Column
                            </div>
                            <Select
                              value={selectedCsvHeaders[seriesIndex.toString()] ?? undefined}
                              onValueChange={(value) => handleCsvHeaderSelect(seriesIndex, value)}
                            >
                              <SelectTrigger className="h-9 w-full sm:w-48">
                                <SelectValue placeholder="Choose metric" />
                              </SelectTrigger>
                              <SelectContent>
                                {csvHeaders[seriesIndex.toString()]?.map((header) => (
                                  <SelectItem key={header} value={header}>
                                    {header}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                        <div className="flex items-center justify-between gap-2">
                          <p className="text-xs text-muted-foreground">
                            Paste a CSV with a Date column followed by your metric columns.
                          </p>
                          <Button type="button" variant="outline" size="sm" onClick={() => handleCsvImport(seriesIndex)}>
                            Convert CSV
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            <div className="flex items-center justify-end gap-4">
              {editingId && (
                <Button type="button" variant="outline" onClick={resetForm} className="flex items-center gap-2">
                  <Copy className="h-4 w-4" />
                  Reset
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting} className="flex items-center gap-2">
                <UploadCloud className="h-4 w-4" />
                {isSubmitting ? "Saving..." : editingId ? "Update Case Study" : "Create Case Study"}
              </Button>
            </div>
          </form>
        </Card>
      )}

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold">Published Case Studies</h3>
          <p className="text-xs text-muted-foreground">
            {caseStudiesQuery?.total ?? 0} total records
          </p>
        </div>

        {isLoading ? (
          <div className="grid gap-6 md:grid-cols-2">
            {Array.from({ length: 2 }).map((_, index) => (
              <Card key={index} className="space-y-4 border border-border/40 bg-background/80 p-6">
                <Skeleton className="h-6 w-40 rounded" />
                <Skeleton className="h-4 w-3/4 rounded" />
                <Skeleton className="h-[200px] w-full rounded-2xl" />
                <div className="flex gap-2">
                  <Skeleton className="h-10 w-28 rounded" />
                  <Skeleton className="h-10 w-28 rounded" />
                </div>
              </Card>
            ))}
          </div>
        ) : !caseStudiesQuery?.data.length ? (
          <div className="rounded-3xl border border-dashed border-border/60 bg-background/60 px-8 py-16 text-center">
            <p className="text-muted-foreground">No case studies yet. Start by creating your first one above.</p>
          </div>
        ) : (
          <div className="grid gap-6 lg:grid-cols-2">
            {caseStudiesQuery.data.map((caseStudy) => (
              <div key={caseStudy._id} className="space-y-4">
                <CaseStudyChartCard caseStudy={caseStudy} />
                <div className="flex flex-wrap items-center gap-3">
                  <Button variant="outline" size="sm" onClick={() => loadCaseStudyIntoForm(caseStudy)} className="flex items-center gap-2">
                    <Copy className="h-4 w-4" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTogglePublished(caseStudy)}
                    className="flex items-center gap-2"
                  >
                    {caseStudy.is_published === false ? "Publish" : "Unpublish"}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleToggleFeatured(caseStudy)}
                    className="flex items-center gap-2"
                  >
                    {caseStudy.is_featured ? "Remove Featured" : "Mark Featured"}
                  </Button>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleDelete(caseStudy._id)}
                    className="flex items-center gap-2"
                  >
                    <Trash2 className="h-4 w-4" />
                    Delete
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

type ChartDatePickerProps = {
  value: string;
  onChange: (value: string) => void;
};

function ChartDatePicker({ value, onChange }: ChartDatePickerProps) {
  const selectedDate = useMemo(() => parseIsoDate(value), [value]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          type="button"
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal",
            !selectedDate && "text-muted-foreground",
          )}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {selectedDate ? format(selectedDate, "PPP") : "Pick a date"}
        </Button>
      </PopoverTrigger>
      <PopoverContent align="start" className="w-auto p-0">
        <Calendar
          mode="single"
          selected={selectedDate ?? undefined}
          onSelect={(date) => {
            if (date) {
              onChange(date.toISOString().slice(0, 10));
            }
          }}
          initialFocus
        />
      </PopoverContent>
    </Popover>
  );
}

function normalizeDate(value: string): string {
  const parsed = Date.parse(value);
  if (!Number.isNaN(parsed)) {
    return new Date(parsed).toISOString().slice(0, 10);
  }
  return value;
}

function shiftDate(dateString: string, days: number) {
  const date = new Date(dateString);
  if (Number.isNaN(date.getTime())) {
    return new Date().toISOString().slice(0, 10);
  }
  date.setDate(date.getDate() + days);
  return date.toISOString().slice(0, 10);
}

function slugify(value: string): string {
  return value
    .trim()
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/^-+|-+$/g, "");
}

function buildCaseStudyUrl(slug: string, currentUrl?: string) {
  const sanitizedSlug = slugify(slug);
  if (!sanitizedSlug) {
    return currentUrl ?? "";
  }

  if (currentUrl) {
    try {
      const parsed = new URL(currentUrl);
      const segments = parsed.pathname.split("/").filter(Boolean);
      if (segments.length > 0) {
        segments.pop();
      }
      const basePath = segments.join("/");
      const baseUrl = `${parsed.origin}${basePath ? `/${basePath}` : ""}`.replace(/\/$/, "");
      return `${baseUrl}/${sanitizedSlug}`;
    } catch (error) {
      console.warn("Failed to derive base from case study URL", error);
    }
  }

  const fallbackBase = DEFAULT_CASE_STUDY_BASE_URL.replace(/\/$/, "");
  return `${fallbackBase}/${sanitizedSlug}`;
}

function parseIsoDate(value: string): Date | undefined {
  if (!value) return undefined;
  const date = new Date(value);
  if (Number.isNaN(date.getTime())) {
    return undefined;
  }
  return date;
}

function parseCsvPoints(
  csv: string,
  options: { header?: string | null } = {},
): { points: ChartPointFormState[]; errors: string[]; headerUsed?: string } {
  const errors: string[] = [];
  const lines = csv
    .split(/\r?\n/)
    .map((line) => line.trim())
    .filter(Boolean);

  if (lines.length === 0) {
    errors.push("CSV is empty.");
    return { points: [], errors };
  }

  const [headerLine, ...rows] = lines;
  const headerCells = headerLine.split(",").map(cleanCsvCell);

  if (headerCells.length <= 1) {
    errors.push("CSV must include at least one metric column after the date column.");
    return { points: [], errors };
  }

  const normalizedHeaders = headerCells.map(normalizeHeaderName);
  let columnIndex = 1;
  let headerUsed: string | undefined;

  if (options.header && options.header.trim()) {
    const target = normalizeHeaderName(options.header);
    columnIndex = normalizedHeaders.findIndex((header, idx) => idx > 0 && header === target);
    if (columnIndex === -1) {
      errors.push(`The column "${options.header}" was not found in the CSV header.`);
      return { points: [], errors };
    }
    headerUsed = headerCells[columnIndex];
  } else {
    columnIndex = headerCells.length > 1 ? 1 : -1;
    headerUsed = headerCells[columnIndex];
  }

  if (columnIndex <= 0) {
    errors.push("Select a valid metric column (cannot use the Date column).");
    return { points: [], errors };
  }

  if (rows.length === 0) {
    errors.push("CSV must include at least one data row below the header.");
    return { points: [], errors };
  }

  const points: ChartPointFormState[] = [];

  rows.forEach((row, index) => {
    const parts = row.split(",").map(cleanCsvCell);

    if (parts.length <= columnIndex) {
      errors.push(`Row ${index + 2}: missing value for ${headerUsed ?? "selected column"}.`);
      return;
    }

    const rawDate = parts[0];
    const isoDate = parseDdMmYyyy(rawDate);
    if (!isoDate) {
      errors.push(`Row ${index + 2}: "${rawDate}" is not a valid dd-mm-yyyy date.`);
      return;
    }

    const rawValue = parts[columnIndex];
    const numericValue = Number(rawValue.replace(/,/g, ""));
    if (!Number.isFinite(numericValue)) {
      errors.push(`Row ${index + 2}: "${rawValue}" is not a valid number.`);
      return;
    }

    points.push({ date: isoDate, value: String(numericValue) });
  });

  points.sort((a, b) => a.date.localeCompare(b.date));

  return { points, errors, headerUsed };
}

function parseDdMmYyyy(value: string): string | null {
  const trimmed = value.trim();
  if (!trimmed) return null;

  const normalized = trimmed.replace(/\./g, "-");

  const match = /^([0-3]\d)[\/-]([0-1]\d)[\/-](\d{4})$/.exec(normalized);
  if (match) {
    const [, dayString, monthString, yearString] = match;
    const day = Number(dayString);
    const month = Number(monthString);
    const year = Number(yearString);

    const date = new Date(Date.UTC(year, month - 1, day));
    if (
      date.getUTCFullYear() !== year ||
      date.getUTCMonth() !== month - 1 ||
      date.getUTCDate() !== day
    ) {
      return null;
    }
    return date.toISOString().slice(0, 10);
  }

  const parsed = Date.parse(trimmed);
  if (!Number.isNaN(parsed)) {
    return new Date(parsed).toISOString().slice(0, 10);
  }

  return null;
}

function extractCsvHeaders(csv: string): string[] {
  const [headerLine] = csv.split(/\r?\n/);
  if (!headerLine) return [];
  const cells = headerLine.split(",").map(cleanCsvCell);
  if (cells.length <= 1) return [];
  return cells.slice(1).filter((cell) => cell.length > 0);
}

function normalizeHeaderName(value: string): string {
  return value.trim().replace(/\s+/g, " ").toLowerCase();
}

function cleanCsvCell(cell: string): string {
  return cell.replace(/^"|"$/g, "").trim();
}
