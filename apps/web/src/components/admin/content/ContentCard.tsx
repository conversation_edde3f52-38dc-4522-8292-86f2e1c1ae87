import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Edit, Trash2, ExternalLink } from "lucide-react";
import { formatDate } from "@/lib/utils/admin";

interface ContentCardProps {
  content: any;
  onEdit: (content: any) => void;
  onDelete: (id: string) => void;
  isDeleting: boolean;
}

export const ContentCard = ({ content, onEdit, onDelete, isDeleting }: ContentCardProps) => {
  return (
    <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
      <div className="flex-1">
        <div className="space-y-2">
          <h3 className="font-medium text-lg">
            {content.content_title || content.content_account}
          </h3>

          {content.content_description && (
            <p className="text-sm text-muted-foreground line-clamp-2">
              {content.content_description}
            </p>
          )}

          <div className="flex items-center gap-2">
            <a
              href={content.content_link}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-blue-600 hover:text-blue-800 hover:underline flex items-center gap-1 transition-colors"
            >
              Link
              <ExternalLink className="h-3 w-3" />
            </a>
          </div>

          {/* Content Types and Categories */}
          <div className="flex flex-wrap gap-1 mt-2">
            {content.content_types?.map((type: string) => (
              <Badge key={type} variant="secondary" className="text-xs">
                {type}
              </Badge>
            ))}
            {content.twitter_content_type && (
              <Badge variant="outline" className="text-xs">
                {content.twitter_content_type}
              </Badge>
            )}
            {content.content_categories?.slice(0, 3).map((category: string) => (
              <Badge key={category} variant="outline" className="text-xs">
                #{category}
              </Badge>
            ))}
            {content.content_categories && content.content_categories.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{content.content_categories.length - 3} more
              </Badge>
            )}
          </div>

          {/* Content Metrics Table */}
          {(() => {
            const metrics = [
              {
                value: content.content_views,
                label: 'Views',
                color: 'text-blue-600',
                show: content.content_views && content.content_views > 0
              },
              {
                value: content.content_listeners,
                label: 'Listeners', 
                color: 'text-purple-600',
                show: content.content_listeners && content.content_listeners > 0
              },
              {
                value: content.twitter_impressions,
                label: 'Impressions',
                color: 'text-green-600', 
                show: content.twitter_impressions && content.twitter_impressions > 0
              },
              {
                value: formatDate(content.content_created_date),
                label: 'Created',
                color: 'text-gray-600',
                show: content.content_created_date,
                isDate: true
              }
            ].filter(metric => metric.show);

            return metrics.length > 0 ? (
              <div className="mt-3 pt-3 border-t">
                <div className={`grid gap-3 text-xs ${metrics.length === 1 ? 'grid-cols-1' : metrics.length === 2 ? 'grid-cols-2' : metrics.length === 3 ? 'grid-cols-3' : metrics.length === 4 ? 'grid-cols-2 lg:grid-cols-4' : 'grid-cols-2 lg:grid-cols-5'}`}>
                  {metrics.map((metric, index) => (
                    <div key={index} className="text-center">
                      <div className={`font-medium ${metric.color}`}>
                        {metric.isDate ? metric.value : metric.value.toLocaleString()}
                      </div>
                      <div className="text-muted-foreground">{metric.label}</div>
                    </div>
                  ))}
                </div>
              </div>
            ) : null;
          })()}
        </div>
      </div>
      
      <div className="flex gap-2">
        <Button
          size="sm"
          variant="outline"
          onClick={() => onEdit(content)}
        >
          <Edit className="h-4 w-4" />
        </Button>
        <Button
          size="sm"
          variant="destructive"
          onClick={() => onDelete(content._id)}
          disabled={isDeleting}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};
