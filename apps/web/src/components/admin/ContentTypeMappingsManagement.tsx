"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import type { Id, Doc } from "@/../../../convex/_generated/dataModel";
import { api } from "@/../../../convex/_generated/api";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { Edit, Plus, Trash2 } from "lucide-react";

interface MappingFormState {
  source_value: string;
  target_content_type: string;
  twitter_content_type: string;
  description: string;
}

const createInitialFormState = (): MappingFormState => ({
  source_value: "",
  target_content_type: "",
  twitter_content_type: "",
  description: "",
});

export function ContentTypeMappingsManagement() {
  const mappings = useQuery(api.admin.listContentTypeMappings, {});
  const createMappingMutation = useMutation(api.admin.createContentTypeMapping);
  const updateMappingMutation = useMutation(api.admin.updateContentTypeMapping);
  const deleteMappingMutation = useMutation(api.admin.deleteContentTypeMapping);

  const [formState, setFormState] = useState<MappingFormState>(createInitialFormState());
  const [editingId, setEditingId] = useState<Id<"content_type_mappings"> | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [pendingToggleId, setPendingToggleId] = useState<string | null>(null);

  const resetForm = () => {
    setFormState(createInitialFormState());
    setEditingId(null);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!formState.source_value.trim() || !formState.target_content_type.trim()) {
      toast.error("Source value and target content type are required");
      return;
    }

    setIsSubmitting(true);
    try {
      if (editingId) {
        await updateMappingMutation({
          id: editingId,
          source_value: formState.source_value.trim(),
          target_content_type: formState.target_content_type.trim(),
          twitter_content_type: formState.twitter_content_type.trim() || undefined,
          description: formState.description.trim() || undefined,
        });
        toast.success("Mapping updated");
      } else {
        await createMappingMutation({
          source_value: formState.source_value.trim(),
          target_content_type: formState.target_content_type.trim(),
          twitter_content_type: formState.twitter_content_type.trim() || undefined,
          description: formState.description.trim() || undefined,
        });
        toast.success("Mapping created");
      }
      resetForm();
    } catch (error) {
      console.error("Failed to save mapping", error);
      toast.error("Unable to save mapping. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const startEditing = (mapping: Doc<"content_type_mappings">) => {
    setEditingId(mapping._id);
    setFormState({
      source_value: mapping.source_value ?? "",
      target_content_type: mapping.target_content_type ?? "",
      twitter_content_type: mapping.twitter_content_type ?? "",
      description: mapping.description ?? "",
    });
  };

  const handleToggleActive = async (mapping: Doc<"content_type_mappings">) => {
    setPendingToggleId(mapping._id);
    try {
      await updateMappingMutation({
        id: mapping._id,
        is_active: !mapping.is_active,
      });
      toast.success(`Mapping ${mapping.is_active ? "disabled" : "enabled"}`);
    } catch (error) {
      console.error("Failed to toggle mapping", error);
      toast.error("Unable to update mapping status.");
    } finally {
      setPendingToggleId(null);
    }
  };

  const handleDelete = async (mapping: Doc<"content_type_mappings">) => {
    try {
      await deleteMappingMutation({ id: mapping._id });
      toast.success("Mapping deleted");
      if (editingId === mapping._id) {
        resetForm();
      }
    } catch (error) {
      console.error("Failed to delete mapping", error);
      toast.error("Unable to delete mapping.");
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Content Type Mappings</h2>
            <p className="text-sm text-muted-foreground">
              Map values from external sources to internal content types and optional Twitter classifications.
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <label className="text-sm font-medium">Source Value</label>
            <Input
              value={formState.source_value}
              onChange={(event) => setFormState((prev) => ({ ...prev, source_value: event.target.value }))}
              placeholder="e.g. Spaces"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Target Content Type</label>
            <Input
              value={formState.target_content_type}
              onChange={(event) => setFormState((prev) => ({ ...prev, target_content_type: event.target.value }))}
              placeholder="e.g. twitter"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Twitter Content Type</label>
            <Input
              value={formState.twitter_content_type}
              onChange={(event) => setFormState((prev) => ({ ...prev, twitter_content_type: event.target.value }))}
              placeholder="Optional e.g. space"
            />
          </div>
          <div className="space-y-2 md:col-span-2">
            <label className="text-sm font-medium">Description</label>
            <Textarea
              value={formState.description}
              onChange={(event) => setFormState((prev) => ({ ...prev, description: event.target.value }))}
              placeholder="Optional description for this mapping"
              rows={3}
            />
          </div>
          <div className="flex items-center gap-2 md:col-span-2">
            <Button type="submit" disabled={isSubmitting} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              {editingId ? "Update Mapping" : "Create Mapping"}
            </Button>
            {editingId && (
              <Button type="button" variant="outline" onClick={resetForm}>
                Cancel
              </Button>
            )}
          </div>
        </form>
      </Card>

      <Card className="p-6 space-y-4">
        <h3 className="text-lg font-semibold">Existing Mappings</h3>
        {mappings === undefined ? (
          <div className="space-y-3">
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={index} className="h-16 w-full" />
            ))}
          </div>
        ) : mappings.length === 0 ? (
          <div className="rounded-lg border border-dashed p-6 text-center text-sm text-muted-foreground">
            No mappings configured yet.
          </div>
        ) : (
          <div className="space-y-3">
            {mappings.map((mapping: Doc<"content_type_mappings">) => (
              <div
                key={mapping._id}
                className="flex flex-col gap-3 rounded-lg border p-4 md:flex-row md:items-center md:justify-between"
              >
                <div className="space-y-1">
                  <div className="flex flex-wrap items-center gap-2">
                    <span className="font-medium capitalize">{mapping.source_value}</span>
                    <Badge variant="secondary">{mapping.target_content_type}</Badge>
                    {mapping.twitter_content_type && (
                      <Badge variant="outline">Twitter: {mapping.twitter_content_type}</Badge>
                    )}
                    <Badge variant={mapping.is_active ? "secondary" : "destructive"}>
                      {mapping.is_active ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  {mapping.description && (
                    <p className="text-sm text-muted-foreground">{mapping.description}</p>
                  )}
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-muted-foreground">Active</span>
                    <Switch
                      checked={mapping.is_active ?? true}
                      onCheckedChange={() => handleToggleActive(mapping)}
                      disabled={pendingToggleId === mapping._id}
                    />
                  </div>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => startEditing(mapping)}
                    title="Edit mapping"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-destructive"
                    onClick={() => handleDelete(mapping)}
                    title="Delete mapping"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
}
