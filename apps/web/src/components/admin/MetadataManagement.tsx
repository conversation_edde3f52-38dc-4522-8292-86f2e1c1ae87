"use client";

import { useState } from "react";
import type { Id } from "@/../../../convex/_generated/dataModel";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { Plus, Trash2 } from "lucide-react";

interface MetadataItem {
  value: string;
  isDefault: boolean;
  isCustom: boolean;
  isDisabled: boolean;
  id?: Id<"custom_metadata_values">;
}

interface MetadataPayload {
  content_types: string[];
  content_categories: string[];
  contentTypesMeta: MetadataItem[];
  contentCategoriesMeta: MetadataItem[];
}

interface MetadataManagementProps {
  data: MetadataPayload | undefined;
  isLoading: boolean;
  onAdd: (type: "content_type" | "content_category", value: string) => Promise<void>;
  onToggle: (type: "content_type" | "content_category", value: string, disabled: boolean) => Promise<void>;
  onDelete: (id: Id<"custom_metadata_values">) => Promise<void>;
}

const Section = ({
  title,
  description,
  items,
  enumType,
  newValue,
  onNewValueChange,
  onAdd,
  onToggle,
  onDelete,
  pendingKey,
  deletingId,
  isSubmitting,
}: {
  title: string;
  description: string;
  items: MetadataItem[];
  enumType: "content_type" | "content_category";
  newValue: string;
  onNewValueChange: (value: string) => void;
  onAdd: (value: string) => Promise<void>;
  onToggle: (item: MetadataItem) => Promise<void>;
  onDelete: (item: MetadataItem) => Promise<void>;
  pendingKey: string | null;
  deletingId: string | null;
  isSubmitting: boolean;
}) => (
  <Card className="p-6 space-y-4">
    <div>
      <h3 className="text-lg font-semibold">{title}</h3>
      <p className="text-sm text-muted-foreground">{description}</p>
    </div>

    <div className="flex gap-2">
      <Input
        value={newValue}
        onChange={(event) => onNewValueChange(event.target.value)}
        placeholder={`Add a new ${enumType === "content_type" ? "content type" : "category"}`}
      />
      <Button
        type="button"
        onClick={async () => {
          try {
            await onAdd(newValue);
          } catch (error) {
            const message = error instanceof Error ? error.message : "Unable to add value";
            toast.error(message);
          }
        }}
        disabled={!newValue.trim() || isSubmitting}
        className="flex items-center gap-2"
      >
        <Plus className="h-4 w-4" />
        Add
      </Button>
    </div>

    <div className="space-y-3">
      {items.map((item) => {
        const key = `${enumType}:${item.value}`;
        const isPending = pendingKey === key;
        const isDeleting = deletingId === (item.id ?? "");
        return (
          <div
            key={key}
            className="flex items-center justify-between rounded-lg border p-3"
          >
            <div className="flex flex-col gap-1">
              <div className="flex items-center gap-3">
                <span className="font-medium capitalize">{item.value}</span>
                {item.isDefault ? (
                  <Badge variant="secondary">Default</Badge>
                ) : (
                  <Badge variant="outline">Custom</Badge>
                )}
                {item.isDisabled && <Badge variant="destructive">Disabled</Badge>}
              </div>
              <span className="text-xs text-muted-foreground">
                {enumType === "content_type" ? "Controls available content type options" : "Controls available category options"}
              </span>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground">Visible</span>
                <Switch
                  checked={!item.isDisabled}
                  onCheckedChange={(checked) => onToggle({ ...item, isDisabled: !checked })}
                  disabled={isPending}
                />
              </div>
              {item.isCustom && item.id && (
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => onDelete(item)}
                  disabled={isDeleting}
                  className="text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        );
      })}
      {items.length === 0 && (
        <div className="rounded-lg border border-dashed p-6 text-center text-sm text-muted-foreground">
          No values configured yet.
        </div>
      )}
    </div>
  </Card>
);

export function MetadataManagement({ data, isLoading, onAdd, onToggle, onDelete }: MetadataManagementProps) {
  const [newType, setNewType] = useState("");
  const [newCategory, setNewCategory] = useState("");
  const [pendingKey, setPendingKey] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [addingType, setAddingType] = useState<"content_type" | "content_category" | null>(null);

  const handleAdd = async (enumType: "content_type" | "content_category", value: string) => {
    setAddingType(enumType);
    try {
      await onAdd(enumType, value);
      toast.success(`${enumType === "content_type" ? "Content type" : "Category"} added`);
      if (enumType === "content_type") {
        setNewType("");
      } else {
        setNewCategory("");
      }
    } catch (error) {
      console.error("Failed to add metadata value", error);
      throw error;
    } finally {
      setAddingType(null);
    }
  };

  const handleToggle = async (enumType: "content_type" | "content_category", item: MetadataItem) => {
    const key = `${enumType}:${item.value}`;
    setPendingKey(key);
    try {
      await onToggle(enumType, item.value, item.isDisabled);
      toast.success(
        `${item.value} ${item.isDisabled ? "hidden" : "enabled"} for ${enumType === "content_type" ? "content types" : "categories"}`,
      );
    } catch (error) {
      console.error("Failed to toggle metadata value", error);
      toast.error("Unable to update value. Please try again.");
    } finally {
      setPendingKey(null);
    }
  };

  const handleDelete = async (item: MetadataItem) => {
    if (!item.id) return;
    setDeletingId(item.id);
    try {
      await onDelete(item.id);
      toast.success(`Deleted ${item.value}`);
    } catch (error) {
      console.error("Failed to delete metadata value", error);
      toast.error("Unable to delete value. Please try again.");
    } finally {
      setDeletingId(null);
    }
  };

  if (isLoading || !data) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-48 w-full" />
        <Skeleton className="h-48 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        <Section
          title="Content Types"
          description="Manage which content types are available when creating content."
          items={data.contentTypesMeta}
          enumType="content_type"
          newValue={newType}
          onNewValueChange={setNewType}
         onAdd={(value) => handleAdd("content_type", value)}
         onToggle={(item) => handleToggle("content_type", item)}
         onDelete={handleDelete}
         pendingKey={pendingKey}
         deletingId={deletingId}
          isSubmitting={addingType === "content_type"}
        />
        <Section
          title="Content Categories"
          description="Enable or disable categories to streamline content tagging."
          items={data.contentCategoriesMeta}
          enumType="content_category"
          newValue={newCategory}
          onNewValueChange={setNewCategory}
          onAdd={(value) => handleAdd("content_category", value)}
          onToggle={(item) => handleToggle("content_category", item)}
          onDelete={handleDelete}
          pendingKey={pendingKey}
          deletingId={deletingId}
          isSubmitting={addingType === "content_category"}
        />
      </div>
    </div>
  );
}
