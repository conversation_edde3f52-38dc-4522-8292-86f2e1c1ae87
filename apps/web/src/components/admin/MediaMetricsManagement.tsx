"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import type { Id, Doc } from "@/../../../convex/_generated/dataModel";
import { api } from "@/../../../convex/_generated/api";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { Edit, Plus, Trash2 } from "lucide-react";

interface MediaMetricFormState {
  account_handle: string;
  display_name: string;
  description: string;
  profile_image_url: string;
  total_impressions: string;
  total_views: string;
  total_followers: string;
  display_order: string;
}

const createInitialFormState = (): MediaMetricFormState => ({
  account_handle: "",
  display_name: "",
  description: "",
  profile_image_url: "",
  total_impressions: "",
  total_views: "",
  total_followers: "",
  display_order: "",
});

const toNumber = (value: string) => {
  if (!value.trim()) return undefined;
  const parsed = Number(value);
  return Number.isNaN(parsed) ? undefined : parsed;
};

export function MediaMetricsManagement() {
  const metrics = useQuery(api.admin.getMediaAccountMetrics, {});
  const createMetricMutation = useMutation(api.admin.createMediaAccountMetric);
  const updateMetricMutation = useMutation(api.admin.updateMediaAccountMetric);
  const deleteMetricMutation = useMutation(api.admin.deleteMediaAccountMetric);

  const [formState, setFormState] = useState<MediaMetricFormState>(createInitialFormState());
  const [editingId, setEditingId] = useState<Id<"media_account_metrics"> | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [pendingToggleId, setPendingToggleId] = useState<string | null>(null);

  const resetForm = () => {
    setFormState(createInitialFormState());
    setEditingId(null);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!formState.account_handle.trim() || !formState.display_name.trim()) {
      toast.error("Account handle and display name are required");
      return;
    }

    setIsSubmitting(true);

    try {
      const payload = {
        account_handle: formState.account_handle.trim().replace(/^@/, ""),
        display_name: formState.display_name.trim(),
        description: formState.description.trim() || undefined,
        profile_image_url: formState.profile_image_url.trim() || undefined,
        total_impressions: toNumber(formState.total_impressions),
        total_views: toNumber(formState.total_views),
        total_followers: toNumber(formState.total_followers),
        display_order: toNumber(formState.display_order),
      };

      if (editingId) {
        await updateMetricMutation({ id: editingId, ...payload });
        toast.success("Media metric updated");
      } else {
        await createMetricMutation({ ...payload });
        toast.success("Media metric created");
      }

      resetForm();
    } catch (error) {
      console.error("Failed to save media metric", error);
      toast.error("Unable to save media metric.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const startEditing = (metric: Doc<"media_account_metrics">) => {
    setEditingId(metric._id);
    setFormState({
      account_handle: metric.account_handle ?? "",
      display_name: metric.display_name ?? "",
      description: metric.description ?? "",
      profile_image_url: metric.profile_image_url ?? "",
      total_impressions: metric.total_impressions?.toString() ?? "",
      total_views: metric.total_views?.toString() ?? "",
      total_followers: metric.total_followers?.toString() ?? "",
      display_order: metric.display_order?.toString() ?? "",
    });
  };

  const handleToggleActive = async (metric: Doc<"media_account_metrics">) => {
    setPendingToggleId(metric._id);
    try {
      await updateMetricMutation({
        id: metric._id,
        is_active: !(metric.is_active ?? true),
      });
      toast.success(`Metric ${metric.is_active ? "disabled" : "enabled"}`);
    } catch (error) {
      console.error("Failed to toggle media metric", error);
      toast.error("Unable to update metric status.");
    } finally {
      setPendingToggleId(null);
    }
  };

  const handleDelete = async (metric: Doc<"media_account_metrics">) => {
    try {
      await deleteMetricMutation({ id: metric._id });
      toast.success("Media metric deleted");
      if (editingId === metric._id) {
        resetForm();
      }
    } catch (error) {
      console.error("Failed to delete media metric", error);
      toast.error("Unable to delete metric.");
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6 space-y-6">
        <div>
          <h2 className="text-2xl font-bold">Media Account Metrics</h2>
          <p className="text-sm text-muted-foreground">
            Manage social metrics for key accounts displayed across the dashboard.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <label className="text-sm font-medium">Account Handle</label>
            <Input
              value={formState.account_handle}
              onChange={(event) => setFormState((prev) => ({ ...prev, account_handle: event.target.value }))}
              placeholder="e.g. @intoblockchain"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Display Name</label>
            <Input
              value={formState.display_name}
              onChange={(event) => setFormState((prev) => ({ ...prev, display_name: event.target.value }))}
              placeholder="Team X"
            />
          </div>
          <div className="space-y-2 md:col-span-2">
            <label className="text-sm font-medium">Description</label>
            <Textarea
              value={formState.description}
              onChange={(event) => setFormState((prev) => ({ ...prev, description: event.target.value }))}
              placeholder="Short description of this account"
              rows={3}
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Profile Image URL</label>
            <Input
              value={formState.profile_image_url}
              onChange={(event) => setFormState((prev) => ({ ...prev, profile_image_url: event.target.value }))}
              placeholder="https://..."
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Display Order</label>
            <Input
              value={formState.display_order}
              onChange={(event) => setFormState((prev) => ({ ...prev, display_order: event.target.value }))}
              placeholder="Optional order value"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Total Impressions</label>
            <Input
              value={formState.total_impressions}
              onChange={(event) => setFormState((prev) => ({ ...prev, total_impressions: event.target.value }))}
              placeholder="e.g. 120000"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Total Views</label>
            <Input
              value={formState.total_views}
              onChange={(event) => setFormState((prev) => ({ ...prev, total_views: event.target.value }))}
              placeholder="e.g. 45000"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Total Followers</label>
            <Input
              value={formState.total_followers}
              onChange={(event) => setFormState((prev) => ({ ...prev, total_followers: event.target.value }))}
              placeholder="e.g. 12000"
            />
          </div>
          <div className="flex items-center gap-2 md:col-span-2">
            <Button type="submit" disabled={isSubmitting} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              {editingId ? "Update Metric" : "Create Metric"}
            </Button>
            {editingId && (
              <Button type="button" variant="outline" onClick={resetForm}>
                Cancel
              </Button>
            )}
          </div>
        </form>
      </Card>

      <Card className="p-6 space-y-4">
        <h3 className="text-lg font-semibold">Tracked Accounts</h3>
        {metrics === undefined ? (
          <div className="space-y-3">
            {Array.from({ length: 4 }).map((_, index) => (
              <Skeleton key={index} className="h-20 w-full" />
            ))}
          </div>
        ) : metrics.length === 0 ? (
          <div className="rounded-lg border border-dashed p-6 text-center text-sm text-muted-foreground">
            No media metrics configured yet.
          </div>
        ) : (
          <div className="space-y-3">
            {metrics.map((metric: Doc<"media_account_metrics">) => (
              <div
                key={metric._id}
                className="flex flex-col gap-3 rounded-lg border p-4 md:flex-row md:items-center md:justify-between"
              >
                <div className="space-y-1">
                  <div className="flex flex-wrap items-center gap-2">
                    <span className="font-medium">@{metric.account_handle}</span>
                    <Badge variant="secondary">{metric.display_name}</Badge>
                    <Badge variant={metric.is_active !== false ? "secondary" : "destructive"}>
                      {metric.is_active !== false ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <div className="flex flex-wrap gap-3 text-xs text-muted-foreground">
                    {typeof metric.total_impressions === "number" && (
                      <span>Impressions: {metric.total_impressions.toLocaleString()}</span>
                    )}
                    {typeof metric.total_views === "number" && (
                      <span>Views: {metric.total_views.toLocaleString()}</span>
                    )}
                    {typeof metric.total_followers === "number" && (
                      <span>Followers: {metric.total_followers.toLocaleString()}</span>
                    )}
                  </div>
                  {metric.description && (
                    <p className="text-sm text-muted-foreground">{metric.description}</p>
                  )}
                </div>
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-muted-foreground">Active</span>
                    <Switch
                      checked={metric.is_active !== false}
                      onCheckedChange={() => handleToggleActive(metric)}
                      disabled={pendingToggleId === metric._id}
                    />
                  </div>
                  <Button variant="outline" size="icon" onClick={() => startEditing(metric)} title="Edit metric">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-destructive"
                    onClick={() => handleDelete(metric)}
                    title="Delete metric"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
}
