import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Shield } from "lucide-react";
import Loader from "@/components/loader";
import { useAdminAuth } from "@/hooks/useAdminAuth";

interface AdminGuardProps {
  children: React.ReactNode;
}

export const AdminGuard = ({ children }: AdminGuardProps) => {
  const router = useRouter();
  const { user, userProfile, isLoading, isAuthenticated, isAdmin } = useAdminAuth();

  // Loading states
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader />
      </div>
    );
  }

  // Not authenticated - show login prompt
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8 max-w-md text-center">
          <Shield className="mx-auto mb-4 h-12 w-12 text-blue-500" />
          <h1 className="text-2xl font-bold mb-2">Authentication Required</h1>
          <p className="text-muted-foreground mb-4">
            You need to sign in to access the admin panel.
          </p>
          <Button onClick={() => router.push("/")} variant="outline">
            Go to Login
          </Button>
        </Card>
      </div>
    );
  }

  // Not admin
  if (!isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="p-8 max-w-md text-center">
          <Shield className="mx-auto mb-4 h-12 w-12 text-red-500" />
          <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
          <p className="text-muted-foreground mb-4">
            You don't have permission to access this page. Admin role required.
          </p>
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">
              Current role: {userProfile?.role || 'none'}
            </p>
            <Button onClick={() => router.push("/")} variant="outline">
              Go Home
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return <>{children}</>;
};