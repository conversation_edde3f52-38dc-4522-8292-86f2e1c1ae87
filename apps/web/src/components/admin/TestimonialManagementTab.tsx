"use client";

import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../../../convex/_generated/api";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { FileUpload } from "@/components/ui/file-upload";
import { TestimonialsGrid } from "@/components/dashboard/TestimonialsGrid";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { 
  Plus, 
  X, 
  Star,
  Building2,
  Link as LinkIcon,
  DollarSign
} from "lucide-react";

interface TestimonialFormData {
  content_title: string;
  content_description: string;
  client_name: string;
  content_link: string;
  screenshot_urls: string[];
  satisfaction_rating: number;
  project_value: number;
}

const initialFormData: TestimonialFormData = {
  content_title: "",
  content_description: "",
  client_name: "",
  content_link: "",
  screenshot_urls: [],
  satisfaction_rating: 5,
  project_value: 0
};

export function TestimonialManagementTab() {
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState<TestimonialFormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const createTestimonialMutation = useMutation(api.testimonials.createTestimonial);
  const deleteTestimonialMutation = useMutation(api.testimonials.deleteTestimonial);

  const handleInputChange = (field: keyof TestimonialFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.content_title.trim()) {
      toast.error("Project title is required");
      return;
    }
    
    if (!formData.content_description.trim()) {
      toast.error("Testimonial text is required");
      return;
    }
    
    if (!formData.client_name.trim()) {
      toast.error("Client name is required");
      return;
    }

    setIsSubmitting(true);
    
    try {
      await createTestimonialMutation({
        content_title: formData.content_title.trim(),
        content_description: formData.content_description.trim(),
        client_name: formData.client_name.trim(),
        content_link: formData.content_link.trim() || undefined,
        screenshot_urls: formData.screenshot_urls.length > 0 ? formData.screenshot_urls : undefined,
        satisfaction_rating: formData.satisfaction_rating,
        project_value: formData.project_value > 0 ? formData.project_value : undefined
      });
      
      toast.success("Testimonial created successfully!");
      setFormData(initialFormData);
      setShowForm(false);
    } catch (error) {
      console.error("Failed to create testimonial:", error);
      toast.error("Failed to create testimonial. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleUploadComplete = (storageIds: string[]) => {
    setFormData(prev => ({
      ...prev,
      screenshot_urls: [...prev.screenshot_urls, ...storageIds]
    }));
    toast.success(`${storageIds.length} screenshots uploaded successfully!`);
  };

  const handleRemoveScreenshot = (index: number) => {
    setFormData(prev => ({
      ...prev,
      screenshot_urls: prev.screenshot_urls.filter((_, i) => i !== index)
    }));
  };

  const handleDeleteTestimonial = async (id: string) => {
    try {
      await deleteTestimonialMutation({ id: id as any });
      toast.success("Testimonial deleted successfully!");
    } catch (error) {
      console.error("Failed to delete testimonial:", error);
      toast.error("Failed to delete testimonial. Please try again.");
    }
  };

  const renderStarRating = () => {
    return (
      <div className="flex items-center gap-2">
        <Label htmlFor="satisfaction_rating">Satisfaction Rating</Label>
        <div className="flex items-center gap-1">
          {Array.from({ length: 5 }).map((_, i) => (
            <button
              key={i}
              type="button"
              onClick={() => handleInputChange('satisfaction_rating', i + 1)}
              className={`p-1 rounded transition-colors ${
                i < formData.satisfaction_rating
                  ? 'text-yellow-400 hover:text-yellow-500'
                  : 'text-muted-foreground hover:text-yellow-300'
              }`}
            >
              <Star 
                className={`h-5 w-5 ${
                  i < formData.satisfaction_rating ? 'fill-current' : ''
                }`}
              />
            </button>
          ))}
          <span className="text-sm text-muted-foreground ml-2">
            ({formData.satisfaction_rating}/5)
          </span>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Testimonial Management</h2>
          <p className="text-muted-foreground">
            Create and manage client testimonials with screenshots and project details
          </p>
        </div>
        <Button
          onClick={() => setShowForm(!showForm)}
          className="flex items-center gap-2"
        >
          {showForm ? (
            <>
              <X className="h-4 w-4" />
              Cancel
            </>
          ) : (
            <>
              <Plus className="h-4 w-4" />
              Add Testimonial
            </>
          )}
        </Button>
      </div>

      {/* Create Form */}
      {showForm && (
        <Card className="p-6">
          <h3 className="text-xl font-semibold mb-6">Create New Testimonial</h3>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-muted-foreground uppercase tracking-wide border-b pb-2">
                Project Information
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="content_title">
                    Project Title <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="content_title"
                    value={formData.content_title}
                    onChange={(e) => handleInputChange('content_title', e.target.value)}
                    placeholder="Website Redesign Project"
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="client_name">
                    Client Name <span className="text-destructive">*</span>
                  </Label>
                  <div className="relative">
                    <Building2 className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="client_name"
                      value={formData.client_name}
                      onChange={(e) => handleInputChange('client_name', e.target.value)}
                      placeholder="Acme Corporation"
                      className="pl-9"
                      required
                    />
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="content_link">Project URL (optional)</Label>
                <div className="relative">
                  <LinkIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="content_link"
                    type="url"
                    value={formData.content_link}
                    onChange={(e) => handleInputChange('content_link', e.target.value)}
                    placeholder="https://example.com"
                    className="pl-9"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="content_description">
                  Testimonial Text <span className="text-destructive">*</span>
                </Label>
                <Textarea
                  id="content_description"
                  value={formData.content_description}
                  onChange={(e) => handleInputChange('content_description', e.target.value)}
                  placeholder="The team delivered exceptional results. Our website conversion increased by 40% within the first month..."
                  rows={4}
                  required
                />
              </div>
            </div>

            {/* Project Details */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-muted-foreground uppercase tracking-wide border-b pb-2">
                Project Details
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  {renderStarRating()}
                </div>
                
                <div>
                  <Label htmlFor="project_value">Project Value (optional)</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="project_value"
                      type="number"
                      min="0"
                      step="100"
                      value={formData.project_value || ""}
                      onChange={(e) => handleInputChange('project_value', parseFloat(e.target.value) || 0)}
                      placeholder="5000"
                      className="pl-9"
                    />
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Project value/impact in dollars
                  </p>
                </div>
              </div>
            </div>

            {/* Screenshots */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-muted-foreground uppercase tracking-wide border-b pb-2">
                Project Screenshots
              </h4>
              
              {/* Current Screenshots */}
              {formData.screenshot_urls.length > 0 && (
                <div className="space-y-2">
                  <Label>Uploaded Screenshots ({formData.screenshot_urls.length})</Label>
                  <div className="flex flex-wrap gap-2">
                    {formData.screenshot_urls.map((_, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="flex items-center gap-2 px-3 py-1"
                      >
                        Screenshot {index + 1}
                        <button
                          type="button"
                          onClick={() => handleRemoveScreenshot(index)}
                          className="hover:bg-destructive/20 rounded-full p-0.5"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              {/* File Upload */}
              <FileUpload
                accept="image/*"
                multiple={true}
                maxSize={5}
                maxFiles={5}
                onUploadComplete={handleUploadComplete}
                onUploadError={(error) => toast.error(`Upload failed: ${error}`)}
                className="border-dashed border-2 border-muted-foreground/25"
              />
            </div>

            {/* Form Actions */}
            <div className="flex gap-2 pt-4 border-t">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex items-center gap-2"
              >
                {isSubmitting ? (
                  <>
                    <div className="h-4 w-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4" />
                    Create Testimonial
                  </>
                )}
              </Button>
              
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowForm(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </div>
          </form>
        </Card>
      )}

      {/* Testimonials Grid */}
      <Card className="p-6">
        <TestimonialsGrid
          showFilters={true}
          showSearch={true}
          showPagination={true}
          itemsPerPage={12}
        />
      </Card>
    </div>
  );
}