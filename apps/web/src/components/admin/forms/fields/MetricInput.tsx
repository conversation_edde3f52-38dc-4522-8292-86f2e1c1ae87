import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { parseMetricValue, formatNumber } from "@/lib/utils/admin";
import type { MetricInputProps } from "@/types/admin";

export const MetricInput = ({ label, value, onChange, placeholder, id, error }: MetricInputProps) => {
  const [displayValue, setDisplayValue] = useState(value.toString());

  // Update display value when prop value changes (for editing)
  useEffect(() => {
    setDisplayValue(value.toString());
  }, [value]);

  const handleInputChange = (input: string) => {
    setDisplayValue(input);
  };

  const handleBlur = () => {
    const parsedValue = parseMetricValue(displayValue);
    onChange(parsedValue);
    // Update display to show the clean parsed value
    setDisplayValue(parsedValue.toString());
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Prevent Enter from doing anything - user should tab or click away
    if (e.key === 'Enter') {
      e.preventDefault();
    }
  };

  return (
    <div className="space-y-2">
      <Label htmlFor={id} className="flex items-center gap-2">
        {label}
      </Label>
      <div className="relative">
        <Input
          id={id}
          type="text"
          value={displayValue}
          onChange={(e) => handleInputChange(e.target.value)}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder={placeholder || "0, 1k, 1.5m, 2b"}
          className={`${error ? "border-destructive" : ""}`}
        />
      </div>
      <div className="flex justify-between text-xs text-muted-foreground">
        <span>Examples: 1k, 1.5m, 2b, 1,234,567</span>
        {value > 0 && (
          <span className="font-medium">
            = {value.toLocaleString()}
          </span>
        )}
      </div>
      {error && <p className="text-sm text-destructive">{error}</p>}
    </div>
  );
};