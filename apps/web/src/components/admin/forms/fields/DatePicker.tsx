import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar } from "lucide-react";
import type { DatePickerProps } from "@/types/admin";

export const DatePicker = ({ label, value, onChange, required = false, error }: DatePickerProps) => {
  const today = new Date().toISOString().split('T')[0];
  const maxDate = today; // Don't allow future dates for content creation
  const minDate = '2020-01-01'; // Reasonable minimum date

  return (
    <div className="space-y-2">
      <Label htmlFor="date-picker" className="flex items-center gap-2">
        <Calendar className="h-4 w-4" />
        {label}
        {required && <span className="text-destructive">*</span>}
      </Label>
      <div className="relative">
        <Input
          id="date-picker"
          type="date"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          required={required}
          min={minDate}
          max={maxDate}
          className={`${error ? "border-destructive" : ""} pr-10`}
        />
        <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground pointer-events-none" />
      </div>
      {error && <p className="text-sm text-destructive">{error}</p>}
      <p className="text-xs text-muted-foreground">
        Select the date when this content was originally created
      </p>
    </div>
  );
};