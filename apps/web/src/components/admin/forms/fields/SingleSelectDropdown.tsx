import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import type { SingleSelectDropdownProps } from "@/types/admin";

export const SingleSelectDropdown = ({ 
  label, 
  value, 
  options, 
  onChange, 
  placeholder, 
  allowNull = true 
}: SingleSelectDropdownProps) => {
  return (
    <div className="space-y-2">
      <Label>{label}</Label>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="w-full justify-between">
            <span className={value ? "" : "text-muted-foreground"}>
              {value ? value.charAt(0).toUpperCase() + value.slice(1) : (placeholder || "Select option...")}
            </span>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          {allowNull && (
            <DropdownMenuItem onClick={() => onChange(null)}>
              <span className="text-muted-foreground">None selected</span>
            </DropdownMenuItem>
          )}
          {options.map(option => (
            <DropdownMenuItem 
              key={option}
              onClick={() => onChange(option)}
              className="cursor-pointer"
            >
              <span className="capitalize">{option}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};