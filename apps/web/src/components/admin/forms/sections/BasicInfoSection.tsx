import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Plus, X } from "lucide-react";
import { DatePicker } from "../fields/DatePicker";
import { SingleSelectDropdown } from "../fields/SingleSelectDropdown";
import { FieldError } from "../fields/FieldError";
import type { ContentFormData, FormErrors } from "@/types/admin";
import { TWITTER_CONTENT_TYPES } from "@/lib/constants/content";
import { extractHostFromUrl } from "@/lib/utils/admin";

interface BasicInfoSectionProps {
  formData: ContentFormData;
  formErrors: FormErrors;
  onChange: (field: keyof ContentFormData, value: any) => void;
}

export const BasicInfoSection = ({ formData, formErrors, onChange }: BasicInfoSectionProps) => {
  const handleInputChange = (field: keyof ContentFormData, value: any) => {
    let newData = { [field]: value };

    // Auto-fill host when content_link is changed
    if (field === 'content_link' && typeof value === 'string' && value.trim()) {
      const extractedHost = extractHostFromUrl(value.trim());
      if (extractedHost && (!formData.host || formData.host === '')) {
        onChange('host', extractedHost);
      }
    }

    onChange(field, value);
  };

  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide border-b pb-2">Basic Information</h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="content_link">Content Link</Label>
          <Input
            id="content_link"
            type="url"
            value={formData.content_link}
            onChange={(e) => handleInputChange('content_link', e.target.value)}
            required
            className={formErrors.content_link ? "border-destructive" : ""}
          />
          <FieldError error={formErrors.content_link} />
        </div>
        
        <div>
          <Label htmlFor="host">Host Account</Label>
          <Input
            id="host"
            value={formData.host}
            onChange={(e) => handleInputChange('host', e.target.value)}
            placeholder="Primary Twitter account (without @)"
            required
            className={formErrors.host ? "border-destructive" : ""}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Auto-filled from Twitter/X links, but you can edit it manually
          </p>
          <FieldError error={formErrors.host} />
        </div>
        
        <div>
          <Label htmlFor="content_account">Content Accounts</Label>
          <div className="space-y-2">
            {formData.content_account.map((account, index) => (
              <div key={index} className="flex gap-2">
                <Input
                  value={account}
                  onChange={(e) => {
                    const newAccounts = [...formData.content_account];
                    newAccounts[index] = e.target.value;
                    onChange('content_account', newAccounts);
                  }}
                  placeholder="Twitter account (without @)"
                  className="flex-1"
                />
                {formData.content_account.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const newAccounts = formData.content_account.filter((_, i) => i !== index);
                      onChange('content_account', newAccounts);
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                onChange('content_account', [...formData.content_account, '']);
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Account
            </Button>
          </div>
          <FieldError error={formErrors.content_account} />
        </div>
        
        <div>
          <DatePicker
            label="Created Date"
            value={formData.content_created_date}
            onChange={(value) => onChange('content_created_date', value)}
            required={true}
            error={formErrors.content_created_date}
          />
        </div>
        
        <div>
          <Label htmlFor="content_title">Title (optional)</Label>
          <Input
            id="content_title"
            value={formData.content_title || ''}
            onChange={(e) => onChange('content_title', e.target.value || null)}
          />
        </div>
        
        <div>
          <SingleSelectDropdown
            label="Twitter Content Type"
            value={formData.twitter_content_type}
            options={TWITTER_CONTENT_TYPES}
            onChange={(value) => onChange('twitter_content_type', value)}
            placeholder="Select content type..."
            allowNull={true}
          />
        </div>
      </div>
    </div>
  );
};