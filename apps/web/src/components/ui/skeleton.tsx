import { cn } from "@/lib/utils"

function Skeleton({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      data-slot="skeleton"
      aria-busy
      className={cn(
        "relative overflow-hidden rounded-md bg-muted/60",
        "shadow-[inset_0_1px_0_0_rgba(255,255,255,0.06)]",
        className
      )}
      {...props}
    >
      <div
        aria-hidden
        className={cn(
          "pointer-events-none absolute inset-0 -translate-x-full w-[200%]",
          "animate-shimmer"
        )}
      />
    </div>
  )}

export { Skeleton }
