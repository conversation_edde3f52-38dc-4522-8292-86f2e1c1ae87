"use client";

import { createContext, useContext, type ReactNode } from "react";
import { Legend, Tooltip, type LegendProps, type TooltipProps } from "recharts";

import { cn } from "@/lib/utils";

export type ChartConfig = Record<
  string,
  {
    label?: string;
    color?: string;
  }
>;

type ChartContextValue = {
  config: ChartConfig;
};

const ChartContext = createContext<ChartContextValue | null>(null);

export function useChartConfig(): ChartContextValue {
  const context = useContext(ChartContext);
  if (!context) {
    throw new Error("Chart components must be wrapped in <ChartContainer>");
  }

  return context;
}

type ChartContainerProps = {
  config: ChartConfig;
  children: ReactNode;
  className?: string;
};

export function ChartContainer({ config, children, className }: ChartContainerProps) {
  const styleVars = Object.entries(config).reduce<Record<string, string>>((acc, [key, value]) => {
    if (value?.color) {
      acc[`--color-${key}`] = value.color;
    }
    return acc;
  }, {});

  return (
    <ChartContext.Provider value={{ config }}>
      <figure
        className={cn(
          "flex h-full w-full flex-col justify-between gap-4 rounded-2xl bg-card/40 p-4 text-card-foreground shadow-sm ring-1 ring-border/40 backdrop-blur",
          className,
        )}
        style={styleVars}
      >
        {children}
      </figure>
    </ChartContext.Provider>
  );
}

type ChartTooltipProps<TValue extends number | string, TName extends string> = TooltipProps<TValue, TName>;

export function ChartTooltip<TValue extends number | string = number, TName extends string = string>(
  props: ChartTooltipProps<TValue, TName>,
) {
  return <Tooltip cursor={{ stroke: "var(--border)", strokeWidth: 1 }} {...props} wrapperStyle={{ outline: "none" }} />;
}

type IndicatorStyle = "dot" | "line" | "dashed";

type ChartTooltipContentProps = {
  active?: boolean;
  payload?: any[];
  label?: string;
  className?: string;
  indicator?: IndicatorStyle;
  labelFormatter?: (value: string) => string;
};

export function ChartTooltipContent({
  active,
  payload,
  label,
  className,
  indicator = "line",
  labelFormatter,
}: ChartTooltipContentProps) {
  const { config } = useChartConfig();

  if (!active || !payload?.length) {
    return null;
  }

  const formattedLabel = labelFormatter ? labelFormatter(String(label)) : label;

  return (
    <div
      className={cn(
        "min-w-[180px] rounded-xl border border-border/60 bg-background/95 p-4 shadow-lg backdrop-blur",
        className,
      )}
    >
      {formattedLabel && <div className="text-sm font-semibold text-muted-foreground">{formattedLabel}</div>}
      <div className="mt-3 space-y-1.5">
        {payload.map((item) => {
          const key = item.dataKey as string;
          const colorVar = config[key]?.color ?? `var(--color-${key})`;
          const displayLabel = config[key]?.label ?? key;
          const value = item.value;

          return (
            <div key={key} className="flex items-center justify-between gap-3 text-sm font-medium">
              <div className="flex items-center gap-2">
                <Indicator color={colorVar} variant={indicator} />
                <span>{displayLabel}</span>
              </div>
              <span className="font-semibold text-foreground">{value}</span>
            </div>
          );
        })}
      </div>
    </div>
  );
}

type IndicatorProps = {
  color?: string;
  variant: IndicatorStyle;
};

function Indicator({ color = "var(--border)", variant }: IndicatorProps) {
  if (variant === "dot") {
    return <span className="size-2.5 rounded-full" style={{ backgroundColor: color }} />;
  }
  if (variant === "dashed") {
    return <span className="h-px w-3 border-t border-dashed" style={{ borderColor: color }} />;
  }
  return <span className="h-px w-3 bg-current" style={{ color }} />;
}

export function ChartLegend(props: LegendProps) {
  const { ref, ...restProps } = props as any;
  return <Legend {...restProps} iconType="circle" />;
}

type ChartLegendContentProps = {
  payload?: Array<{
    dataKey?: string;
    value?: string;
    color?: string;
  }>;
  className?: string;
};

export function ChartLegendContent({ payload, className }: ChartLegendContentProps) {
  const { config } = useChartConfig();

  if (!payload?.length) return null;

  // Deduplicate legend entries by label to avoid duplicates when multiple series share a label
  const seen = new Set<string>();
  const uniqueEntries = payload.filter((entry) => {
    const key = entry.dataKey as string;
    const label = (config[key]?.label ?? entry.value ?? key) as string;
    if (seen.has(label)) return false;
    seen.add(label);
    return true;
  });

  return (
    <div className={cn("flex flex-wrap items-center gap-3 text-xs font-medium text-muted-foreground", className)}>
      {uniqueEntries.map((entry) => {
        const key = entry.dataKey as string;
        const color = config[key]?.color ?? `var(--color-${key})` ?? entry.color;
        const label = config[key]?.label ?? entry.value ?? key;

        return (
          <div key={key} className="flex items-center gap-2 rounded-full bg-muted/60 px-3 py-1">
            <span className="size-2.5 rounded-full" style={{ backgroundColor: color }} />
            <span className="tracking-wide text-foreground/80">{label}</span>
          </div>
        );
      })}
    </div>
  );
}

