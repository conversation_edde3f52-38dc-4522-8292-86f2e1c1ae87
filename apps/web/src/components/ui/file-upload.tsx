"use client";

import { useState, useRef, useCallback } from "react";
import { useMutation } from "convex/react";
import { api } from "@/../../../convex/_generated/api";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { 
  Upload, 
  X, 
  Image as ImageIcon, 
  FileText, 
  File,
  CheckCircle,
  AlertCircle
} from "lucide-react";

interface FileItem {
  file: File;
  id: string;
  status: "pending" | "uploading" | "success" | "error";
  progress: number;
  storageId?: string;
  error?: string;
}

interface FileUploadProps {
  accept?: string;
  multiple?: boolean;
  maxSize?: number; // in MB
  maxFiles?: number;
  onUploadComplete?: (storageIds: string[]) => void;
  onUploadError?: (error: string) => void;
  className?: string;
  disabled?: boolean;
}

export function FileUpload({
  accept = "image/*",
  multiple = true,
  maxSize = 5, // 5MB default
  maxFiles = 10,
  onUploadComplete,
  onUploadError,
  className = "",
  disabled = false
}: FileUploadProps) {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const generateUploadUrl = useMutation(api.files.generateUploadUrl);
  const saveFileMetadata = useMutation(api.files.saveFileMetadata);

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith("image/")) return ImageIcon;
    if (fileType.includes("text") || fileType.includes("document")) return FileText;
    return File;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const validateFile = (file: File): string | null => {
    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
      return `File size exceeds ${maxSize}MB limit`;
    }

    // Check file type if accept is specified
    if (accept !== "*" && accept !== "*/*") {
      const acceptedTypes = accept.split(",").map(type => type.trim());
      const isAccepted = acceptedTypes.some(type => {
        if (type.endsWith("/*")) {
          return file.type.startsWith(type.slice(0, -2));
        }
        return file.type === type || file.name.endsWith(type);
      });
      
      if (!isAccepted) {
        return `File type not accepted. Allowed: ${accept}`;
      }
    }

    return null;
  };

  const addFiles = useCallback((newFiles: File[]) => {
    if (disabled) return;

    // Check max files limit
    if (files.length + newFiles.length > maxFiles) {
      toast.error(`Maximum ${maxFiles} files allowed`);
      return;
    }

    const validFiles: FileItem[] = [];
    const errors: string[] = [];

    newFiles.forEach(file => {
      const error = validateFile(file);
      if (error) {
        errors.push(`${file.name}: ${error}`);
      } else {
        validFiles.push({
          file,
          id: Math.random().toString(36).substring(2, 15),
          status: "pending",
          progress: 0
        });
      }
    });

    if (errors.length > 0) {
      toast.error(`Some files were rejected:\n${errors.join("\n")}`);
    }

    if (validFiles.length > 0) {
      setFiles(prev => [...prev, ...validFiles]);
    }
  }, [files.length, maxFiles, maxSize, accept, disabled]);

  const uploadFile = async (fileItem: FileItem) => {
    try {
      setFiles(prev => 
        prev.map(f => 
          f.id === fileItem.id 
            ? { ...f, status: "uploading", progress: 0 }
            : f
        )
      );

      // Generate upload URL
      const uploadUrl = await generateUploadUrl();

      // Upload file with progress tracking
      const response = await fetch(uploadUrl, {
        method: "POST",
        headers: { "Content-Type": fileItem.file.type },
        body: fileItem.file,
      });

      if (!response.ok) {
        throw new Error("Upload failed");
      }

      const { storageId } = await response.json();

      // Save metadata
      await saveFileMetadata({
        storageId,
        filename: fileItem.file.name,
        contentType: fileItem.file.type,
        size: fileItem.file.size
      });

      setFiles(prev => 
        prev.map(f => 
          f.id === fileItem.id 
            ? { ...f, status: "success", progress: 100, storageId }
            : f
        )
      );

      return storageId;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Upload failed";
      
      setFiles(prev => 
        prev.map(f => 
          f.id === fileItem.id 
            ? { ...f, status: "error", error: errorMessage }
            : f
        )
      );
      
      throw error;
    }
  };

  const uploadAllFiles = async () => {
    if (disabled) return;

    const pendingFiles = files.filter(f => f.status === "pending");
    if (pendingFiles.length === 0) return;

    try {
      const uploadPromises = pendingFiles.map(uploadFile);
      const storageIds = await Promise.all(uploadPromises);
      
      toast.success(`Successfully uploaded ${storageIds.length} files`);
      onUploadComplete?.(storageIds.filter(Boolean));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Upload failed";
      toast.error(`Upload failed: ${errorMessage}`);
      onUploadError?.(errorMessage);
    }
  };

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const clearAllFiles = () => {
    setFiles([]);
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setIsDragOver(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    if (disabled) return;

    const droppedFiles = Array.from(e.dataTransfer.files);
    addFiles(droppedFiles);
  }, [addFiles, disabled]);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFiles = Array.from(e.target.files);
      addFiles(selectedFiles);
      // Reset input
      e.target.value = "";
    }
  };

  const successfulUploads = files.filter(f => f.status === "success").length;
  const hasErrors = files.some(f => f.status === "error");
  const isUploading = files.some(f => f.status === "uploading");
  const pendingUploads = files.filter(f => f.status === "pending").length;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Drop zone */}
      <div
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center transition-colors
          ${isDragOver 
            ? "border-primary bg-primary/5" 
            : "border-muted-foreground/25 hover:border-muted-foreground/50"
          }
          ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
        `}
        onClick={() => !disabled && fileInputRef.current?.click()}
      >
        <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">
          {isDragOver ? "Drop files here" : "Upload Files"}
        </h3>
        <p className="text-muted-foreground mb-4">
          Drag and drop files here, or click to select files
        </p>
        <div className="text-sm text-muted-foreground space-y-1">
          <p>Accepted: {accept === "*" ? "All files" : accept}</p>
          <p>Max size: {maxSize}MB per file</p>
          <p>Max files: {maxFiles}</p>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={handleFileSelect}
          className="hidden"
          disabled={disabled}
        />
      </div>

      {/* Files list */}
      {files.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-medium">
              Files ({files.length})
            </h4>
            <div className="flex gap-2">
              {pendingUploads > 0 && (
                <Button
                  size="sm"
                  onClick={uploadAllFiles}
                  disabled={isUploading || disabled}
                >
                  Upload {pendingUploads} files
                </Button>
              )}
              <Button
                size="sm"
                variant="outline"
                onClick={clearAllFiles}
                disabled={isUploading || disabled}
              >
                Clear all
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            {files.map((fileItem) => {
              const Icon = getFileIcon(fileItem.file.type);
              
              return (
                <div
                  key={fileItem.id}
                  className="flex items-center gap-3 p-3 border rounded-lg bg-muted/30"
                >
                  <Icon className="h-5 w-5 text-muted-foreground" />
                  
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">
                      {fileItem.file.name}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {formatFileSize(fileItem.file.size)}
                    </p>
                    
                    {fileItem.status === "uploading" && (
                      <Progress value={fileItem.progress} className="mt-2" />
                    )}
                    
                    {fileItem.error && (
                      <p className="text-sm text-destructive mt-1">
                        {fileItem.error}
                      </p>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={
                        fileItem.status === "success" ? "default" :
                        fileItem.status === "error" ? "destructive" :
                        fileItem.status === "uploading" ? "secondary" :
                        "outline"
                      }
                      className="text-xs"
                    >
                      {fileItem.status === "success" && <CheckCircle className="h-3 w-3 mr-1" />}
                      {fileItem.status === "error" && <AlertCircle className="h-3 w-3 mr-1" />}
                      {fileItem.status}
                    </Badge>
                    
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeFile(fileItem.id)}
                      disabled={fileItem.status === "uploading"}
                      className="h-8 w-8 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Summary */}
          {(successfulUploads > 0 || hasErrors) && (
            <div className="flex items-center gap-4 text-sm">
              {successfulUploads > 0 && (
                <span className="text-green-600 flex items-center gap-1">
                  <CheckCircle className="h-4 w-4" />
                  {successfulUploads} uploaded
                </span>
              )}
              {hasErrors && (
                <span className="text-destructive flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  {files.filter(f => f.status === "error").length} failed
                </span>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}