"use client";

import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";

interface ConfirmDialogProps {
  open: boolean;
  title: string;
  description?: string;
  checkboxLabel?: string;
  confirmLabel?: string;
  cancelLabel?: string;
  loading?: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

export function ConfirmDialog({
  open,
  title,
  description,
  checkboxLabel = "I understand this action cannot be undone",
  confirmLabel = "Confirm",
  cancelLabel = "Cancel",
  loading = false,
  onConfirm,
  onCancel,
}: ConfirmDialogProps) {
  const [checked, setChecked] = useState(false);

  if (!open) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/40 backdrop-blur-sm"
      role="dialog"
      aria-modal="true"
    >
      <Card className="w-full max-w-md p-6 shadow-xl border-2">
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">{title}</h3>
            {description && (
              <p className="mt-1 text-sm text-muted-foreground">{description}</p>
            )}
          </div>

          <label className="flex items-center gap-2 text-sm">
            <Checkbox
              aria-label="confirm-checkbox"
              checked={checked}
              onCheckedChange={(v: any) => {
                const next = Boolean(v);
                console.log("[ConfirmDialog] Checkbox toggled:", next);
                setChecked(next);
              }}
            />
            {checkboxLabel}
          </label>

          <div className="flex justify-end gap-2 pt-2">
            <Button
              variant="outline"
              onClick={() => {
                console.log("[ConfirmDialog] Cancel clicked");
                setChecked(false);
                onCancel();
              }}
            >
              {cancelLabel}
            </Button>
            <Button
              variant="destructive"
              disabled={!checked || loading}
              onClick={() => {
                console.log("[ConfirmDialog] Confirm clicked, proceeding...", {
                  checked,
                  loading,
                });
                onConfirm();
                setChecked(false);
              }}
            >
              {loading ? "Processing..." : confirmLabel}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}

