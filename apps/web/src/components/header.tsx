"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useQuery } from "convex/react";
import { api } from "@/../../../convex/_generated/api";
import { AuthButton } from "@/components/auth/auth-button";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { IBCLogo } from "@/components/ui/ibc-logo";
import { BarChart3, Zap, Shield, Folder, LogIn } from "lucide-react";
import { GlobalSearch } from "@/components/search/GlobalSearch";
import { HamburgerMenu } from "@/components/navigation/HamburgerMenu";
import { useClerk, useUser } from "@clerk/nextjs";

export default function Header() {
  const pathname = usePathname();
  const user = useQuery(api.auth.getCurrentUser, {});
  const userLoading = user === undefined;
  const { user: clerkUser, isLoaded } = useUser();
  const { openSignIn } = useClerk();

  // Get user profile to check if admin (only if user is authenticated)
  const userProfile = useQuery(api.auth.getUserProfile, {});

  const isAdmin = userProfile?.role === 'admin';

  // Determine which page we're on
  const isLandingPage = pathname === '/';
  const isDashboardPage = pathname === '/dashboard';
  const isGroupsPage = pathname?.startsWith('/groups');

  console.log('Header: Current pathname:', pathname, 'isLandingPage:', isLandingPage, 'isDashboardPage:', isDashboardPage);
  
  return (
    <header className="sticky top-0 z-50 w-full glass-effect border-b border-border/30">
      <div className="content-container">
        <div className="center-all justify-between h-20">
          {/* Brand */}
          <Link href="/" className="hover:opacity-90 transition-opacity duration-300 flex items-center">
            <IBCLogo className="text-ibc-green" width={100} height={45} />
          </Link>

          {/* Right Navigation */}
          <div className="flex items-center gap-3">
            {/* Search */}
            <GlobalSearch />

            {/* Dashboard Button */}
            <Link href="/dashboard">
              <Button variant="default" className="flex items-center space-x-2">
                <BarChart3 className="h-4 w-4" />
                <span className="hidden sm:inline">Dashboard</span>
              </Button>
            </Link>

            {/* Sign In Button - Show when user is not authenticated */}
            {isLoaded && !clerkUser && (
              <Button
                variant="outline"
                onClick={() => openSignIn()}
                className="flex items-center space-x-2"
              >
                <LogIn className="h-4 w-4" />
                <span>Sign In</span>
              </Button>
            )}

            {/* Hamburger Menu - Far Right */}
            <HamburgerMenu />
          </div>
        </div>
      </div>
    </header>
  );
}
