import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export function TestimonialCardSkeleton({ withImage = true }: { withImage?: boolean }) {
  return (
    <Card className="p-6 h-full">
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <div className="space-y-2 w-full max-w-[80%]">
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-4 rounded" />
              <Skeleton className="h-4 w-32" />
            </div>
            <Skeleton className="h-5 w-10/12" />
            <div className="flex gap-1">
              <Skeleton className="h-3 w-20" />
              <Skeleton className="h-3 w-16" />
            </div>
          </div>
          <Skeleton className="h-8 w-8 rounded-md" />
        </div>

        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-11/12" />
          <Skeleton className="h-4 w-9/12" />
        </div>

        {withImage && (
          <div className="relative">
            <Skeleton className="aspect-video w-full rounded-lg" />
          </div>
        )}

        <div className="flex items-center justify-between pt-2">
          <Skeleton className="h-4 w-28" />
          <Skeleton className="h-6 w-24 rounded-full" />
        </div>
      </div>
    </Card>
  );
}

