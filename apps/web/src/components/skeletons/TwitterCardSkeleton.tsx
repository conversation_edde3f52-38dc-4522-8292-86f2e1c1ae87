import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export function TwitterCardSkeleton() {
  return (
    <Card className="p-5 sm:p-6 h-full">
      <div className="space-y-3">
        <div className="flex items-start gap-2">
          <Skeleton className="h-5 w-5 rounded" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-5 w-10/12" />
            <Skeleton className="h-4 w-7/12" />
          </div>
        </div>
        <div className="flex flex-wrap gap-2 min-h-[24px]">
          <Skeleton className="h-5 w-16 rounded-full" />
          <Skeleton className="h-5 w-20 rounded-full" />
          <Skeleton className="h-5 w-14 rounded-full" />
        </div>
        <div className="grid grid-cols-3 divide-x">
          <div className="px-3 text-center space-y-2">
            <Skeleton className="h-3 w-10 mx-auto" />
            <Skeleton className="h-5 w-14 mx-auto" />
          </div>
          <div className="px-3 text-center space-y-2">
            <Skeleton className="h-3 w-12 mx-auto" />
            <Skeleton className="h-5 w-14 mx-auto" />
          </div>
          <div className="px-3 text-center space-y-2">
            <Skeleton className="h-3 w-16 mx-auto" />
            <Skeleton className="h-5 w-14 mx-auto" />
          </div>
        </div>
        <div className="flex items-center justify-between pt-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-8 w-20 rounded-md" />
        </div>
      </div>
    </Card>
  );
}

