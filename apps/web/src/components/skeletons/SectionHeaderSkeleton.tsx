import { Skeleton } from "@/components/ui/skeleton";

export function SectionHeaderSkeleton({ withBadge = true }: { withBadge?: boolean }) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-3 w-full max-w-xl">
        <Skeleton className="h-6 w-6 rounded-md" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-5 w-2/3" />
          <Skeleton className="h-3 w-1/3" />
        </div>
      </div>
      {withBadge && <Skeleton className="h-6 w-20 rounded-full" />}
    </div>
  );
}

