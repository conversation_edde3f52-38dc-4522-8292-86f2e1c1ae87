import type { Id } from "@/../../../convex/_generated/dataModel";

export interface ChartPoint {
  date: string;
  value: number;
}

export type ChartSeriesVariant = "line" | "area";

export interface ChartSeries {
  id: string;
  label: string;
  color_token?: string;
  stroke_color?: string;
  variant?: ChartSeriesVariant;
  fill_opacity?: number;
  points: ChartPoint[];
}

export interface LegendItem {
  label: string;
  color_token?: string;
  stroke_color?: string;
}

export interface MarketingCaseStudy {
  _id: Id<"marketing_case_studies">;
  _creationTime: number;
  slug: string;
  title: string;
  summary?: string;
  cta_label?: string;
  cta_url: string;
  chart_series: ChartSeries[];
  legend?: LegendItem[];
  tags?: string[];
  is_featured?: boolean;
  is_published?: boolean;
  display_order?: number;
  created_at: number;
  updated_at: number;
  created_by?: string;
  updated_by?: string;
  background_style?: string;
}

