export interface ContentFormData {
  content_link: string;
  content_tags: string[];
  host: string;
  content_account: string[];
  content_created_date: string;
  content_types: string[];
  twitter_content_type: string | null;
  content_views: number;
  content_listeners: number;
  twitter_impressions: number;
  content_follow_increase: number;
  content_title: string | null;
  content_description: string | null;
  content_categories: string[];
}

export type FormErrors = Partial<Record<keyof ContentFormData, string>>;

export interface Filters {
  contentTypes: string[];
  categories: string[];
  sortBy: string;
  sortOrder: string;
}

export interface FieldErrorProps {
  error?: string;
}

export interface MultiSelectDropdownProps {
  label: string;
  value: string[];
  options: readonly string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  onAddNew?: (newValue: string) => Promise<void>;
  addNewLabel?: string;
  canAddNew?: boolean;
}

export interface DatePickerProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  required?: boolean;
  error?: string;
}

export interface SingleSelectDropdownProps {
  label: string;
  value: string | null;
  options: readonly string[];
  onChange: (value: string | null) => void;
  placeholder?: string;
  allowNull?: boolean;
}

export interface MetricInputProps {
  label: string;
  value: number;
  onChange: (value: number) => void;
  placeholder?: string;
  id?: string;
  error?: string;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  totalItems: number;
  itemsPerPage: number;
}