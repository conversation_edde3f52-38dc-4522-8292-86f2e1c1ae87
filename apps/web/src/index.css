@import "tailwindcss";
@import "tw-animate-css";

/* UploadThing styling integration for Tailwind CSS v4 */
@import "uploadthing/tw/v4";
@source "../node_modules/@uploadthing/react/dist";

/* Custom animations for hamburger menu */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --font-sans: "<PERSON>", "<PERSON>eist", ui-sans-serif, system-ui, sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

html,
body {
  @apply bg-white dark:bg-gray-950;

  @media (prefers-color-scheme: dark) {
    color-scheme: dark;
  }
}

:root {
  --radius: 0.625rem;
  
  /* Tech Vibes Color Palette */
  /* Background: #fffffe → Light off-white */
  --background: oklch(99.9% 0.001 0);
  --foreground: oklch(27% 0.05 250); /* #272343 → Headline dark blue */
  
  /* Cards and surfaces */
  --card: oklch(99.9% 0.001 0); /* Same as background */
  --card-foreground: oklch(27% 0.05 250);
  
  /* Popovers */
  --popover: oklch(99.9% 0.001 0);
  --popover-foreground: oklch(27% 0.05 250);
  
  /* Primary - Using the bright yellow accent */
  --primary: oklch(88% 0.15 95); /* #ffd803 → Bright yellow */
  --primary-foreground: oklch(27% 0.05 250); /* Dark blue text on yellow */
  
  /* Secondary - Using the light mint */
  --secondary: oklch(95% 0.02 180); /* #e3f6f5 → Light mint */
  --secondary-foreground: oklch(27% 0.05 250);
  
  /* Muted areas */
  --muted: oklch(95% 0.02 180); /* Light mint for muted areas */
  --muted-foreground: oklch(35% 0.04 245); /* #2d334a → Paragraph color */
  
  /* Accent - Tertiary teal */
  --accent: oklch(85% 0.08 180); /* #bae8e8 → Light teal */
  --accent-foreground: oklch(27% 0.05 250);
  
  /* Destructive */
  --destructive: oklch(58% 0.2 25);
  
  /* Borders and inputs */
  --border: oklch(90% 0.02 180); /* Subtle teal tint */
  --input: oklch(90% 0.02 180);
  --ring: oklch(88% 0.15 95); /* Yellow focus ring */
  
  /* Chart colors - tech vibes palette */
  --chart-1: oklch(88% 0.15 95); /* Yellow */
  --chart-2: oklch(85% 0.08 180); /* Light teal */
  --chart-3: oklch(27% 0.05 250); /* Dark blue */
  --chart-4: oklch(95% 0.02 180); /* Light mint */
  --chart-5: oklch(35% 0.04 245); /* Dark gray-blue */
  
  /* Custom light blue for ATTENTION text */
  --attention-blue: oklch(65% 0.15 240); /* Light blue */
  
  /* IBC Brand Colors */
  --ibc-green: oklch(55% 0.2 140); /* #00c729 → IBC Green */
  --ibc-green-light: oklch(85% 0.15 140); /* Light IBC Green */
  --ibc-green-dark: oklch(35% 0.25 140); /* Dark IBC Green */
  
  /* Sidebar */
  --sidebar: oklch(99.9% 0.001 0);
  --sidebar-foreground: oklch(27% 0.05 250);
  --sidebar-primary: oklch(88% 0.15 95); /* Yellow accent */
  --sidebar-primary-foreground: oklch(27% 0.05 250);
  --sidebar-accent: oklch(95% 0.02 180);
  --sidebar-accent-foreground: oklch(27% 0.05 250);
  --sidebar-border: oklch(90% 0.02 180);
  --sidebar-ring: oklch(88% 0.15 95);
}

.dark {
  /* Rich dark mode with better contrast */
  --background: oklch(8% 0.01 250); /* Much darker background */
  --foreground: oklch(98% 0.01 0); /* Pure white text */
  
  --card: oklch(12% 0.02 250); /* Dark cards with subtle blue tint */
  --card-foreground: oklch(95% 0.01 0);
  
  --popover: oklch(10% 0.02 250);
  --popover-foreground: oklch(98% 0.01 0);
  
  /* Bright yellow accent for dark mode */
  --primary: oklch(85% 0.18 95); /* More vibrant yellow */
  --primary-foreground: oklch(10% 0.02 250);
  
  --secondary: oklch(18% 0.03 250); /* Dark blue-teal */
  --secondary-foreground: oklch(90% 0.02 180);
  
  --muted: oklch(15% 0.02 250);
  --muted-foreground: oklch(65% 0.02 0); /* Neutral gray */
  
  --accent: oklch(75% 0.12 180); /* Bright teal accent */
  --accent-foreground: oklch(10% 0.02 250);
  
  --destructive: oklch(68% 0.22 25);
  
  --border: oklch(22% 0.02 250);
  --input: oklch(15% 0.02 250);
  --ring: oklch(85% 0.18 95);
  
  /* Vibrant dark mode chart colors */
  --chart-1: oklch(85% 0.18 95); /* Bright yellow */
  --chart-2: oklch(75% 0.12 180); /* Bright teal */
  --chart-3: oklch(70% 0.15 250); /* Bright blue */
  --chart-4: oklch(80% 0.10 160); /* Light teal */
  --chart-5: oklch(78% 0.08 200); /* Cyan */
  
  /* Custom light blue for ATTENTION text - same in dark mode */
  --attention-blue: oklch(70% 0.15 240); /* Slightly brighter in dark mode */
  
  /* IBC Brand Colors - Dark Mode */
  --ibc-green: oklch(65% 0.22 140); /* Brighter IBC Green for dark mode */
  --ibc-green-light: oklch(85% 0.15 140); /* Light IBC Green */
  --ibc-green-dark: oklch(25% 0.3 140); /* Darker IBC Green */
  
  --sidebar: oklch(10% 0.02 250);
  --sidebar-foreground: oklch(95% 0.01 0);
  --sidebar-primary: oklch(85% 0.18 95);
  --sidebar-primary-foreground: oklch(10% 0.02 250);
  --sidebar-accent: oklch(18% 0.03 250);
  --sidebar-accent-foreground: oklch(90% 0.02 180);
  --sidebar-border: oklch(22% 0.02 250);
  --sidebar-ring: oklch(85% 0.18 95);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-attention-blue: var(--attention-blue);
  --color-ibc-green: var(--ibc-green);
  --color-ibc-green-light: var(--ibc-green-light);
  --color-ibc-green-dark: var(--ibc-green-dark);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Responsive container with perfect centering */
  .content-container {
    width: 100%;
    max-width: 1320px; /* widen layout for richer grids */
    margin: 0 auto;
    padding: 0 1rem;
  }
  
  @media (min-width: 640px) {
    .content-container {
      padding: 0 1.5rem;
    }
  }
  
  @media (min-width: 1024px) {
    .content-container {
      padding: 0 2rem;
    }
  }
  
  /* Perfect centering utility */
  .center-all {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* Tech vibes gradient backgrounds */
  .gradient-tech {
    background: linear-gradient(135deg, 
      oklch(99.9% 0.001 0) 0%, 
      oklch(95% 0.02 180) 100%);
  }
  
  .dark .gradient-tech {
    background: linear-gradient(135deg, 
      oklch(8% 0.01 250) 0%, 
      oklch(12% 0.02 250) 100%);
  }
  
  /* Modern glass effect with better dark mode */
  .glass-effect {
    background: oklch(99.9% 0.001 0 / 85%);
    backdrop-filter: blur(16px);
    border: 1px solid oklch(90% 0.02 180 / 20%);
  }
  
  .dark .glass-effect {
    background: oklch(12% 0.02 250 / 90%);
    backdrop-filter: blur(16px);
    border: 1px solid oklch(22% 0.02 250 / 40%);
  }
  
  /* Tech-themed animations */
  .pulse-tech {
    animation: pulse-tech 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  @keyframes pulse-tech {
    0%, 100% {
      box-shadow: 0 0 0 0 oklch(88% 0.15 95 / 60%);
    }
    50% {
      box-shadow: 0 0 0 12px oklch(88% 0.15 95 / 0%);
    }
  }
  
  /* Enhanced card hover effects */
  .card-hover {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .card-hover:hover {
    transform: translateY(-4px) scale(1.01);
    box-shadow: 0 20px 40px oklch(27% 0.05 250 / 15%);
  }
  
  .dark .card-hover:hover {
    box-shadow: 0 20px 40px oklch(0% 0 0 / 30%);
  }
  
  /* Better responsive text truncation */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* Custom styled scrollbar */
  .scrollbar-styled {
    scrollbar-width: thin;
    scrollbar-color: var(--muted) var(--background);
  }
  
  .scrollbar-styled::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-styled::-webkit-scrollbar-track {
    background: var(--background);
    border-radius: 3px;
  }
  
  .scrollbar-styled::-webkit-scrollbar-thumb {
    background: var(--muted);
    border-radius: 3px;
  }
  
  .scrollbar-styled::-webkit-scrollbar-thumb:hover {
    background: var(--muted-foreground);
  }

  /* ===== TESTIMONIALS CAROUSEL STYLES ===== */
  
  /* Carousel Card Animations */
  .carousel-card-wrapper {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Enhanced Card Design */
  .carousel-card {
    background: oklch(99.9% 0.001 0 / 95%);
    backdrop-filter: blur(20px);
    border: 1px solid oklch(90% 0.02 180 / 30%);
    box-shadow: 
      0 4px 6px -1px oklch(27% 0.05 250 / 10%),
      0 2px 4px -1px oklch(27% 0.05 250 / 6%),
      0 20px 25px -5px oklch(27% 0.05 250 / 10%);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .dark .carousel-card {
    background: oklch(12% 0.02 250 / 95%);
    backdrop-filter: blur(20px);
    border: 1px solid oklch(22% 0.02 250 / 50%);
    box-shadow: 
      0 4px 6px -1px oklch(0% 0 0 / 30%),
      0 2px 4px -1px oklch(0% 0 0 / 20%),
      0 20px 25px -5px oklch(0% 0 0 / 40%);
  }
  
  .carousel-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
      0 10px 15px -3px oklch(27% 0.05 250 / 20%),
      0 4px 6px -2px oklch(27% 0.05 250 / 10%),
      0 30px 40px -10px oklch(27% 0.05 250 / 20%);
  }
  
  .dark .carousel-card:hover {
    box-shadow: 
      0 10px 15px -3px oklch(0% 0 0 / 50%),
      0 4px 6px -2px oklch(0% 0 0 / 30%),
      0 30px 40px -10px oklch(0% 0 0 / 60%);
  }
  
  /* Gradient Animation */
  @keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
  
  .animate-gradient-shift {
    animation: gradient-shift 3s ease infinite;
  }
  
  /* Shimmer Loading Effect */
  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }
  
  .animate-shimmer {
    animation: shimmer 2s infinite;
    background: linear-gradient(
      90deg,
      transparent,
      oklch(100% 0 0 / 20%),
      transparent
    );
  }
  
  /* Enhanced Carousel Navigation Buttons */
  .carousel-nav-button {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: oklch(99.9% 0.001 0 / 95%);
    backdrop-filter: blur(16px);
    border: 2px solid oklch(90% 0.02 180 / 20%);
    box-shadow: 
      0 8px 32px oklch(0% 0 0 / 12%),
      0 4px 16px oklch(0% 0 0 / 8%),
      inset 0 1px 0 oklch(100% 0 0 / 20%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
  }
  
  .dark .carousel-nav-button {
    background: oklch(8% 0.02 250 / 95%);
    border: 2px solid oklch(22% 0.02 250 / 30%);
    box-shadow: 
      0 8px 32px oklch(0% 0 0 / 40%),
      0 4px 16px oklch(0% 0 0 / 20%),
      inset 0 1px 0 oklch(100% 0 0 / 10%);
  }
  
  .carousel-nav-button:hover {
    transform: scale(1.1) translateY(-2px);
    background: oklch(99.9% 0.001 0);
    border-color: oklch(88% 0.15 95 / 40%);
    box-shadow: 
      0 12px 48px oklch(0% 0 0 / 20%),
      0 8px 24px oklch(0% 0 0 / 12%),
      0 0 0 1px oklch(88% 0.15 95 / 20%),
      inset 0 1px 0 oklch(100% 0 0 / 30%);
  }
  
  .dark .carousel-nav-button:hover {
    background: oklch(12% 0.02 250);
    border-color: oklch(88% 0.15 95 / 40%);
    box-shadow: 
      0 12px 48px oklch(0% 0 0 / 60%),
      0 8px 24px oklch(0% 0 0 / 30%),
      0 0 0 1px oklch(88% 0.15 95 / 30%),
      inset 0 1px 0 oklch(100% 0 0 / 15%);
  }
  
  .carousel-nav-button:active {
    transform: scale(1.05) translateY(-1px);
    transition-duration: 0.1s;
  }
  
  .carousel-nav-button:focus-visible {
    outline: none;
    box-shadow: 
      0 0 0 3px oklch(88% 0.15 95 / 40%),
      0 8px 32px oklch(0% 0 0 / 12%),
      0 4px 16px oklch(0% 0 0 / 8%);
  }
  
  /* Carousel Dots - Removed old styles, now using Tailwind inline classes */
  
  /* Testimonials Section Animations */
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
  
  .testimonials-decoration {
    animation: float 6s ease-in-out infinite;
  }
  
  .testimonials-decoration-delayed {
    animation: float 6s ease-in-out 2s infinite;
  }
  
  /* Section Title Gradient */
  .testimonials-title {
    background: linear-gradient(
      135deg,
      var(--foreground) 0%,
      var(--primary) 50%,
      var(--accent) 100%
    );
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-shift 4s ease infinite;
  }
  
  /* Premium Glass Badge */
  .testimonials-badge {
    background: linear-gradient(
      135deg,
      oklch(99.9% 0.001 0 / 10%),
      oklch(88% 0.15 95 / 20%)
    );
    backdrop-filter: blur(10px);
    border: 1px solid oklch(88% 0.15 95 / 30%);
  }
  
  .dark .testimonials-badge {
    background: linear-gradient(
      135deg,
      oklch(12% 0.02 250 / 20%),
      oklch(88% 0.15 95 / 30%)
    );
    border: 1px solid oklch(88% 0.15 95 / 40%);
  }
  
  /* Parallax Effect for Background Elements */
  @keyframes parallax-float {
    0% { transform: translate(0, 0) rotate(0deg); }
    33% { transform: translate(30px, -30px) rotate(120deg); }
    66% { transform: translate(-20px, 20px) rotate(240deg); }
    100% { transform: translate(0, 0) rotate(360deg); }
  }
  
  .parallax-element {
    animation: parallax-float 20s ease-in-out infinite;
  }
  
  /* Responsive Adjustments */
  @media (max-width: 640px) {
    .carousel-nav-button {
      width: 40px;
      height: 40px;
    }
    
    .carousel-card {
      box-shadow: 
        0 2px 4px -1px oklch(27% 0.05 250 / 10%),
        0 1px 2px -1px oklch(27% 0.05 250 / 6%);
    }
  }
  
  /* Entrance Animation for Section */
  @keyframes section-fade-in {
    from {
      opacity: 0;
      transform: translateY(40px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .testimonials-section-animate {
    animation: section-fade-in 0.8s ease-out;
  }

  /* ===== REFLECTIVE MEDIA CARDS STYLES ===== */
  
  /* Advanced Reflective Glass Card */
  .reflective-card {
    position: relative;
    background: linear-gradient(
      135deg,
      oklch(99.9% 0.001 0 / 90%) 0%,
      oklch(95% 0.02 180 / 85%) 50%,
      oklch(99.9% 0.001 0 / 95%) 100%
    );
    backdrop-filter: blur(20px) saturate(1.8);
    border: 1px solid oklch(90% 0.02 180 / 30%);
    box-shadow: 
      0 8px 32px oklch(0% 0 0 / 15%),
      0 2px 8px oklch(0% 0 0 / 10%),
      inset 0 1px 0 oklch(100% 0 0 / 40%),
      inset 0 -1px 0 oklch(0% 0 0 / 10%);
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    overflow: hidden;
  }
  
  .dark .reflective-card {
    background: linear-gradient(
      135deg,
      oklch(12% 0.02 250 / 95%) 0%,
      oklch(15% 0.03 250 / 90%) 50%,
      oklch(8% 0.01 250 / 95%) 100%
    );
    backdrop-filter: blur(20px) saturate(1.8);
    border: 1px solid oklch(22% 0.02 250 / 50%);
    box-shadow: 
      0 8px 32px oklch(0% 0 0 / 40%),
      0 2px 8px oklch(0% 0 0 / 20%),
      inset 0 1px 0 oklch(100% 0 0 / 15%),
      inset 0 -1px 0 oklch(0% 0 0 / 20%);
  }
  
  /* Reflection Shine Effect */
  .reflective-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent 0%,
      oklch(100% 0 0 / 20%) 50%,
      transparent 100%
    );
    transition: left 0.8s cubic-bezier(0.23, 1, 0.32, 1);
    z-index: 1;
    pointer-events: none;
  }
  
  .reflective-card:hover::before {
    left: 100%;
  }
  
  /* Enhanced Hover Effects */
  .reflective-card:hover {
    transform: translateY(-8px) scale(1.02) rotateX(2deg);
    box-shadow: 
      0 20px 60px oklch(0% 0 0 / 25%),
      0 8px 24px oklch(0% 0 0 / 15%),
      0 0 0 1px oklch(85% 0.18 95 / 30%),
      inset 0 1px 0 oklch(100% 0 0 / 60%),
      inset 0 -1px 0 oklch(0% 0 0 / 15%);
    border-color: oklch(85% 0.18 95 / 40%);
  }
  
  .dark .reflective-card:hover {
    box-shadow: 
      0 20px 60px oklch(0% 0 0 / 60%),
      0 8px 24px oklch(0% 0 0 / 30%),
      0 0 0 1px oklch(85% 0.18 95 / 40%),
      inset 0 1px 0 oklch(100% 0 0 / 20%),
      inset 0 -1px 0 oklch(0% 0 0 / 30%);
  }
  
  /* Profile Avatar with Glow */
  .profile-avatar {
    position: relative;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid oklch(85% 0.18 95 / 60%);
    box-shadow: 
      0 4px 12px oklch(0% 0 0 / 20%),
      0 0 0 1px oklch(85% 0.18 95 / 30%),
      inset 0 1px 0 oklch(100% 0 0 / 30%);
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  }
  
  .profile-avatar:hover {
    transform: scale(1.1);
    border-color: oklch(85% 0.18 95 / 80%);
    box-shadow: 
      0 8px 24px oklch(0% 0 0 / 30%),
      0 0 0 1px oklch(85% 0.18 95 / 50%),
      0 0 20px oklch(85% 0.18 95 / 40%),
      inset 0 1px 0 oklch(100% 0 0 / 50%);
  }
  
  /* Media Card Grid Enhancement - Simplified */
  .media-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    width: 100%;
    padding: 0;
  }
  
  @media (max-width: 639px) {
    .media-cards-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
      padding: 0 0.5rem;
    }
  }
  
  @media (min-width: 640px) and (max-width: 1023px) {
    .media-cards-grid {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
    }
  }
  
  @media (min-width: 1024px) {
    .media-cards-grid {
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;
    }
  }
  
  /* Individual Card Sizing - Simplified */
  .media-card-wrapper {
    width: 100%;
    min-height: 280px;
    display: flex;
    flex-direction: column;
  }
  
  .media-card-wrapper a {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  @media (max-width: 639px) {
    .media-card-wrapper {
      min-height: 240px;
    }
  }
  
  /* Content Animation on Hover */
  .media-card-content {
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
    position: relative;
    z-index: 2;
  }
  
  .reflective-card:hover .media-card-content {
    transform: translateY(-2px);
  }
  
  /* Enhanced Metrics Badge */
  .metrics-badge {
    background: linear-gradient(
      135deg,
      oklch(99.9% 0.001 0 / 90%) 0%,
      oklch(95% 0.02 180 / 85%) 100%
    );
    backdrop-filter: blur(12px);
    border: 1px solid oklch(90% 0.02 180 / 40%);
    box-shadow: 
      0 2px 8px oklch(0% 0 0 / 10%),
      inset 0 1px 0 oklch(100% 0 0 / 50%);
    border-radius: 0.5rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    transition: all 0.3s ease;
  }
  
  .dark .metrics-badge {
    background: linear-gradient(
      135deg,
      oklch(15% 0.02 250 / 90%) 0%,
      oklch(12% 0.02 250 / 95%) 100%
    );
    border: 1px solid oklch(22% 0.02 250 / 60%);
    box-shadow: 
      0 2px 8px oklch(0% 0 0 / 20%),
      inset 0 1px 0 oklch(100% 0 0 / 20%);
  }
  
  .reflective-card:hover .metrics-badge {
    transform: scale(1.05);
    box-shadow: 
      0 4px 12px oklch(0% 0 0 / 15%),
      inset 0 1px 0 oklch(100% 0 0 / 60%);
  }
  
  .dark .reflective-card:hover .metrics-badge {
    box-shadow: 
      0 4px 12px oklch(0% 0 0 / 30%),
      inset 0 1px 0 oklch(100% 0 0 / 25%);
  }

  /* Mobile-specific adjustments */
  @media (max-width: 480px) {
    .reflective-card {
      backdrop-filter: blur(16px) saturate(1.5);
    }
    
    .reflective-card:hover {
      transform: translateY(-4px) scale(1.01);
    }
    
    .profile-avatar {
      width: 36px;
      height: 36px;
    }
  }
  
  @media (min-width: 481px) and (max-width: 640px) {
    .profile-avatar {
      width: 40px;
      height: 40px;
    }
  }
  
  @media (min-width: 641px) and (max-width: 1023px) {
    .profile-avatar {
      width: 44px;
      height: 44px;
    }
  }
}
