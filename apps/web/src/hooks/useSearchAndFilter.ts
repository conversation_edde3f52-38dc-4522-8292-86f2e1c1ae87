import { useState, useCallback, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import type { Filters } from "@/types/admin";

export const useSearchAndFilter = () => {
  const searchParams = useSearchParams();
  
  // Initialize search state from URL parameters
  const [searchQuery, setSearchQuery] = useState(() => searchParams.get("q") || "");
  const [filters, setFilters] = useState<Filters>(() => ({
    contentTypes: searchParams.get("types")?.split(",").filter(Boolean) || [],
    categories: searchParams.get("categories")?.split(",").filter(Boolean) || [],
    sortBy: searchParams.get("sort") || "impressions",
    sortOrder: searchParams.get("order") || "desc"
  }));

  // Track if search is active
  const isSearchActive = searchQuery.length > 0 || filters.contentTypes.length > 0 || filters.categories.length > 0;

  // Update URL when search/filters change
  const updateURL = useCallback(() => {
    const params = new URLSearchParams();
    if (searchQuery) params.set("q", searchQuery);
    if (filters.contentTypes.length > 0) params.set("types", filters.contentTypes.join(","));
    if (filters.categories.length > 0) params.set("categories", filters.categories.join(","));
    if (filters.sortBy !== "impressions") params.set("sort", filters.sortBy);
    if (filters.sortOrder !== "desc") params.set("order", filters.sortOrder);

    const newURL = params.toString() ? `?${params.toString()}` : window.location.pathname;
    window.history.replaceState({}, "", newURL);
  }, [searchQuery, filters]);

  useEffect(() => {
    updateURL();
  }, [updateURL]);

  // Search handlers
  const handleSearch = (query: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [SEARCH] Search query:', query);
    }
    setSearchQuery(query);
  };

  const handleFilter = (filterType: string, values: string[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [SEARCH] Filter change:', filterType, values);
    }
    setFilters(prev => ({
      ...prev,
      [filterType]: values
    }));
  };

  const handleSort = (sortBy: string, sortOrder?: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [SEARCH] Sort change:', sortBy, sortOrder);
    }
    setFilters(prev => ({
      ...prev,
      sortBy,
      sortOrder: sortOrder || prev.sortOrder
    }));
  };

  return {
    searchQuery,
    filters,
    isSearchActive,
    handleSearch,
    handleFilter,
    handleSort,
    setSearchQuery,
    setFilters
  };
};