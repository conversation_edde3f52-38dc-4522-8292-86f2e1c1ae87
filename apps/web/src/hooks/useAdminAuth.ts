import { useQuery } from "convex/react";
import { api } from "@/../../../convex/_generated/api";

export const useAdminAuth = () => {
  // Use Convex for authentication
  const user = useQuery(api.auth.getCurrentUser, {});
  const userProfile = useQuery(api.auth.getUserProfile, {});
  
  const userLoading = user === undefined;
  const profileLoading = userProfile === undefined;

  // Comprehensive debugging (development only)
  if (process.env.NODE_ENV === 'development') {
    console.log("🔍 [ADMIN AUTH] ==================== ADMIN AUTH STATE ====================");
    console.log("🔍 [ADMIN AUTH] getCurrentUser query:", {
      hasUser: !!user,
      userEmail: user?.email,
      userId: user?._id,
      userLoading,
      userError: userLoading ? 'Loading...' : (!user ? 'No user' : 'User found')
    });

    console.log("🔍 [ADMIN AUTH] getUserProfile query:", {
      profileLoading,
      hasProfileData: !!userProfile,
      profileData: userProfile,
      userRole: userProfile?.role
    });

    // Additional debugging for profile data
    if (userProfile) {
      console.log("✅ [ADMIN AUTH] Profile data found:", userProfile);
    } else if (!profileLoading) {
      console.log("⚠️ [ADMIN AUTH] Profile query returned null (no error, no data)");
    }
  }

  const isLoading = userLoading || profileLoading;
  const isAuthenticated = !!user;
  const isAdmin = !!(userProfile && userProfile.role === 'admin');

  return {
    user,
    userProfile,
    isLoading,
    isAuthenticated,
    isAdmin,
    error: null // Convex doesn't return errors in the same way
  };
};