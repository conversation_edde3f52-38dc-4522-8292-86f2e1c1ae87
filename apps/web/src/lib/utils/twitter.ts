// Twitter utility helper functions
export interface TwitterContentData {
  _id?: string;
  content_link?: string;
  content_title?: string;
  content_description?: string;
  content_created_date?: number;
  twitter_impressions?: number;
  twitter_likes?: number;
  twitter_retweets?: number;
  content_views?: number;
  content_listeners?: number;
  twitter_content_type?: string | null;
  content_account?: string[];
}

export interface DashboardSettings {
  twitter_impressions_visible: boolean;
  twitter_likes_visible: boolean;
  twitter_retweets_visible: boolean;
  content_views_visible: boolean;
  content_listeners_visible?: boolean;
  content_created_date_visible: boolean;
}

/**
 * Determines if content is Twitter-related based on content type or link
 */
export function isTwitterContent(content: TwitterContentData): boolean {
  // Check for Twitter content type (both old and new formats)
  if (content.twitter_content_type) {
    return ['tweet', 'twitter_space', 'twitter_thread', 'retweet', 'space', 'interview', 'thread'].includes(content.twitter_content_type);
  }
  
  // Check for Twitter/X URLs - covers broadcasts, spaces, tweets, etc.
  if (content.content_link) {
    return content.content_link.includes('twitter.com') || content.content_link.includes('x.com');
  }
  
  return false;
}

/**
 * Gets the Twitter content type display name
 */
export function getTwitterContentTypeDisplay(type: string | null | undefined): string {
  if (!type) {
    return 'Twitter Content';
  }
  
  const typeMap: Record<string, string> = {
    'tweet': 'Tweet',
    'twitter_space': 'Twitter Space',
    'twitter_thread': 'Thread',
    'retweet': 'Retweet',
    'space': 'Twitter Space',
    'interview': 'Interview',
    'thread': 'Thread'
  };
  
  return typeMap[type] || 'Twitter Content';
}

/**
 * Formats large numbers for display (1.2K, 1.5M, etc.)
 */
export function formatMetricNumber(num: number | null | undefined): string {
  if (!num || num === 0) return '0';
  
  const absNum = Math.abs(num);
  
  if (absNum >= 1000000) {
    return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
  }
  if (absNum >= 1000) {
    return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
  }
  
  return num.toString();
}

/**
 * Formats date for display
 */
export function formatContentDate(timestamp: number | undefined): string {
  if (!timestamp) return 'Unknown';
  
  const date = new Date(timestamp);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) {
    return '1 day ago';
  } else if (diffDays <= 7) {
    return `${diffDays} days ago`;
  } else if (diffDays <= 30) {
    const weeks = Math.floor(diffDays / 7);
    return weeks === 1 ? '1 week ago' : `${weeks} weeks ago`;
  } else {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined 
    });
  }
}

/**
 * Gets metrics that should be displayed based on dashboard settings
 */
export function getVisibleMetrics(content: TwitterContentData, settings: DashboardSettings) {
  const metrics = [];

  if (settings.twitter_impressions_visible && content.twitter_impressions) {
    metrics.push({
      label: 'Impressions',
      value: formatMetricNumber(content.twitter_impressions),
      icon: 'eye' as const,
      color: 'text-emerald-600'
    });
  }

  if (settings.twitter_likes_visible && content.twitter_likes) {
    metrics.push({
      label: 'Likes',
      value: formatMetricNumber(content.twitter_likes),
      icon: 'heart' as const,
      color: 'text-red-600'
    });
  }

  if (settings.twitter_retweets_visible && content.twitter_retweets) {
    metrics.push({
      label: 'Retweets',
      value: formatMetricNumber(content.twitter_retweets),
      icon: 'repeat' as const,
      color: 'text-green-600'
    });
  }

  if (settings.content_views_visible && content.content_views) {
    metrics.push({
      label: 'Views',
      value: formatMetricNumber(content.content_views),
      icon: 'play' as const,
      color: 'text-blue-600'
    });
  }

  if (settings.content_listeners_visible && content.content_listeners) {
    metrics.push({
      label: 'Listeners',
      value: formatMetricNumber(content.content_listeners),
      icon: 'headphones' as const,
      color: 'text-purple-600'
    });
  }

  return metrics;
}

/**
 * Extracts hashtags from content title or description
 */
export function extractHashtags(text: string | undefined): string[] {
  if (!text) return [];
  
  const hashtags = text.match(/#[a-zA-Z0-9_]+/g);
  return hashtags ? hashtags.slice(0, 3) : []; // Limit to first 3 hashtags
}

/**
 * Gets the domain from a Twitter/X URL for display
 */
export function getTwitterDomain(url: string | undefined): string {
  if (!url) return 'twitter.com';
  
  if (url.includes('x.com')) return 'x.com';
  return 'twitter.com';
}

/**
 * Truncates content title to fit card display
 */
export function truncateTitle(title: string | undefined, maxLength: number = 60): string {
  if (!title) return 'Untitled Content';
  
  if (title.length <= maxLength) return title;
  return title.substring(0, maxLength - 3) + '...';
}

/**
 * Gets engagement rate as a percentage (likes + retweets) / impressions
 */
export function getEngagementRate(content: TwitterContentData): number | null {
  const { twitter_impressions, twitter_likes = 0, twitter_retweets = 0 } = content;
  
  if (!twitter_impressions || twitter_impressions === 0) return null;
  
  const engagement = twitter_likes + twitter_retweets;
  return Math.round((engagement / twitter_impressions) * 100 * 100) / 100; // Round to 2 decimal places
}

/**
 * Gets content type emoji for visual display
 */
export function getContentTypeEmoji(contentType?: string | null): string {
  if (!contentType) return '';
  
  const type = contentType.toLowerCase();
  switch (type) {
    case 'tweet':
    case 'twitter_thread':
    case 'thread':
      return '💬';
    case 'twitter_space':
    case 'space':
      return '🎙️';
    case 'live':
    case 'live_stream':
      return '🔴';
    case 'interview':
      return '🎤';
    case 'retweet':
      return '🔄';
    default:
      return '📝';
  }
}

/**
 * Gets formatted metrics in a consistent structure for card display
 */
export function getFormattedMetrics(content: TwitterContentData): Array<{
  label: string;
  value: string;
  rawValue: number;
}> {
  const metrics = [];
  
  if (content.twitter_impressions && content.twitter_impressions > 0) {
    metrics.push({
      label: 'Impressions',
      value: formatMetricNumber(content.twitter_impressions),
      rawValue: content.twitter_impressions
    });
  }
  
  if (content.content_views && content.content_views > 0) {
    metrics.push({
      label: 'Views',
      value: formatMetricNumber(content.content_views),
      rawValue: content.content_views
    });
  }
  
  if (content.content_listeners && content.content_listeners > 0) {
    metrics.push({
      label: 'Listeners',
      value: formatMetricNumber(content.content_listeners),
      rawValue: content.content_listeners
    });
  }
  
  return metrics;
}

/**
 * Adds missing content_listeners field for compatibility
 */
export interface TwitterContentDataExtended extends TwitterContentData {
  content_listeners?: number;
}