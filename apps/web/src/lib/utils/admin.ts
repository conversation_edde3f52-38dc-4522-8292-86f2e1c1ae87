export const extractHostFromUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();
    
    // Check if it's a Twitter/X URL
    if (hostname === 'twitter.com' || hostname === 'x.com' || hostname === 'www.twitter.com' || hostname === 'www.x.com') {
      const pathParts = urlObj.pathname.split('/').filter(Boolean);
      // First part of path should be the username
      if (pathParts.length > 0 && pathParts[0] !== 'i') {
        return pathParts[0].toLowerCase();
      }
    }
    
    // For other URLs, try to extract from domain or path
    if (hostname.includes('youtube.com') || hostname.includes('youtu.be')) {
      // For YouTube, try to extract channel name from URL
      const pathParts = urlObj.pathname.split('/').filter(Boolean);
      if (pathParts.includes('c') || pathParts.includes('channel')) {
        const channelIndex = pathParts.findIndex(part => part === 'c' || part === 'channel');
        if (channelIndex >= 0 && pathParts[channelIndex + 1]) {
          return pathParts[channelIndex + 1].toLowerCase();
        }
      }
      // For @username format
      if (pathParts.length > 0 && pathParts[0].startsWith('@')) {
        return pathParts[0].substring(1).toLowerCase();
      }
    }
    
    return '';
  } catch {
    return '';
  }
};

export const formatDate = (dateString: string | null) => {
  if (!dateString) return 'N/A';
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  } catch {
    return dateString;
  }
};

export const parseMetricValue = (input: string): number => {
  if (!input || input.trim() === '') return 0;
  
  // Remove any whitespace and convert to lowercase
  const cleaned = input.trim().toLowerCase().replace(/,/g, '');
  
  // Handle k/m/b notation
  const multipliers: { [key: string]: number } = {
    'k': 1000,
    'm': 1000000,
    'b': 1000000000
  };
  
  // Check if the string ends with a multiplier
  const lastChar = cleaned.slice(-1);
  if (multipliers[lastChar]) {
    const numPart = cleaned.slice(0, -1);
    const baseNumber = parseFloat(numPart);
    if (!isNaN(baseNumber)) {
      return Math.floor(baseNumber * multipliers[lastChar]);
    }
  }
  
  // Handle regular numbers (including decimals, but floor them)
  const parsed = parseFloat(cleaned);
  return isNaN(parsed) ? 0 : Math.floor(Math.max(0, parsed));
};

export const formatNumber = (num: number): string => {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(num % 1000000000 === 0 ? 0 : 1) + 'b';
  } else if (num >= 1000000) {
    return (num / 1000000).toFixed(num % 1000000 === 0 ? 0 : 1) + 'm';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(num % 1000 === 0 ? 0 : 1) + 'k';
  }
  return num.toString();
};