/**
 * Environment variable validation for Convex + Clerk setup
 */

interface RequiredEnvVars {
  NEXT_PUBLIC_CONVEX_URL: string;
  NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: string;
  CLERK_SECRET_KEY?: string;
  CLERK_JWT_ISSUER_DOMAIN: string;
  CORS_ORIGIN?: string;
}

/**
 * Validates that all required environment variables are present
 */
export function validateEnvironmentVariables(): RequiredEnvVars {
  const errors: string[] = [];

  // Required client-side variables
  const NEXT_PUBLIC_CONVEX_URL = process.env.NEXT_PUBLIC_CONVEX_URL;
  if (!NEXT_PUBLIC_CONVEX_URL) {
    errors.push('NEXT_PUBLIC_CONVEX_URL is required');
  }

  const NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY;
  if (!NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY) {
    errors.push('NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY is required');
  }

  const CLERK_JWT_ISSUER_DOMAIN = process.env.CLERK_JWT_ISSUER_DOMAIN;
  if (!CLERK_JWT_ISSUER_DOMAIN) {
    errors.push('CLERK_JWT_ISSUER_DOMAIN is required');
  }

  // Validate URL format for Convex URL
  if (NEXT_PUBLIC_CONVEX_URL) {
    try {
      const url = new URL(NEXT_PUBLIC_CONVEX_URL);
      if (!url.protocol.match(/^https?:$/)) {
        errors.push('NEXT_PUBLIC_CONVEX_URL must use http or https protocol');
      }
      if (!url.hostname.includes('convex.cloud')) {
        errors.push('NEXT_PUBLIC_CONVEX_URL must be a valid Convex URL');
      }
    } catch {
      errors.push('NEXT_PUBLIC_CONVEX_URL must be a valid URL');
    }
  }

  if (CLERK_JWT_ISSUER_DOMAIN) {
    try {
      const url = new URL(CLERK_JWT_ISSUER_DOMAIN);
      if (!url.protocol.startsWith('http')) {
        errors.push('CLERK_JWT_ISSUER_DOMAIN must use http or https protocol');
      }
    } catch {
      errors.push('CLERK_JWT_ISSUER_DOMAIN must be a valid URL');
    }
  }

  // Validate Clerk publishable key format
  if (NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY && !NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY.startsWith('pk_')) {
    errors.push('NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY must start with "pk_"');
  }

  // Server-side only validation (skip if this is running on client)
  if (typeof window === 'undefined') {
    // Validate Clerk secret key format
    const CLERK_SECRET_KEY = process.env.CLERK_SECRET_KEY;
    if (CLERK_SECRET_KEY && !CLERK_SECRET_KEY.startsWith('sk_')) {
      errors.push('CLERK_SECRET_KEY must start with "sk_"');
    }

    // Validate CORS_ORIGIN format if it exists
    const CORS_ORIGIN = process.env.CORS_ORIGIN;
    if (CORS_ORIGIN && CORS_ORIGIN !== '*') {
      try {
        new URL(CORS_ORIGIN);
      } catch {
        errors.push('CORS_ORIGIN must be a valid URL or "*"');
      }
    }
  }

  if (errors.length > 0) {
    const errorMessage = `Environment validation failed:\n${errors.map(e => `  - ${e}`).join('\n')}`;
    throw new Error(errorMessage);
  }

  return {
    NEXT_PUBLIC_CONVEX_URL: NEXT_PUBLIC_CONVEX_URL!,
    NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY: NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY!,
    CLERK_JWT_ISSUER_DOMAIN: CLERK_JWT_ISSUER_DOMAIN!,
    CLERK_SECRET_KEY: process.env.CLERK_SECRET_KEY,
    CORS_ORIGIN: process.env.CORS_ORIGIN,
  };
}

/**
 * Get validated environment variables with defaults
 */
export function getValidatedEnv() {
  return validateEnvironmentVariables();
}

/**
 * Validate environment on application startup
 */
export function validateEnvOnStartup() {
  try {
    validateEnvironmentVariables();
    console.log('✅ Environment validation passed (Convex + Clerk)');
  } catch (error) {
    console.error('❌ Environment validation failed:', error instanceof Error ? error.message : error);
    process.exit(1);
  }
}
