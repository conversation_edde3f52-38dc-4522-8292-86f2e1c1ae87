/**
 * Rate limiting utilities for API protection
 */

interface RateLimit {
  count: number;
  resetTime: number;
  blocked: boolean;
}

interface RateLimitRule {
  windowMs: number;  // Time window in milliseconds
  maxRequests: number;  // Max requests per window
  blockDurationMs: number;  // How long to block after limit exceeded
}

// Default rate limit rules
const DEFAULT_RULES: Record<string, RateLimitRule> = {
  // Authentication endpoints - stricter limits
  'auth': {
    windowMs: 15 * 60 * 1000,  // 15 minutes
    maxRequests: 5,  // 5 attempts per 15 minutes
    blockDurationMs: 60 * 60 * 1000,  // Block for 1 hour
  },
  // Search endpoints - moderate limits
  'search': {
    windowMs: 60 * 1000,  // 1 minute
    maxRequests: 30,  // 30 searches per minute
    blockDurationMs: 5 * 60 * 1000,  // Block for 5 minutes
  },
  // CRUD operations - moderate limits
  'mutation': {
    windowMs: 60 * 1000,  // 1 minute
    maxRequests: 60,  // 60 mutations per minute
    blockDurationMs: 2 * 60 * 1000,  // Block for 2 minutes
  },
  // Read operations - more lenient
  'query': {
    windowMs: 60 * 1000,  // 1 minute
    maxRequests: 120,  // 120 queries per minute
    blockDurationMs: 60 * 1000,  // Block for 1 minute
  },
  // Admin operations - very strict
  'admin': {
    windowMs: 60 * 1000,  // 1 minute
    maxRequests: 10,  // 10 admin operations per minute
    blockDurationMs: 30 * 60 * 1000,  // Block for 30 minutes
  },
  // Default fallback
  'default': {
    windowMs: 60 * 1000,  // 1 minute
    maxRequests: 100,  // 100 requests per minute
    blockDurationMs: 5 * 60 * 1000,  // Block for 5 minutes
  }
};

/**
 * In-memory rate limiting store
 * In production, consider using Redis or a persistent store
 */
class RateLimitStore {
  private store = new Map<string, RateLimit>();

  get(key: string): RateLimit | null {
    const record = this.store.get(key);
    if (!record) return null;

    // Check if window has expired
    if (Date.now() > record.resetTime) {
      this.store.delete(key);
      return null;
    }

    return record;
  }

  set(key: string, value: RateLimit): void {
    this.store.set(key, value);
  }

  delete(key: string): void {
    this.store.delete(key);
  }

  // Clean up expired entries periodically
  cleanup(): void {
    const now = Date.now();
    for (const [key, record] of this.store.entries()) {
      if (now > record.resetTime) {
        this.store.delete(key);
      }
    }
  }
}

export class RateLimiter {
  private store = new RateLimitStore();

  constructor() {
    // Clean up expired entries every 5 minutes
    setInterval(() => {
      this.store.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Check if request is allowed under rate limit
   * @param identifier - User ID, IP address, or other identifier
   * @param ruleType - Type of rule to apply (auth, search, mutation, etc.)
   * @returns Object with allowed status and remaining requests
   */
  checkLimit(identifier: string, ruleType: string = 'default'): {
    allowed: boolean;
    limit: number;
    remaining: number;
    resetTime: number;
    blocked: boolean;
  } {
    const rule = DEFAULT_RULES[ruleType] || DEFAULT_RULES.default;
    const key = `${identifier}:${ruleType}`;
    const now = Date.now();

    let record = this.store.get(key);

    if (!record) {
      // First request in window
      record = {
        count: 1,
        resetTime: now + rule.windowMs,
        blocked: false,
      };
      this.store.set(key, record);

      return {
        allowed: true,
        limit: rule.maxRequests,
        remaining: rule.maxRequests - 1,
        resetTime: record.resetTime,
        blocked: false,
      };
    }

    // Check if currently blocked
    if (record.blocked && now < record.resetTime) {
      return {
        allowed: false,
        limit: rule.maxRequests,
        remaining: 0,
        resetTime: record.resetTime,
        blocked: true,
      };
    }

    // Check if within limit
    if (record.count < rule.maxRequests) {
      record.count++;
      this.store.set(key, record);

      return {
        allowed: true,
        limit: rule.maxRequests,
        remaining: rule.maxRequests - record.count,
        resetTime: record.resetTime,
        blocked: false,
      };
    }

    // Limit exceeded - block the identifier
    record.blocked = true;
    record.resetTime = now + rule.blockDurationMs;
    this.store.set(key, record);

    return {
      allowed: false,
      limit: rule.maxRequests,
      remaining: 0,
      resetTime: record.resetTime,
      blocked: true,
    };
  }

  /**
   * Reset rate limit for an identifier
   */
  reset(identifier: string, ruleType: string = 'default'): void {
    const key = `${identifier}:${ruleType}`;
    this.store.delete(key);
  }

  /**
   * Get current status without incrementing counter
   */
  getStatus(identifier: string, ruleType: string = 'default'): {
    count: number;
    remaining: number;
    resetTime: number;
    blocked: boolean;
  } | null {
    const rule = DEFAULT_RULES[ruleType] || DEFAULT_RULES.default;
    const key = `${identifier}:${ruleType}`;
    const record = this.store.get(key);

    if (!record) {
      return {
        count: 0,
        remaining: rule.maxRequests,
        resetTime: 0,
        blocked: false,
      };
    }

    return {
      count: record.count,
      remaining: Math.max(0, rule.maxRequests - record.count),
      resetTime: record.resetTime,
      blocked: record.blocked,
    };
  }
}

// Export singleton instance
export const rateLimiter = new RateLimiter();

/**
 * Get client identifier from request
 */
export function getClientIdentifier(req: Request | { headers?: Record<string, string | undefined>; ip?: string }, userId?: string): string {
  if (userId) {
    return `user:${userId}`;
  }

  // Try to get IP from various headers (for proxied requests)
  const headers = ('headers' in req ? req.headers : req.headers) || {};
  
  // Handle both Headers object and plain object
  const getHeader = (name: string): string | undefined => {
    if (typeof headers.get === 'function') {
      // Headers object
      return (headers as Headers).get(name) || undefined;
    } else {
      // Plain object
      return (headers as Record<string, string | undefined>)[name];
    }
  };

  const ip = 
    getHeader('x-real-ip') ||
    getHeader('x-forwarded-for')?.split(',')[0]?.trim() ||
    getHeader('x-forwarded') ||
    getHeader('forwarded-for') ||
    getHeader('forwarded') ||
    ('ip' in req ? req.ip : 'unknown');

  return `ip:${ip}`;
}

/**
 * Determine rate limit rule type based on operation
 */
export function getRuleType(operation: string, path?: string): string {
  if (path?.includes('/auth/') || operation.includes('login') || operation.includes('register')) {
    return 'auth';
  }
  
  if (operation.includes('admin') || path?.includes('/admin/')) {
    return 'admin';
  }
  
  if (operation.includes('search') || operation.includes('filter')) {
    return 'search';
  }
  
  if (operation.includes('create') || operation.includes('update') || operation.includes('delete')) {
    return 'mutation';
  }
  
  if (operation.includes('get') || operation.includes('list') || operation.includes('query')) {
    return 'query';
  }
  
  return 'default';
}