/**
 * Secure logging utilities to prevent information disclosure in production
 */

export interface LogContext {
  userId?: string;
  operation?: string;
  endpoint?: string;
  [key: string]: any;
}

/**
 * Safe logging that respects environment settings
 */
export class SecureLogger {
  private isProduction = process.env.NODE_ENV === 'production';
  private isDebugEnabled = process.env.DEBUG === 'true' || process.env.NODE_ENV === 'development';

  /**
   * Log debug information (only in development)
   */
  debug(message: string, context?: LogContext) {
    if (this.isDebugEnabled && !this.isProduction) {
      console.log(`🔍 [DEBUG] ${message}`, this.sanitizeContext(context));
    }
  }

  /**
   * Log info level messages (always logged but sanitized in production)
   */
  info(message: string, context?: LogContext) {
    if (this.isProduction) {
      console.log(`ℹ️ [INFO] ${message}`);
    } else {
      console.log(`ℹ️ [INFO] ${message}`, context);
    }
  }

  /**
   * Log warnings (always logged)
   */
  warn(message: string, context?: LogContext) {
    if (this.isProduction) {
      console.warn(`⚠️ [WARN] ${message}`);
    } else {
      console.warn(`⚠️ [WARN] ${message}`, context);
    }
  }

  /**
   * Log errors (always logged but sanitized in production)
   */
  error(message: string, error?: Error | unknown, context?: LogContext) {
    if (this.isProduction) {
      // In production, log generic error info without exposing details
      console.error(`❌ [ERROR] ${message}`);
      if (error instanceof Error) {
        console.error(`❌ [ERROR] ${error.name}: ${error.message}`);
      }
    } else {
      console.error(`❌ [ERROR] ${message}`, error, context);
    }
  }

  /**
   * Log authentication events (for security monitoring)
   */
  auth(message: string, userId?: string) {
    if (this.isProduction) {
      console.log(`🔐 [AUTH] ${message} ${userId ? `(User: ${this.maskUserId(userId)})` : ''}`);
    } else {
      console.log(`🔐 [AUTH] ${message}`, { userId });
    }
  }

  /**
   * Log security events (always logged)
   */
  security(message: string, context?: LogContext) {
    if (this.isProduction) {
      console.warn(`🛡️ [SECURITY] ${message}`);
    } else {
      console.warn(`🛡️ [SECURITY] ${message}`, context);
    }
  }

  /**
   * Sanitize context to remove sensitive information
   */
  private sanitizeContext(context?: LogContext): LogContext | undefined {
    if (!context) return undefined;

    const sanitized = { ...context };

    // Remove sensitive fields
    const sensitiveFields = [
      'password', 'token', 'accessToken', 'refreshToken', 
      'secret', 'key', 'email', 'phone', 'ssn'
    ];

    sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    });

    // Mask user ID
    if (sanitized.userId) {
      sanitized.userId = this.maskUserId(sanitized.userId);
    }

    return sanitized;
  }

  /**
   * Mask user ID for production logging
   */
  private maskUserId(userId: string): string {
    if (userId.length <= 4) return '[MASKED]';
    return userId.substring(0, 4) + '*'.repeat(userId.length - 4);
  }
}

// Export singleton instance
export const logger = new SecureLogger();

// Convenience functions
export const debugLog = (message: string, context?: LogContext) => logger.debug(message, context);
export const infoLog = (message: string, context?: LogContext) => logger.info(message, context);
export const warnLog = (message: string, context?: LogContext) => logger.warn(message, context);
export const errorLog = (message: string, error?: Error | unknown, context?: LogContext) => logger.error(message, error, context);
export const authLog = (message: string, userId?: string) => logger.auth(message, userId);
export const securityLog = (message: string, context?: LogContext) => logger.security(message, context);