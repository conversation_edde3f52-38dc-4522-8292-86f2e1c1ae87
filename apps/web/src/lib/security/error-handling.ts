/**
 * Secure error handling utilities for Convex + Next.js setup
 * Prevents information disclosure while maintaining good UX
 */

import { errorLog } from './logging';

export interface SecureErrorOptions {
  publicMessage?: string;
  code?: 'BAD_REQUEST' | 'UNAUTHORIZED' | 'FORBIDDEN' | 'NOT_FOUND' | 'INTERNAL_SERVER_ERROR' | 'TOO_MANY_REQUESTS';
  logContext?: Record<string, any>;
}

/**
 * Create a secure error that logs sensitive details server-side
 * but only exposes safe information to the client
 */
export function createSecureError(
  internalMessage: string,
  error?: Error | unknown,
  options: SecureErrorOptions = {}
): Error {
  const {
    publicMessage = 'An error occurred',
    logContext = {}
  } = options;

  // Log the full error details server-side
  errorLog(`[SECURE_ERROR] ${internalMessage}`, error, logContext);

  // Return sanitized error to client
  const secureError = new Error(publicMessage);
  secureError.name = 'SecureError';
  return secureError;
}

/**
 * Handle Convex errors securely
 */
export function handleConvexError(
  error: unknown,
  operation: string,
  context?: Record<string, any>
): Error {
  // Log specific Convex error details internally
  errorLog(`Convex error during ${operation}`, error, context);

  // Check if it's a Convex authentication error
  if (error && typeof error === 'object' && 'message' in error) {
    const errorMessage = (error as { message: string }).message;
    
    if (errorMessage.includes('Authentication required')) {
      return new Error('Please sign in to continue');
    }
    
    if (errorMessage.includes('Admin privileges required')) {
      return new Error('Access denied');
    }
    
    if (errorMessage.includes('validation')) {
      return new Error('Invalid request data');
    }
  }

  // Return generic message for other errors
  return new Error('Operation failed');
}

/**
 * Handle authentication errors
 */
export function handleAuthError(
  error: unknown,
  context?: Record<string, any>
): Error {
  errorLog('Authentication error', error, context);

  // Don't reveal whether user exists or not
  return new Error('Authentication failed');
}

/**
 * Handle authorization errors
 */
export function handleAuthorizationError(
  error: unknown,
  requiredPermission?: string,
  context?: Record<string, any>
): Error {
  errorLog('Authorization error', error, { 
    ...context, 
    requiredPermission 
  });

  return new Error('Access denied');
}

/**
 * Handle validation errors
 */
export function handleValidationError(
  error: unknown,
  field?: string,
  context?: Record<string, any>
): Error {
  errorLog('Validation error', error, { ...context, field });

  if (field) {
    return new Error(`Invalid ${field}`);
  }

  return new Error('Invalid request data');
}

/**
 * Handle not found errors
 */
export function handleNotFoundError(
  resourceType: string = 'resource',
  context?: Record<string, any>
): Error {
  errorLog(`${resourceType} not found`, undefined, context);

  return new Error(`${resourceType} not found`);
}

/**
 * Generic error wrapper for async operations
 */
export function withSecureErrorHandling<T>(
  operation: () => Promise<T>,
  operationName: string,
  context?: Record<string, any>
): Promise<T> {
  return operation().catch((error) => {
    // Handle Convex-specific errors
    if (error && typeof error === 'object' && 'message' in error) {
      const errorMessage = (error as { message: string }).message;
      
      if (errorMessage.includes('Authentication') || errorMessage.includes('Admin')) {
        throw handleConvexError(error, operationName, context);
      }
    }

    // Handle unexpected errors
    throw createSecureError(
      `Unexpected error in ${operationName}`,
      error,
      {
        publicMessage: 'An unexpected error occurred',
        logContext: context,
      }
    );
  });
}

/**
 * Sanitize error for client response
 */
export function sanitizeErrorForClient(error: unknown): {
  message: string;
  code?: string;
} {
  if (error instanceof Error) {
    return {
      message: error.message,
      code: error.name,
    };
  }

  // Don't expose internal error details
  return {
    message: 'An error occurred',
    code: 'INTERNAL_SERVER_ERROR',
  };
}

/**
 * Error boundary for React components
 */
export class ErrorBoundary extends Error {
  constructor(
    public originalError: Error,
    public context?: Record<string, any>
  ) {
    super('Error boundary caught an error');
    this.name = 'ErrorBoundary';
    
    // Log the error with context
    errorLog('React error boundary triggered', originalError, context);
  }
}

/**
 * Validate and sanitize error messages
 */
export function sanitizeErrorMessage(message: string): string {
  // Remove potentially sensitive information patterns
  return message
    .replace(/password.*$/gi, '[REDACTED]')
    .replace(/token.*$/gi, '[REDACTED]')
    .replace(/key.*$/gi, '[REDACTED]')
    .replace(/secret.*$/gi, '[REDACTED]')
    .replace(/\b\d{3}-\d{2}-\d{4}\b/g, '[SSN]') // SSN pattern
    .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]') // Email pattern
    .slice(0, 200); // Limit length
}

/**
 * Handle Clerk authentication errors specifically
 */
export function handleClerkError(error: unknown): Error {
  errorLog('Clerk authentication error', error);
  
  if (error && typeof error === 'object' && 'message' in error) {
    const errorMessage = (error as { message: string }).message;
    
    if (errorMessage.includes('Failed to load Clerk')) {
      return new Error('Authentication service unavailable. Please check your internet connection and try again.');
    }
    
    if (errorMessage.includes('Invalid publishable key')) {
      return new Error('Authentication configuration error. Please contact support.');
    }
  }
  
  return new Error('Authentication failed. Please try again.');
}