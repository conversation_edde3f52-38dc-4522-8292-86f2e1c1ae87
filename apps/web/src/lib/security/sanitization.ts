/**
 * Security utilities for input sanitization and validation
 */

/**
 * Sanitizes search input to prevent SQL injection attacks
 * Escapes special characters used in SQL LIKE patterns and limits length
 */
export function sanitizeSearchInput(input: string): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  // Limit input length to prevent DoS attacks
  const maxLength = 100;
  const trimmed = input.slice(0, maxLength);

  // Escape SQL LIKE pattern special characters: %, _, \
  return trimmed.replace(/[%_\\]/g, '\\$&');
}

/**
 * Sanitizes host names - allows only valid domain characters
 */
export function sanitizeHost(host: string): string {
  if (!host || typeof host !== 'string') {
    return '';
  }

  // Remove any characters that aren't valid for domain names
  // Allow: a-z, 0-9, hyphens, periods, underscores
  return host
    .toLowerCase()
    .replace(/[^a-z0-9\-._]/g, '')
    .slice(0, 253); // Domain name length limit
}

/**
 * Sanitizes text content for display to prevent XSS
 */
export function sanitizeTextContent(content: string): string {
  if (!content || typeof content !== 'string') {
    return '';
  }

  // Basic HTML entity encoding
  return content
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
}

/**
 * Validates and sanitizes URL inputs
 */
export function sanitizeUrl(url: string): string {
  if (!url || typeof url !== 'string') {
    return '';
  }

  try {
    const parsed = new URL(url);
    
    // Only allow http and https protocols
    if (!['http:', 'https:'].includes(parsed.protocol)) {
      throw new Error('Invalid protocol');
    }

    return parsed.toString();
  } catch {
    return '';
  }
}

/**
 * Validates redirect paths to prevent open redirect attacks
 */
export function validateRedirectPath(path: string): string {
  if (!path || typeof path !== 'string') {
    return '/';
  }

  // Allow only relative paths starting with /
  if (!path.startsWith('/')) {
    return '/';
  }

  // Prevent protocol-relative URLs (//example.com)
  if (path.startsWith('//')) {
    return '/';
  }

  // Define allowed paths
  const allowedPaths = [
    '/',
    '/dashboard',
    '/admin',
    '/groups',
    '/auth/login',
    '/auth/callback'
  ];

  // Allow exact matches or paths that start with allowed prefixes
  const isAllowed = allowedPaths.some(allowedPath => {
    return path === allowedPath || 
           (allowedPath !== '/' && path.startsWith(allowedPath + '/'));
  });

  return isAllowed ? path : '/';
}

/**
 * Limits string length and sanitizes for database storage
 */
export function sanitizeStringForDb(input: string, maxLength: number = 255): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  return input.trim().slice(0, maxLength);
}

/**
 * Validates and sanitizes array inputs
 */
export function sanitizeArray(input: unknown, maxLength: number = 10): string[] {
  if (!Array.isArray(input)) {
    return [];
  }

  return input
    .slice(0, maxLength)
    .filter(item => typeof item === 'string' && item.trim().length > 0)
    .map(item => sanitizeStringForDb(item.toString(), 50));
}

/**
 * Sanitizes numeric inputs with bounds checking
 */
export function sanitizeNumber(input: unknown, min: number = 0, max: number = Number.MAX_SAFE_INTEGER): number {
  const num = Number(input);
  
  if (isNaN(num) || !isFinite(num)) {
    return 0;
  }

  return Math.max(min, Math.min(max, Math.floor(num)));
}