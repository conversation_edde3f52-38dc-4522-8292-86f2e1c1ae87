import { z } from "zod";

export const contentFormSchema = z.object({
  content_link: z.string().url("Please enter a valid URL"),
  content_tags: z.array(z.string()).default([]),
  host: z.string().min(1, "Host account is required").transform(val => val.trim().toLowerCase()),
  content_account: z.array(z.string()).min(1, "At least one content account is required")
    .transform(accounts => accounts.filter(account => account.trim() !== '')),
  content_created_date: z.string().min(1, "Created date is required"),
  // Changed from enum validation to accept any string array since values are dynamic from DB
  content_types: z.array(z.string()).default([]),
  twitter_content_type: z.string().nullable().optional(),
  content_views: z.number().min(0, "Views must be 0 or greater").default(0),
  content_listeners: z.number().min(0, "Listeners must be 0 or greater").default(0),
  twitter_impressions: z.number().min(0, "Impressions must be 0 or greater").default(0),
  content_follow_increase: z.number().min(0, "Follow increase must be 0 or greater").default(0),
  content_title: z.string().nullable().optional(),
  content_description: z.string().nullable().optional(),
  // Changed from enum validation to accept any string array since values are dynamic from DB
  content_categories: z.array(z.string()).default([]),
});