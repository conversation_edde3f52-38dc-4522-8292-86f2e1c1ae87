/**
 * Enhanced image compression utility for UploadThing uploads
 * Optimized for testimonial screenshots and marketing images
 */

import imageCompression from 'browser-image-compression';

export interface CompressionOptions {
  maxSizeMB?: number;
  maxWidthOrHeight?: number;
  quality?: number;
  fileType?: string;
  useWebWorker?: boolean;
}

export interface CompressionResult {
  file: File;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
}

/**
 * Compress image with optimal settings for testimonials
 */
export const compressTestimonialImage = async (
  file: File,
  options?: Partial<CompressionOptions>
): Promise<CompressionResult> => {
  const defaultOptions: CompressionOptions = {
    maxSizeMB: 0.8,           // 800KB max for fast loading
    maxWidthOrHeight: 1920,   // 1080p max resolution
    quality: 0.85,           // High quality for testimonials
    fileType: 'image/jpeg',  // Consistent format
    useWebWorker: true       // Better performance
  };

  const finalOptions = { ...defaultOptions, ...options };
  const originalSize = file.size;

  try {
    const compressedFile = await imageCompression(file, finalOptions);
    const compressionRatio = ((originalSize - compressedFile.size) / originalSize) * 100;

    console.log(`🗜️ Image compressed: ${(originalSize / 1024 / 1024).toFixed(2)}MB → ${(compressedFile.size / 1024 / 1024).toFixed(2)}MB (${compressionRatio.toFixed(1)}% reduction)`);

    return {
      file: compressedFile,
      originalSize,
      compressedSize: compressedFile.size,
      compressionRatio
    };
  } catch (error) {
    console.error('❌ Compression failed:', error);
    
    // Fallback to original file
    return {
      file,
      originalSize,
      compressedSize: originalSize,
      compressionRatio: 0
    };
  }
};

/**
 * Batch compress multiple images with progress tracking
 */
export const compressImageBatch = async (
  files: File[],
  onProgress?: (completed: number, total: number) => void
): Promise<CompressionResult[]> => {
  const results: CompressionResult[] = [];
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    
    if (file.type.startsWith('image/')) {
      const result = await compressTestimonialImage(file);
      results.push(result);
    } else {
      // Non-image files pass through unchanged
      results.push({
        file,
        originalSize: file.size,
        compressedSize: file.size,
        compressionRatio: 0
      });
    }
    
    onProgress?.(i + 1, files.length);
  }
  
  return results;
};

/**
 * Smart compression based on file size and type
 */
export const smartCompress = async (file: File): Promise<File> => {
  // Skip compression for small files (< 200KB)
  if (file.size < 200 * 1024) {
    console.log(`⚡ Skipping compression for small file: ${file.name}`);
    return file;
  }

  // Skip non-image files
  if (!file.type.startsWith('image/')) {
    console.log(`📄 Skipping compression for non-image: ${file.name}`);
    return file;
  }

  // Aggressive compression for very large files (> 5MB)
  if (file.size > 5 * 1024 * 1024) {
    console.log(`🔥 Aggressive compression for large file: ${file.name}`);
    const result = await compressTestimonialImage(file, {
      maxSizeMB: 0.5,
      maxWidthOrHeight: 1440,
      quality: 0.75
    });
    return result.file;
  }

  // Standard compression for medium files
  const result = await compressTestimonialImage(file);
  return result.file;
};

/**
 * Estimate compression savings before actual compression
 */
export const estimateCompression = (file: File): {
  estimatedSize: number;
  estimatedSavings: number;
} => {
  if (!file.type.startsWith('image/')) {
    return { estimatedSize: file.size, estimatedSavings: 0 };
  }

  // Rough estimates based on file type and size
  let compressionFactor = 0.3; // 30% of original size on average
  
  if (file.type === 'image/png') compressionFactor = 0.4;
  if (file.type === 'image/webp') compressionFactor = 0.6;
  
  const estimatedSize = Math.max(file.size * compressionFactor, 100 * 1024); // Min 100KB
  const estimatedSavings = file.size - estimatedSize;
  
  return { estimatedSize, estimatedSavings };
};