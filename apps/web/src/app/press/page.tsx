"use client";

import { useState } from "react";
import Link from "next/link";
import { SearchBar } from "@/components/search-bar";
import { FilteredContentGrid } from "@/components/dashboard/FilteredContentGrid";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

interface Filters {
  contentTypes: string[];
  categories: string[];
  sortBy: string;
  sortOrder: string;
}

export default function PressPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState<Filters>({
    contentTypes: ["presskit"],
    categories: [],
    sortBy: "date",
    sortOrder: "desc",
  });

  const handleSearch = (query: string) => setSearchQuery(query);

  const handleFilter = (filterType: string, values: string[]) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: values,
    }));
  };

  const handleSort = (sortBy: string, sortOrder?: string) => {
    setFilters((prev) => ({
      ...prev,
      sortBy,
      sortOrder: sortOrder || prev.sortOrder,
    }));
  };

  return (
    <div className="min-h-screen gradient-tech">
      <section className="content-container pt-24 pb-12 text-center">
        <div className="mx-auto max-w-4xl space-y-6">
          <span className="inline-flex rounded-full border border-border/40 bg-muted/20 px-4 py-2 text-xs font-semibold uppercase tracking-[0.4em] text-muted-foreground/80">
            Press Releases
          </span>
          <h1 className="text-4xl sm:text-5xl font-bold leading-tight text-foreground/95">
            Official announcements and coverage ready for partners
          </h1>
          <p className="text-base text-muted-foreground">
            Browse media kits, syndicated interviews, and aggregated coverage to support go-to-market pushes.
          </p>
          <div className="flex justify-center">
            <Link href="/">
              <Button size="lg" className="flex items-center gap-2">
                Back to landing
                <ArrowRight className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <section className="content-container pb-24">
        <div className="glass-effect rounded-3xl p-8 sm:p-10 lg:p-12 card-hover space-y-10">
          <SearchBar
            onSearch={handleSearch}
            onFilter={handleFilter}
            onSort={handleSort}
            placeholder="Search press releases and media kits..."
            filters={filters}
            searchQuery={searchQuery}
          />

          <FilteredContentGrid
            searchQuery={searchQuery}
            contentTypes={filters.contentTypes}
            categories={filters.categories}
            sortBy={filters.sortBy as "impressions" | "date"}
            sortOrder={filters.sortOrder as "desc" | "asc"}
          />
        </div>
      </section>
    </div>
  );
}

