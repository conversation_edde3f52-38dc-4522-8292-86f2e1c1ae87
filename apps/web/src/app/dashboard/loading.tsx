import { Skeleton } from "@/components/ui/skeleton";
import { Card } from "@/components/ui/card";
import { SectionHeaderSkeleton } from "@/components/skeletons/SectionHeaderSkeleton";
import { TwitterCardSkeleton } from "@/components/skeletons/TwitterCardSkeleton";
import { TestimonialCardSkeleton } from "@/components/skeletons/TestimonialCardSkeleton";

export default function Loading() {
  return (
    <div className="min-h-screen gradient-tech">
      {/* Hero */}
      <section className="content-container py-16 lg:py-24">
        <div className="center-all flex-col text-center space-y-8">
          <div className="space-y-4 max-w-3xl w-full">
            <Skeleton className="h-10 w-2/3 mx-auto rounded-lg" />
            <Skeleton className="h-6 w-1/2 mx-auto" />
          </div>
          <div className="w-full max-w-4xl mx-auto">
            <Card className="p-4">
              <Skeleton className="h-12 w-full rounded-xl" />
            </Card>
          </div>
        </div>
      </section>

      <section className="content-container space-y-12 pb-20">
        <div className="glass-effect rounded-3xl p-8 space-y-6">
          <SectionHeaderSkeleton />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-7 xl:gap-8">
            {Array.from({ length: 6 }).map((_, i) => (
              <TwitterCardSkeleton key={i} />
            ))}
          </div>
        </div>

        <div className="glass-effect rounded-3xl p-8 space-y-6">
          <SectionHeaderSkeleton />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-7 xl:gap-8">
            {Array.from({ length: 6 }).map((_, i) => (
              <TestimonialCardSkeleton key={i} />
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}

