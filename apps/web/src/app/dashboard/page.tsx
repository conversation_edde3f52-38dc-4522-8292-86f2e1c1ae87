"use client";

import { useState, useEffect, Suspense } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { TwitterContentGrid } from "@/components/dashboard/TwitterContentGrid";
import { TestimonialsGrid } from "@/components/dashboard/TestimonialsGrid";
import { FilteredContentGrid } from "@/components/dashboard/FilteredContentGrid";
// import { ContentTable } from "@/components/content-table"; // Archived
// import { TestimonialsSection } from "@/components/testimonials-section"; // Archived
import { SectionHeaderSkeleton } from "@/components/skeletons/SectionHeaderSkeleton";
import { TwitterCardSkeleton } from "@/components/skeletons/TwitterCardSkeleton";
import { MarketingCaseStudiesGrid } from "@/components/marketing/MarketingCaseStudiesGrid";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

interface Filters {
  contentTypes: string[];
  categories: string[];
  sortBy: string;
  sortOrder: string;
}

function HomeContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  console.log('Dashboard page loaded');
  
  const [searchQuery, setSearchQuery] = useState(() => searchParams.get("q") || "");
  const [filters, setFilters] = useState<Filters>(() => ({
    contentTypes: searchParams.get("types")?.split(",").filter(Boolean) || [],
    categories: searchParams.get("categories")?.split(",").filter(Boolean) || [],
    sortBy: searchParams.get("sort") || "impressions",
    sortOrder: searchParams.get("order") || "desc",
  }));

  useEffect(() => {
    const currentQuery = searchParams.get("q") || "";
    const currentFilters = {
      contentTypes: searchParams.get("types")?.split(",").filter(Boolean) || [],
      categories: searchParams.get("categories")?.split(",").filter(Boolean) || [],
      sortBy: searchParams.get("sort") || "impressions",
      sortOrder: searchParams.get("order") || "desc",
    };

    setSearchQuery(currentQuery);
    setFilters(currentFilters);
  }, [searchParams]);

  useEffect(() => {
    const params = new URLSearchParams();
    const trimmedQuery = searchQuery.trim();

    if (trimmedQuery) params.set("q", trimmedQuery);
    if (filters.contentTypes.length > 0) params.set("types", filters.contentTypes.join(","));
    if (filters.categories.length > 0) params.set("categories", filters.categories.join(","));
    if (filters.sortBy !== "impressions") params.set("sort", filters.sortBy);
    if (filters.sortOrder !== "desc") params.set("order", filters.sortOrder);

    const nextQuery = params.toString();
    const currentQuery = searchParams.toString();

    if (nextQuery !== currentQuery) {
      router.replace(nextQuery ? `/dashboard?${nextQuery}` : "/dashboard", { scroll: false });
    }
  }, [filters, searchQuery, router, searchParams]);

  // getDisplayTitle function moved to FilteredContentGrid component

  const isSearchActive = searchQuery || filters.contentTypes.length > 0 || filters.categories.length > 0 || filters.sortBy !== "impressions" || filters.sortOrder !== "desc";

  return (
    <div className="min-h-screen gradient-tech">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/3 via-transparent to-accent/3" />
        <div className="relative content-container pt-12 pb-20 lg:pt-16 lg:pb-32">
          {/* Main Hero Content */}
          <div className="center-all flex-col text-center space-y-12">
            {/* Title */}
            <div className="space-y-6">
              <h1 className="text-5xl sm:text-6xl lg:text-7xl xl:text-8xl font-bold tracking-tight leading-tight">
                <div className="bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                  IBC VENTURES
                </div>
                <div className="bg-gradient-to-r from-ibc-green to-ibc-green-light bg-clip-text text-transparent mt-2">
                  PORTFOLIO
                </div>
              </h1>
              
              <p className="text-muted-foreground text-xl sm:text-2xl max-w-3xl mx-auto leading-relaxed font-medium">
                The ONLY media-led incubator and tokenized deal desk and venture fund in the world
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="content-container pb-20">
        {isSearchActive ? (
          // Show filtered results when search is active
          <div className="glass-effect rounded-3xl p-10 card-hover">
            <FilteredContentGrid
              searchQuery={searchQuery}
              contentTypes={filters.contentTypes}
              categories={filters.categories}
              sortBy={filters.sortBy as "impressions" | "date"}
              sortOrder={filters.sortOrder as "desc" | "asc"}
            />
          </div>
        ) : (
          // Show all sections when no search is active
          <div className="space-y-20">
            {/* Twitter Spaces & Tweets Section */}
            <div className="glass-effect rounded-3xl p-10 card-hover">
              <TwitterContentGrid />
            </div>

            {/* Marketing Case Studies Section */}
            <div className="glass-effect rounded-3xl p-10 card-hover">
              <div className="space-y-10">
                <div className="text-center space-y-4">
                  <h2 className="text-3xl font-bold">Marketing Case Studies</h2>
                  <p className="text-muted-foreground">
                    Track the compounding growth of launches accelerated through the IBC venture and media engine.
                  </p>
                </div>

                <MarketingCaseStudiesGrid initialPageSize={6} />

                <div className="flex justify-center">
                  <Link href="/marketing">
                    <Button size="lg" className="flex items-center gap-2">
                      Explore marketing wins
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}
      </section>

      {/* Testimonials Section */}
      {!isSearchActive && (
        <div className="content-container py-20">
          <div className="glass-effect rounded-3xl p-10 card-hover">
            <TestimonialsGrid 
              showFilters={false}
              showSearch={false}
              showPagination={true}
              itemsPerPage={6}
            />
          </div>
        </div>
      )}
    </div>
  );
}

export default function Dashboard() {
  return (
    <Suspense
      fallback={(
        <div className="content-container py-16 space-y-8">
          <SectionHeaderSkeleton />
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <TwitterCardSkeleton key={i} />
            ))}
          </div>
        </div>
      )}
    >
      <HomeContent />
    </Suspense>
  );
}
