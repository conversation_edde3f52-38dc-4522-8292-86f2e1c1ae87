"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/../../../convex/_generated/api";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Plus, Trash2, Folder, Users, Globe, Lock } from "lucide-react";
import { toast } from "sonner";
import { useAuth } from "@/components/auth/auth-provider";
import { GroupCardSkeleton } from "@/components/skeletons/GroupCardSkeleton";
import { Skeleton } from "@/components/ui/skeleton";

export default function GroupsPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [isCreating, setIsCreating] = useState(false);
  const [newGroupName, setNewGroupName] = useState("");
  const [newGroupDescription, setNewGroupDescription] = useState("");
  const [newGroupIsPublic, setNewGroupIsPublic] = useState(true);

  const groups = useQuery(api.groups.getGroups, {});

  const createGroupMutation = useMutation(api.groups.createGroup);
  const deleteGroupMutation = useMutation(api.groups.deleteGroup);

  const handleCreateGroup = async () => {
    if (!newGroupName.trim()) {
      toast.error("Group name is required");
      return;
    }

    try {
      await createGroupMutation({
        name: newGroupName.trim(),
        description: newGroupDescription.trim() || undefined,
        is_public: newGroupIsPublic,
      });

      console.log("Group created successfully");
      toast.success("Group created successfully!");
      setIsCreating(false);
      setNewGroupName("");
      setNewGroupDescription("");
      setNewGroupIsPublic(true);
    } catch (error: any) {
      console.error("Error creating group:", error);
      toast.error(error.message || "Failed to create group");
    }
  };

  const handleDeleteGroup = async (groupId: string, groupName: string) => {
    if (confirm(`Are you sure you want to delete "${groupName}"? This cannot be undone.`)) {
      try {
        await deleteGroupMutation({ group_id: groupId as any });
        console.log("Group deleted successfully");
        toast.success("Group deleted successfully!");
      } catch (error: any) {
        console.error("Error deleting group:", error);
        toast.error(error.message || "Failed to delete group");
      }
    }
  };

  if (authLoading) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div className="flex items-center justify-between mb-6 sm:mb-8">
          <div className="space-y-2">
            <Skeleton className="h-8 w-40" />
            <Skeleton className="h-4 w-72" />
          </div>
          <Skeleton className="h-9 w-32 rounded-md" />
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <GroupCardSkeleton key={i} />
          ))}
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            Please sign in to view groups
          </h1>
        </div>
      </div>
    );
  }

  if (groups === undefined) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        <div className="flex items-center justify-between mb-6 sm:mb-8">
          <div className="space-y-2">
            <Skeleton className="h-8 w-40" />
            <Skeleton className="h-4 w-72" />
          </div>
          <Skeleton className="h-9 w-32 rounded-md" />
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <GroupCardSkeleton key={i} />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6 sm:mb-8">
        <div className="min-w-0 flex-1">
          <h1 className="text-2xl sm:text-3xl font-semibold text-gray-900 dark:text-white">
            My Groups
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1 sm:mt-2 text-sm sm:text-base">
            Organize your favorite content into custom collections
          </p>
        </div>

        <Button
          onClick={() => setIsCreating(true)}
          disabled={isCreating}
          className="flex items-center gap-2 w-full sm:w-auto justify-center"
        >
          <Plus className="h-4 w-4" />
          New Group
        </Button>
      </div>

      {/* Create new group form */}
      {isCreating && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg sm:text-xl">Create New Group</CardTitle>
            <CardDescription className="text-sm sm:text-base">
              Give your group a name and optional description
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="group-name" className="text-sm font-medium">Group Name</Label>
              <Input
                id="group-name"
                placeholder="Enter group name"
                value={newGroupName}
                onChange={(e) => setNewGroupName(e.target.value)}
                maxLength={100}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="group-description" className="text-sm font-medium">Description (optional)</Label>
              <Input
                id="group-description"
                placeholder="What's this group about?"
                value={newGroupDescription}
                onChange={(e) => setNewGroupDescription(e.target.value)}
                maxLength={500}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">Privacy Settings</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="group-public"
                  checked={newGroupIsPublic}
                  onCheckedChange={(checked) => setNewGroupIsPublic(Boolean(checked))}
                />
                <Label htmlFor="group-public" className="text-sm flex items-center gap-2 cursor-pointer">
                  {newGroupIsPublic ? (
                    <>
                      <Globe className="h-4 w-4 text-green-600" />
                      <span>Make this group public (anyone can view when shared)</span>
                    </>
                  ) : (
                    <>
                      <Lock className="h-4 w-4 text-gray-600" />
                      <span>Keep this group private (only you can view)</span>
                    </>
                  )}
                </Label>
              </div>
              <p className="text-xs text-gray-500 ml-6">
                {newGroupIsPublic ?
                  "Public groups can be shared with others via a link" :
                  "Private groups are only visible to you"
                }
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                onClick={handleCreateGroup}
                className="w-full sm:w-auto"
              >
                Create Group
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setIsCreating(false);
                  setNewGroupName("");
                  setNewGroupDescription("");
                  setNewGroupIsPublic(true);
                }}
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Groups grid */}
      {groups && groups.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
          {groups.map((group) => (
            <Card
              key={group._id}
              className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-[1.02] group"
              onClick={() => router.push(`/groups/${group._id}`)}
            >
              <CardHeader className="flex flex-row items-start justify-between space-y-0 pb-3">
                <div className="flex items-start gap-3 min-w-0 flex-1">
                  <Folder className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                  <div className="min-w-0 flex-1">
                    <CardTitle className="text-base sm:text-lg line-clamp-2 group-hover:text-blue-600 transition-colors">
                      {group.name}
                    </CardTitle>
                    {group.description && (
                      <CardDescription className="mt-1 text-sm line-clamp-2">
                        {group.description}
                      </CardDescription>
                    )}
                  </div>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteGroup(group._id, group.name);
                  }}
                  className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </CardHeader>

              <CardContent className="pt-0">
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-3">
                  <Users className="h-4 w-4" />
                  <span>
                    {group.content_count} {group.content_count === 1 ? "item" : "items"}
                  </span>
                </div>

                <div className="text-xs text-gray-500 dark:text-gray-500">
                  Created {new Date(group._creationTime).toLocaleDateString()}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="text-center py-12 sm:py-16">
          <CardContent>
            <Folder className="h-12 w-12 sm:h-16 sm:w-16 text-gray-400 mx-auto mb-4 sm:mb-6" />
            <h3 className="text-lg sm:text-xl font-medium text-gray-900 dark:text-white mb-2">
              No groups yet
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4 sm:mb-6 text-sm sm:text-base max-w-md mx-auto">
              Create your first group to start organizing content into custom collections
            </p>
            <Button
              onClick={() => setIsCreating(true)}
              className="w-full sm:w-auto"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Group
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
