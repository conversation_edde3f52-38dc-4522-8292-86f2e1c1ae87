"use client";

import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowR<PERSON>, BarChart3, Users, Target, Zap, ExternalLink, Eye, Heart, Repeat2, Globe } from "lucide-react";
import { useQuery } from "convex/react";
import { api } from "@/../../../convex/_generated/api";

const ACCOUNT_HANDLES = [
  'MarioNawfal',
  'RoundtableSpace',
  'Crypto_TownHall',
] as const;




import { formatNumber } from "@/lib/utils/format";

export default function LandingPage() {
  const router = useRouter();
  const featuredContent = useQuery(api.content.getFeaturedContent, {});
  const featuredLoading = featuredContent === undefined;
  const mediaMetrics = useQuery(api.admin.getMediaAccountMetrics, {});
  if (process.env.NODE_ENV === 'development') {
    console.log('[Landing] mediaMetrics', mediaMetrics);
  }

  const handleDashboardClick = () => {
    console.log('Dashboard button clicked - attempting navigation...');
    try {
      router.push('/dashboard');
      // Fallback navigation if router.push fails
      setTimeout(() => {
        if (window.location.pathname !== '/dashboard') {
          console.log('Router.push failed, using window.location');
          window.location.href = '/dashboard';
        }
      }, 100);
    } catch (error) {
      console.error('Navigation error:', error);

      window.location.href = '/dashboard';
    }
  };

  return (
    <div className="min-h-screen gradient-tech">
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5" />
        <div className="relative content-container py-24 lg:py-32">
          <div className="center-all flex-col text-center space-y-12 max-w-6xl mx-auto">
            {/* Main Hero Content */}
            <div className="space-y-8">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold tracking-tight leading-tight">
                <div className="bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                  IBC VENTURES
                </div>
                <div className="bg-gradient-to-r from-ibc-green to-ibc-green-light bg-clip-text text-transparent mt-2">
                  WEB3 MEDIA EMPIRE
                </div>
              </h1>

              <p className="text-muted-foreground text-lg sm:text-xl max-w-3xl mx-auto leading-relaxed font-medium">
                The fastest way for Web3 teams to launch, grow, and win attention. Backed by 700+ portfolio companies and the largest media platform on X.
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                size="lg"
                className="flex items-center gap-2 text-lg px-8 py-6 hover:scale-105 transition-all duration-300 bg-ibc-green hover:bg-ibc-green-dark text-white"
                onClick={handleDashboardClick}
                aria-label="Explore the dashboard"
              >
                <BarChart3 className="h-5 w-5" />
                Explore Dashboard
                <ArrowRight className="h-5 w-5" />
              </Button>
              <Button variant="outline" size="lg" className="flex items-center gap-2 text-lg px-8 py-6 hover:scale-105 transition-all duration-300" asChild>
                <a href="#how-it-works" aria-label="Learn how IBC helps teams">Learn More</a>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Impact Metrics Section */}
      <section className="content-container py-16 sm:py-20 lg:py-24">
        <div className="glass-effect rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-12 xl:p-16 card-hover">
          <div className="text-center space-y-8 sm:space-y-10 lg:space-y-12">
            <div className="space-y-3 sm:space-y-4 lg:space-y-6">
               <h2 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold">Impact</h2>              <p className="text-muted-foreground text-base sm:text-lg lg:text-xl max-w-2xl lg:max-w-3xl mx-auto px-4">
                With 4B+ monthly impressions, millions of weekly listeners, and 700+ portfolio companies
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 lg:gap-12 xl:gap-16 max-w-5xl xl:max-w-6xl mx-auto">
              <div className="space-y-3 sm:space-y-4 lg:space-y-5 text-center px-2 sm:px-4">
                <div className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-chart-1 leading-none tracking-tight min-h-[60px] sm:min-h-[80px] lg:min-h-[100px] xl:min-h-[120px] flex items-center justify-center">
                  4B+
                </div>
                <div className="text-muted-foreground text-sm sm:text-base lg:text-lg xl:text-xl font-semibold tracking-wide px-1 sm:px-2">
                  Monthly Impressions
                </div>
              </div>
              <div className="space-y-3 sm:space-y-4 lg:space-y-5 text-center px-2 sm:px-4">
                <div className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-chart-2 leading-none tracking-tight min-h-[60px] sm:min-h-[80px] lg:min-h-[100px] xl:min-h-[120px] flex items-center justify-center">
                  700+
                </div>
                <div className="text-muted-foreground text-sm sm:text-base lg:text-lg xl:text-xl font-semibold tracking-wide px-1 sm:px-2">
                  Portfolio Companies
                </div>
              </div>
              <div className="space-y-3 sm:space-y-4 lg:space-y-5 text-center px-2 sm:px-4">
                <div className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-chart-3 leading-none tracking-tight min-h-[60px] sm:min-h-[80px] lg:min-h-[100px] xl:min-h-[120px] flex items-center justify-center">
                  200+
                </div>
                <div className="text-muted-foreground text-sm sm:text-base lg:text-lg xl:text-xl font-semibold tracking-wide px-1 sm:px-2">
                  Team Members
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Growth & Media Section */}
      <section className="content-container py-24">
        <div className="space-y-16">
          {/* Our Growth Section */}
          <div className="glass-effect rounded-3xl p-8 sm:p-10 lg:p-12">
            <div className="max-w-4xl mx-auto text-center mb-12">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">Our Growth</h2>
              <p className="text-muted-foreground text-lg sm:text-xl leading-relaxed">
                We consistently rank in the global top 10 accounts across ALL social media platforms (several times reaching #4).
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
              <div className="flex flex-col items-center text-center group">
                <div className="p-4 rounded-2xl bg-chart-1/10 group-hover:bg-chart-1/20 transition-colors mb-6">
                  <Target className="h-8 w-8 text-chart-1" />
                </div>
                <h3 className="font-semibold text-xl mb-3">Portfolio Success</h3>
                <p className="text-muted-foreground leading-relaxed">Marketed, launched, and accelerated 700+ projects since 2024</p>
              </div>

              <div className="flex flex-col items-center text-center group">
                <div className="p-4 rounded-2xl bg-chart-2/10 group-hover:bg-chart-2/20 transition-colors mb-6">
                  <Users className="h-8 w-8 text-chart-2" />
                </div>
                <h3 className="font-semibold text-xl mb-3">Incubator Growth</h3>
                <p className="text-muted-foreground leading-relaxed">Expanded our incubator to include over 45 startups in less than 8 months, including 3 unicorns</p>
              </div>

              <div className="flex flex-col items-center text-center group">
                <div className="p-4 rounded-2xl bg-chart-3/10 group-hover:bg-chart-3/20 transition-colors mb-6">
                  <Zap className="h-8 w-8 text-chart-3" />
                </div>
                <h3 className="font-semibold text-xl mb-3">Meme Projects</h3>
                <p className="text-muted-foreground leading-relaxed">Collaborated with 50+ meme projects, with our latest being the top-performing launch in the last quarter of 2024</p>
              </div>
            </div>
          </div>

            {/* Media Assets Section */}
            <div className="glass-effect rounded-3xl p-8 sm:p-10 lg:p-12">
              <div className="max-w-4xl mx-auto text-center mb-12">
                <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">Media Assets</h2>
                <p className="text-muted-foreground text-base sm:text-lg leading-relaxed">Reach audiences daily across shows, threads, and research.</p>
              </div>

              <div className="media-cards-grid">
                <div className="media-card-wrapper">
                  <a
                    href="https://twitter.com/MarioNawfal"
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label="Visit @MarioNawfal on X"
                    className="reflective-card rounded-2xl focus:outline-none focus-visible:ring-2 focus-visible:ring-ibc-green/50 block w-full h-full"
                  >
                    <div className="media-card-content h-full p-6 flex flex-col justify-between">
                      <div className="flex items-start gap-4">
                        <div className="profile-avatar flex-shrink-0">
                          <img
                            src="https://pbs.twimg.com/profile_images/1656005685280079873/ykwJzjrg_400x400.jpg"
                            alt="Mario Nawfal"
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.src = `https://ui-avatars.com/api/?name=Mario+Nawfal&background=3b82f6&color=fff&size=96`;
                            }}
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-bold text-lg sm:text-xl mb-1">@MarioNawfal</h3>
                          <p className="text-sm text-muted-foreground leading-relaxed">Daily markets & breaking news</p>
                        </div>
                      </div>
                      
                      <div className="mt-6 flex items-center justify-between">
                        <div className="metrics-badge">
                          {mediaMetrics?.find(m => m.account_handle === 'MarioNawfal')?.total_impressions?.toLocaleString?.() ?? '—'} views
                        </div>
                        <div className="text-muted-foreground flex items-center gap-2 text-sm font-medium">
                          <span>Visit</span>
                          <ExternalLink className="h-4 w-4" />
                        </div>
                      </div>
                    </div>
                  </a>
                </div>

                <div className="media-card-wrapper">
                  <a
                    href="https://twitter.com/RoundtableSpace"
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label="Visit @RoundtableSpace on X"
                    className="reflective-card rounded-2xl focus:outline-none focus-visible:ring-2 focus-visible:ring-ibc-green/50 block w-full h-full"
                  >
                    <div className="media-card-content h-full p-6 flex flex-col justify-between">
                      <div className="flex items-start gap-4">
                        <div className="profile-avatar flex-shrink-0">
                          <img
                            src="https://pbs.twimg.com/profile_images/1650618163998703616/LwYdCkAx_400x400.jpg"
                            alt="Roundtable Space"
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.src = `https://ui-avatars.com/api/?name=Roundtable+Space&background=8b5cf6&color=fff&size=96`;
                            }}
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-bold text-lg sm:text-xl mb-1">@RoundtableSpace</h3>
                          <p className="text-sm text-muted-foreground leading-relaxed">Web3's daily roundtable</p>
                        </div>
                      </div>
                      
                      <div className="mt-6 flex items-center justify-between">
                        <div className="metrics-badge">
                          {mediaMetrics?.find(m => m.account_handle === 'RoundtableSpace')?.total_impressions?.toLocaleString?.() ?? '—'} views
                        </div>
                        <div className="text-muted-foreground flex items-center gap-2 text-sm font-medium">
                          <span>Visit</span>
                          <ExternalLink className="h-4 w-4" />
                        </div>
                      </div>
                    </div>
                  </a>
                </div>

                <div className="media-card-wrapper">
                  <a
                    href="https://twitter.com/Crypto_TownHall"
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label="Visit @Crypto_TownHall on X"
                    className="reflective-card rounded-2xl focus:outline-none focus-visible:ring-2 focus-visible:ring-ibc-green/50 block w-full h-full"
                  >
                    <div className="media-card-content h-full p-6 flex flex-col justify-between">
                      <div className="flex items-start gap-4">
                        <div className="profile-avatar flex-shrink-0">
                          <img
                            src="https://pbs.twimg.com/profile_images/1585659174821617664/TdcCUe3N_400x400.jpg"
                            alt="Crypto TownHall"
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.src = `https://ui-avatars.com/api/?name=Crypto+TownHall&background=ef4444&color=fff&size=96`;
                            }}
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-bold text-lg sm:text-xl mb-1">@Crypto_TownHall</h3>
                          <p className="text-sm text-muted-foreground leading-relaxed">Panel debates with top voices</p>
                        </div>
                      </div>
                      
                      <div className="mt-6 flex items-center justify-between">
                        <div className="metrics-badge">
                          {mediaMetrics?.find(m => m.account_handle === 'Crypto_TownHall')?.total_impressions?.toLocaleString?.() ?? '—'} views
                        </div>
                        <div className="text-muted-foreground flex items-center gap-2 text-sm font-medium">
                          <span>Visit</span>
                          <ExternalLink className="h-4 w-4" />
                        </div>
                      </div>
                    </div>
                  </a>
                </div>
              </div>
            </div>        </div>
      </section>

      {/* Featured Content Section */}
      <section className="content-container py-24">
        <div className="space-y-12">
          <div className="text-center space-y-4">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold">Portfolio Success Stories</h2>
            <p className="text-muted-foreground text-lg sm:text-xl max-w-3xl mx-auto leading-relaxed">
              Countdown shows that spark launches like EtherFi (200x), Pixels (170x), Portal (40x)
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
            {featuredLoading ? (
              Array.from({ length: 6 }).map((_, i) => (
                <Card key={i} className="p-6 lg:p-8 glass-effect">
                  <div className="space-y-4">
                    <div className="flex items-start gap-2">
                      <Skeleton className="h-4 w-4 rounded" />
                      <Skeleton className="h-5 w-3/4" />
                    </div>
                    <Skeleton className="h-4 w-1/2" />
                    <div className="flex gap-4">
                      <Skeleton className="h-3 w-16" />
                      <Skeleton className="h-3 w-16" />
                      <Skeleton className="h-3 w-16" />
                    </div>
                  </div>
                </Card>
              ))
            ) : (
              featuredContent?.slice(0, 6).map((item: any) => (
                <Card key={item._id} className="p-6 lg:p-8 glass-effect card-hover group hover:scale-[1.02] transition-all duration-300">
                  <div className="space-y-5">
                    <div className="space-y-2">
                      <div className="flex items-start justify-between gap-2">
                        <h3 className="font-semibold text-lg lg:text-xl leading-tight line-clamp-2">
                          {item.content_title || `Content from @${item.content_account}`}
                        </h3>
                        <a
                          href={item.content_link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="opacity-0 group-hover:opacity-100 transition-all duration-300 p-2 rounded-lg hover:bg-background/50"
                        >
                          <ExternalLink className="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors" />
                        </a>
                      </div>
                      <p className="text-sm text-muted-foreground">@{item.content_account}</p>
                    </div>

                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        <span>{formatNumber(item.content_views)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="h-4 w-4" />
                         <span>{formatNumber(item.content_likes ?? item.content_listeners)}</span>                      </div>
                      <div className="flex items-center gap-1">
                        <Repeat2 className="h-4 w-4" />
                        <span>{formatNumber(item.twitter_retweets ?? 0)}</span>
                      </div>
                    </div>

                    {item.content_categories && item.content_categories.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {item.content_categories.slice(0, 2).map((category: any) => (
                          <span
                            key={category}
                            className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full"
                          >
                            {category}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </Card>
              ))
            )}
          </div>

          <div className="text-center">
            <Button
              variant="outline"
              size="lg"
              className="flex items-center gap-2 hover:scale-105 transition-all duration-300"
              onClick={handleDashboardClick}
            >
              View All Content
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </section>

      {/* Social Links Section */}
      <section className="content-container py-24">
        <div className="glass-effect rounded-3xl p-10">
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold">Connect With Us</h2>
              <p className="text-muted-foreground text-lg sm:text-xl max-w-3xl mx-auto leading-relaxed">
                Follow our journey across platforms and stay updated with our latest content
              </p>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6 max-w-3xl mx-auto">
              <a
                href="https://x.com/MarioXNawfal"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Follow Mario X Nawfal on X (Twitter)"
                className="flex flex-col items-center gap-3 p-6 sm:p-8 rounded-2xl bg-background/50 hover:bg-background/80 hover:scale-105 transition-all duration-300 group min-h-[120px] sm:min-h-[140px]"
              >
                <div className="p-3 rounded-full bg-blue-500/10 group-hover:bg-blue-500/20 transition-colors">
                  <span className="inline-block h-6 w-6 text-blue-500" aria-hidden="true" role="img">𝕏</span>
                </div>
                <span className="font-semibold">X</span>
              </a>

              <a
                href="https://linkedin.com/company/infinity-blockchain-capital"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Follow Infinity Blockchain Capital on LinkedIn"
                className="flex flex-col items-center gap-3 p-6 sm:p-8 rounded-2xl bg-background/50 hover:bg-background/80 hover:scale-105 transition-all duration-300 group min-h-[120px] sm:min-h-[140px]"
              >
                <div className="p-3 rounded-full bg-blue-600/10 group-hover:bg-blue-600/20 transition-colors">
                  <span className="inline-block h-6 w-6 text-blue-600" aria-hidden="true" role="img">in</span>
                </div>
                <span className="font-semibold">LinkedIn</span>
              </a>

              <a
                href="https://youtube.com/@MarioNawfal"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Subscribe to Mario Nawfal on YouTube"
                className="flex flex-col items-center gap-3 p-6 sm:p-8 rounded-2xl bg-background/50 hover:bg-background/80 hover:scale-105 transition-all duration-300 group min-h-[120px] sm:min-h-[140px]"
              >
                <div className="p-3 rounded-full bg-red-500/10 group-hover:bg-red-500/20 transition-colors">
                  <span className="inline-block h-6 w-6 text-red-500" aria-hidden="true" role="img">▶</span>
                </div>
                <span className="font-semibold">YouTube</span>
              </a>

              <a
                href="https://ibc.capital"
                target="_blank"
                rel="noopener noreferrer"
                aria-label="Visit Infinity Blockchain Capital website"
                className="flex flex-col items-center gap-3 p-6 sm:p-8 rounded-2xl bg-background/50 hover:bg-background/80 hover:scale-105 transition-all duration-300 group min-h-[120px] sm:min-h-[140px]"
              >
                <div className="p-3 rounded-full bg-green-500/10 group-hover:bg-green-500/20 transition-colors">
                  <Globe className="h-6 w-6 text-green-500" aria-hidden="true" />
                </div>
                <span className="font-semibold">Website</span>
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="content-container py-24">
        <div className="glass-effect rounded-3xl p-12 lg:p-16 text-center">
          <div className="space-y-8 max-w-3xl mx-auto">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold">Ready to Explore?</h2>
            <p className="text-muted-foreground text-lg sm:text-xl leading-relaxed">
              Dive into our comprehensive dashboard to discover our most impactful content,
              track performance metrics, and see what drives attention in today's digital landscape.
            </p>
            <div className="flex justify-center">
              <Button
                size="lg"
                className="flex items-center gap-2 text-lg px-8 py-6 hover:scale-105 transition-all duration-300"
                onClick={handleDashboardClick}
              >
                <BarChart3 className="h-5 w-5" />
                Explore Dashboard
                <ArrowRight className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
