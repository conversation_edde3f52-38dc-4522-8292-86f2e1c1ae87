"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { SearchBar } from "@/components/search-bar";
import { FilteredContentGrid } from "@/components/dashboard/FilteredContentGrid";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

interface Filters {
  contentTypes: string[];
  categories: string[];
  sortBy: string;
  sortOrder: string;
}

function TwitterContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [searchQuery, setSearchQuery] = useState(() => searchParams.get("q") || "");
  const [filters, setFilters] = useState<Filters>(() => ({
    contentTypes: searchParams.get("types")?.split(",").filter(Boolean) || ["twitter"],
    categories: searchParams.get("categories")?.split(",").filter(Boolean) || [],
    sortBy: searchParams.get("sort") || "impressions",
    sortOrder: searchParams.get("order") || "desc",
  }));

  // Sync URL parameters when state changes
  useEffect(() => {
    const params = new URLSearchParams();
    const trimmedQuery = searchQuery.trim();

    if (trimmedQuery) params.set("q", trimmedQuery);
    if (filters.contentTypes.length > 0 && !filters.contentTypes.includes("twitter")) {
      params.set("types", filters.contentTypes.join(","));
    }
    if (filters.categories.length > 0) params.set("categories", filters.categories.join(","));
    if (filters.sortBy !== "impressions") params.set("sort", filters.sortBy);
    if (filters.sortOrder !== "desc") params.set("order", filters.sortOrder);

    const nextQuery = params.toString();
    const currentQuery = searchParams.toString();

    if (nextQuery !== currentQuery) {
      router.replace(nextQuery ? `/twitter?${nextQuery}` : "/twitter", { scroll: false });
    }
  }, [filters, searchQuery, router, searchParams]);

  // Sync state with URL changes
  useEffect(() => {
    const currentQuery = searchParams.get("q") || "";
    const currentFilters = {
      contentTypes: searchParams.get("types")?.split(",").filter(Boolean) || ["twitter"],
      categories: searchParams.get("categories")?.split(",").filter(Boolean) || [],
      sortBy: searchParams.get("sort") || "impressions",
      sortOrder: searchParams.get("order") || "desc",
    };

    setSearchQuery(currentQuery);
    setFilters(currentFilters);
  }, [searchParams]);

  const handleSearch = (query: string) => setSearchQuery(query);

  const handleFilter = (filterType: string, values: string[]) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: values,
    }));
  };

  const handleSort = (sortBy: string, sortOrder?: string) => {
    setFilters((prev) => ({
      ...prev,
      sortBy,
      sortOrder: sortOrder || prev.sortOrder,
    }));
  };

  return (
    <div className="min-h-screen gradient-tech">
      <section className="content-container pt-24 pb-12 text-center">
        <div className="mx-auto max-w-4xl space-y-6">
          <span className="inline-flex rounded-full border border-border/40 bg-muted/20 px-4 py-2 text-xs font-semibold uppercase tracking-[0.4em] text-muted-foreground/80">
            Twitter Library
          </span>
          <h1 className="text-4xl sm:text-5xl font-bold leading-tight text-foreground/95">
            Explore the top-performing threads, tweets, and spaces
          </h1>
          <p className="text-base text-muted-foreground">
            Filter by category, campaign, or performance to surface the content formats that deliver the strongest reach right now.
          </p>
          <div className="flex justify-center">
            <Link href="/">
              <Button size="lg" className="flex items-center gap-2">
                Back to landing
                <ArrowRight className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <section className="content-container pb-24">
        <div className="glass-effect rounded-3xl p-8 sm:p-10 lg:p-12 card-hover space-y-10">
          <SearchBar
            onSearch={handleSearch}
            onFilter={handleFilter}
            onSort={handleSort}
            placeholder="Search Twitter content..."
            filters={filters}
            searchQuery={searchQuery}
          />

          <FilteredContentGrid
            searchQuery={searchQuery}
            contentTypes={filters.contentTypes}
            categories={filters.categories}
            sortBy={filters.sortBy as "impressions" | "date"}
            sortOrder={filters.sortOrder as "desc" | "asc"}
          />
        </div>
      </section>
    </div>
  );
}

export default function TwitterPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen gradient-tech">
          <section className="content-container pt-24 pb-12 text-center">
            <div className="mx-auto max-w-4xl space-y-6">
              <div className="h-6 w-32 bg-muted animate-pulse rounded mx-auto" />
              <div className="h-12 w-96 bg-muted animate-pulse rounded mx-auto" />
              <div className="h-4 w-64 bg-muted animate-pulse rounded mx-auto" />
            </div>
          </section>
        </div>
      }
    >
      <TwitterContent />
    </Suspense>
  );
}

