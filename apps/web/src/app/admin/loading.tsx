import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { ListItemSkeleton } from "@/components/skeletons/ListItemSkeleton";

export default function Loading() {
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-64" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-9 w-32 rounded-md" />
            <Skeleton className="h-9 w-28 rounded-md" />
          </div>
        </div>

        <Card className="p-6 space-y-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <ListItemSkeleton key={i} />
          ))}
        </Card>
      </div>
    </div>
  );
}

