"use client";

import { useEffect, useMemo, useState, Suspense } from "react";
import { useQuery, useMutation } from "convex/react";
import type { Id } from "@/../../../convex/_generated/dataModel";
import { api } from "@/../../../convex/_generated/api";
import { AuthDebugPanel } from "@/components/debug/auth-debug";
import { SearchBar } from "@/components/search-bar";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Loader from "@/components/loader";
import { ListItemSkeleton } from "@/components/skeletons/ListItemSkeleton";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { Plus, BarChart3, X, Database, Settings, RefreshCw, Eye, EyeOff, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";
import { Switch } from "@/components/ui/switch";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";


// Import new components and hooks
import { AdminGuard } from "@/components/admin/guards/AdminGuard";
import { ContentCard } from "@/components/admin/content/ContentCard";
import { Pagination } from "@/components/admin/Pagination";
import { BasicInfoSection } from "@/components/admin/forms/sections/BasicInfoSection";
import { MetricInput } from "@/components/admin/forms/fields/MetricInput";
import { MetadataManagement } from "@/components/admin/MetadataManagement";
import { MediaMetricsManagement } from "@/components/admin/MediaMetricsManagement";
import { MultiSelectDropdown } from "@/components/admin/forms/fields/MultiSelectDropdown";
import { TestimonialManagementTab } from "@/components/admin/TestimonialManagementTab";
import { CaseStudyManagementTab } from "@/components/admin/CaseStudyManagementTab";
import { useAdminAuth } from "@/hooks/useAdminAuth";
// import { useContentManagement } from "@/hooks/useContentManagement"; // Archived
import { useSearchAndFilter } from "@/hooks/useSearchAndFilter";
import { useMediaQuery } from "@/hooks/useMediaQuery";
// import { useMetadataManagement } from "@/hooks/useMetadataManagement"; // TODO: Implement this hook for Convex

// Import types and constants
import type { ContentFormData, FormErrors } from "@/types/admin";
import { CONTENT_TYPES, CONTENT_CATEGORIES } from "@/lib/constants/content";
import { extractHostFromUrl } from "@/lib/utils/admin";

const createInitialFormState = (): ContentFormData => ({
  content_link: "",
  content_title: "",
  content_description: "",
  content_categories: [],
  content_types: [],
  content_tags: [],
  content_account: [],
  content_created_date: new Date().toISOString().split('T')[0],
  twitter_content_type: null,
  content_views: 0,
  content_listeners: 0,
  twitter_impressions: 0,
  content_follow_increase: 0,
  host: "",
});

function AdminPageContent() {
  const [activeTab, setActiveTab] = useState<'content' | 'metadata' | 'testimonials' | 'case-studies' | 'sync-configs' | 'media-metrics' | 'dashboard-settings'>('content');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [showForm, setShowForm] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Check if we're on mobile
  const isMobile = useMediaQuery('(max-width: 1023px)');

  // Tab configuration
  const tabs = [
    { id: 'content', label: 'Content Management', icon: Database },
    { id: 'metadata', label: 'Metadata', icon: Settings },
    { id: 'testimonials', label: 'Testimonials', icon: Plus },
    { id: 'case-studies', label: 'Case Studies', icon: Sparkles },
    { id: 'sync-configs', label: 'Sync Configurations', icon: RefreshCw },
    { id: 'media-metrics', label: 'Media Metrics', icon: BarChart3 },
    { id: 'dashboard-settings', label: 'Dashboard Settings', icon: Settings },
  ] as const;

  const currentTab = tabs.find(tab => tab.id === activeTab);

  const { userProfile } = useAdminAuth();
  const {
    searchQuery,
    filters,
    isSearchActive,
    handleSearch,
    handleFilter,
    handleSort
  } = useSearchAndFilter();

  // Content management state
  const [formData, setFormData] = useState<ContentFormData>(createInitialFormState());
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [isEditing, setIsEditing] = useState(false);
  const [editingContentId, setEditingContentId] = useState<Id<"content_pieces"> | null>(null);
  const contentQuery = useQuery(api.content.getAllContent, { page: currentPage, limit: pageSize });
  const createContentMutation = useMutation(api.content.createContent);
  const updateContentMutation = useMutation(api.content.updateContent);
  const deleteContentMutation = useMutation(api.content.deleteContent);
  const metadataQuery = useQuery(api.admin.getMetadataOptions, {});
  const addMetadataValueMutation = useMutation(api.admin.addMetadataValue);
  const setMetadataDisabledMutation = useMutation(api.admin.setMetadataDisabled);
  const deleteMetadataValueMutation = useMutation(api.admin.deleteMetadataValue);

  const contentTypeOptions = useMemo(
    () => metadataQuery?.content_types ?? Array.from(CONTENT_TYPES),
    [metadataQuery],
  );

  const contentCategoryOptions = useMemo(
    () => metadataQuery?.content_categories ?? Array.from(CONTENT_CATEGORIES),
    [metadataQuery],
  );

  const metadataLoading = metadataQuery === undefined;

  // Dashboard settings
  const dashboardSettingsQuery = useQuery(api.dashboardSettings.getDashboardSettings);
  const updateDashboardSettingMutation = useMutation(api.dashboardSettings.updateDashboardSetting);

  type DashboardSettings = {
    twitter_impressions_visible: boolean;
    twitter_likes_visible: boolean;
    twitter_retweets_visible: boolean;
    content_views_visible: boolean;
    content_created_date_visible: boolean;
  };

  const defaultDashboardSettings: DashboardSettings = {
    twitter_impressions_visible: true,
    twitter_likes_visible: true,
    twitter_retweets_visible: true,
    content_views_visible: true,
    content_created_date_visible: true,
  };

  type DashboardSettingKey = keyof DashboardSettings;

  const [dashboardSettings, setDashboardSettings] = useState(defaultDashboardSettings);

  useEffect(() => {
    if (dashboardSettingsQuery) {
      setDashboardSettings((prev) => ({
        ...prev,
        ...dashboardSettingsQuery,
      }));
    }
  }, [dashboardSettingsQuery]);
  
  const resetFormState = () => {
    setFormData(createInitialFormState());
    setFormErrors({});
    setIsEditing(false);
    setEditingContentId(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const errors: FormErrors = {};
    const trimmedLink = formData.content_link.trim();
    if (!trimmedLink) {
      errors.content_link = "Content link is required";
    }

    const trimmedHost = formData.host.trim();
    if (!trimmedHost) {
      errors.host = "Host account is required";
    }

    const createdAtMs = Date.parse(formData.content_created_date);
    if (Number.isNaN(createdAtMs)) {
      errors.content_created_date = "Please provide a valid date";
    }

    const filteredAccounts = formData.content_account
      .map((account) => account.trim().replace(/^@/, ""))
      .filter(Boolean);

    if (filteredAccounts.length === 0) {
      errors.content_account = "At least one content account is required";
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      toast.error("Please fix the highlighted fields before saving.");
      return;
    }

    const payload = {
      content_title: formData.content_title?.trim() || undefined,
      content_description: formData.content_description?.trim() || undefined,
      content_link: trimmedLink,
      content_created_date: Number.isNaN(createdAtMs) ? Date.now() : createdAtMs,
      content_types: formData.content_types.map((type) => type.trim().toLowerCase()),
      content_categories: formData.content_categories.map((category) => category.trim().toLowerCase()),
      content_tags: formData.content_tags.map((tag) => tag.trim().toLowerCase()),
      content_account: filteredAccounts,
      host: trimmedHost,
      twitter_content_type: formData.twitter_content_type || undefined,
      twitter_impressions: formData.twitter_impressions || 0,
      content_views: formData.content_views || 0,
      content_listeners: formData.content_listeners || 0,
      content_follow_increase: formData.content_follow_increase || 0,
      screenshot_urls: [] as string[],
    };

    try {
      if (isEditing && editingContentId) {
        await updateContentMutation({
          id: editingContentId,
          ...payload,
        });
        toast.success("Content updated successfully!");
      } else {
        await createContentMutation({
          content_uuid: crypto.randomUUID(),
          ...payload,
        });
        toast.success("Content created successfully!");
      }
      resetFormState();
      setShowForm(false);
    } catch (error) {
      console.error("Failed to save content", error);
      toast.error("Failed to save content. Please try again.");
    }
  };

  const handleAddMetadataValue = async (
    enumType: 'content_type' | 'content_category',
    rawValue: string,
  ) => {
    const value = rawValue.trim();
    if (!value) {
      const label = enumType === 'content_type' ? 'Content type' : 'Category';
      throw new Error(`${label} cannot be empty`);
    }

    await addMetadataValueMutation({
      enum_type: enumType,
      value,
      created_by: userProfile?.email ?? undefined,
    });
  };

  const handleAddContentType = async (type: string) => handleAddMetadataValue('content_type', type);

  const handleAddContentCategory = async (category: string) =>
    handleAddMetadataValue('content_category', category);

  const handleToggleMetadataValue = async (
    enumType: 'content_type' | 'content_category',
    value: string,
    disabled: boolean,
  ) => {
    await setMetadataDisabledMutation({ enum_type: enumType, value, disabled: !disabled });
  };

  const handleDeleteMetadataValue = async (id: Id<'custom_metadata_values'>) => {
    await deleteMetadataValueMutation({ id });
  };

  // Get filtered content for search functionality using Convex
  const filteredContentQuery = useQuery(api.content.getFilteredContent, {
    search: searchQuery,
    contentTypes: filters.contentTypes,
    categories: filters.categories,
    sortBy: filters.sortBy as "impressions" | "date",
    sortOrder: filters.sortOrder as "desc" | "asc",
    page: 1,
    limit: 50
  });

  const updateStatsMutation = useMutation(api.admin.updateStats);
  
  const handleUpdateStats = async () => {
    try {
      await updateStatsMutation();
      toast.success("Stats updated successfully!");
    } catch (error) {
      toast.error(`Failed to update stats: ${error}`);
    }
  };

  const handleDashboardSettingChange = async (settingKey: DashboardSettingKey, value: boolean) => {
    setDashboardSettings((prev) => ({
      ...prev,
      [settingKey]: value,
    }));

    try {
      await updateDashboardSettingMutation({
        setting_key: settingKey,
        setting_value: value,
        updated_by: userProfile?.email || "admin"
      });
      toast.success("Dashboard setting updated successfully!");
    } catch (error) {
      setDashboardSettings((prev) => ({
        ...prev,
        [settingKey]: !value,
      }));
      toast.error(`Failed to update setting: ${error}`);
    }
  };

  const handleInputChange = (field: keyof ContentFormData, value: any) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value
      };

      // Auto-fill host when content_link is changed
      if (field === 'content_link' && typeof value === 'string' && value.trim()) {
        const extractedHost = extractHostFromUrl(value.trim());
        if (extractedHost && (!prev.host || prev.host === '')) {
          newData.host = extractedHost;
        }
      }

      return newData;
    });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleEditWithForm = (content: any) => {
    const createdDateMs = typeof content.content_created_date === 'number'
      ? content.content_created_date
      : Date.parse(content.content_created_date ?? '');

    const createdDateISO = Number.isNaN(createdDateMs)
      ? new Date().toISOString().split('T')[0]
      : new Date(createdDateMs).toISOString().split('T')[0];

    setIsEditing(true);
    setEditingContentId(content._id as Id<'content_pieces'>);
    setFormErrors({});
    setFormData({
      content_link: content.content_link ?? '',
      content_title: content.content_title ?? '',
      content_description: content.content_description ?? '',
      content_categories: (content.content_categories ?? []).map((item: string) => item.toLowerCase()),
      content_types: (content.content_types ?? []).map((item: string) => item.toLowerCase()),
      content_tags: (content.content_tags ?? []).map((item: string) => item.toLowerCase()),
      content_account: (content.content_account ?? []).map((item: string) => item),
      content_created_date: createdDateISO,
      twitter_content_type: content.twitter_content_type ?? null,
      content_views: content.content_views ?? 0,
      content_listeners: content.content_listeners ?? 0,
      twitter_impressions: content.twitter_impressions ?? 0,
      content_follow_increase: content.content_follow_increase ?? 0,
      host: content.host ?? '',
    });
    setShowForm(true);
  };

  const handleCancelEditWithForm = () => {
    resetFormState();
    setShowForm(false);
  };

  if (process.env.NODE_ENV === 'development') {
    console.log('Admin page loaded');
    console.log('🔍 [ADMIN] Search state:', {
      searchQuery,
      filters,
      isSearchActive,
      filteredResults: filteredContentQuery?.data?.length,
      allContent: contentQuery?.data?.length,
    });
  }

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [pendingDeleteId, setPendingDeleteId] = useState<string | null>(null);

  const requestDelete = (id: string) => {
    console.log("[ADMIN] Request delete clicked", { id });
    setPendingDeleteId(id);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (pendingDeleteId == null) return;
    try {
      await deleteContentMutation({ id: pendingDeleteId as any });
      toast.success("Content deleted successfully");
    } catch (error) {
      console.error("Failed to delete content", error);
      toast.error("Failed to delete content. Please try again.");
    } finally {
      setDeleteDialogOpen(false);
      setPendingDeleteId(null);
    }
  };

  const cancelDelete = () => {
    console.log("[ADMIN] Delete cancelled", { pendingDeleteId });
    setDeleteDialogOpen(false);
    setPendingDeleteId(null);
  };

  return (
    <div className="min-h-screen bg-background p-8 overflow-x-hidden">
      <AuthDebugPanel />
      <div className="max-w-6xl mx-auto w-full">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Admin Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back, {userProfile?.full_name || userProfile?.email}
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={handleUpdateStats}
              variant="outline"
              className="flex items-center gap-2"
            >
              <BarChart3 className="h-4 w-4" />
              Update Stats
            </Button>
            {activeTab === 'content' && (
              isEditing ? (
                <Button
                  onClick={handleCancelEditWithForm}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  Cancel Edit
                </Button>
              ) : (
                <Button
                  onClick={() => setShowForm(!showForm)}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Content
                </Button>
              )
            )}
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="mb-8">
          <div className="border-b border-border">
            {/* Mobile Menu */}
            <div className="lg:hidden">
              <DropdownMenu open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-between mb-4">
                    <div className="flex items-center gap-2">
                      {currentTab && <currentTab.icon className="h-4 w-4" />}
                      <span>{currentTab?.label}</span>
                    </div>
                    <Menu className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-[200px]">
                  {tabs.map((tab) => (
                    <DropdownMenuItem
                      key={tab.id}
                      onClick={() => {
                        setActiveTab(tab.id as any);
                        setMobileMenuOpen(false);
                      }}
                      className={`flex items-center gap-2 ${
                        activeTab === tab.id ? 'bg-accent text-accent-foreground' : ''
                      }`}
                    >
                      <tab.icon className="h-4 w-4" />
                      <span>{tab.label}</span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Desktop Tabs */}
            <nav className="hidden lg:flex -mb-px flex-wrap gap-4 sm:gap-6" aria-label="Tabs">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm shrink-0 ${
                    activeTab === tab.id
                      ? 'border-primary text-primary'
                      : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <tab.icon className="h-4 w-4" />
                    {tab.label}
                  </div>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Content Tab */}
        {activeTab === 'content' && (
          <>
            {/* Search Bar */}
            <div className="mb-8">
              <SearchBar
                onSearch={handleSearch}
                onFilter={handleFilter}
                onSort={handleSort}
                placeholder="Search admin content..."
                filters={filters}
                searchQuery={searchQuery}
              />
            </div>

            {/* Create Content Form */}
            {(showForm || isEditing) && (
              <Card className="p-6 mb-8">
                <h2 className="text-xl font-semibold mb-4">
                  {isEditing ? "Edit Content" : "Create New Content"}
                </h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Basic Information Section */}
                  <BasicInfoSection
                    formData={formData}
                    formErrors={formErrors}
                    onChange={handleInputChange}
                  />

                  {/* Content Metrics Section */}
                  <div className="space-y-4">
                    <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide border-b pb-2">Content Metrics</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      <MetricInput
                        id="content_views"
                        label="Views"
                        value={formData.content_views}
                        onChange={(value) => handleInputChange('content_views', value)}
                        placeholder="Video views (e.g., 1.2m)"
                      />
                      <MetricInput
                        id="content_listeners"
                        label="Listeners"
                        value={formData.content_listeners}
                        onChange={(value) => handleInputChange('content_listeners', value)}
                        placeholder="Audio listeners (e.g., 500k)"
                      />
                      <MetricInput
                        id="twitter_impressions"
                        label="Impressions"
                        value={formData.twitter_impressions}
                        onChange={(value) => handleInputChange('twitter_impressions', value)}
                        placeholder="Twitter impressions (e.g., 2.5m)"
                      />
                    </div>
                  </div>

                  {/* Content Details Section */}
                  <div className="space-y-4">
                    <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide border-b pb-2">Content Details</h3>
                    <div>
                      <Label htmlFor="content_description">Description (optional)</Label>
                      <Input
                        id="content_description"
                        value={formData.content_description || ''}
                        onChange={(e) => handleInputChange('content_description', e.target.value || null)}
                        placeholder="Brief description of the content..."
                      />
                    </div>

                    <div>
                      <Label htmlFor="content_tags">Tags (comma-separated)</Label>
                      <Input
                        id="content_tags"
                        value={formData.content_tags.join(', ')}
                        onChange={(e) => {
                          const array = e.target.value.split(',').map(item => item.trim()).filter(Boolean);
                          handleInputChange('content_tags', array);
                        }}
                        placeholder="tag1, tag2, tag3"
                      />
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <MultiSelectDropdown
                          label="Content Types"
                          value={formData.content_types}
                          options={contentTypeOptions}
                          onChange={(value) => handleInputChange('content_types', value)}
                          placeholder="Select content types..."
                          canAddNew={true}
                          onAddNew={handleAddContentType}
                          addNewLabel="Content Type"
                        />
                      </div>

                      <div>
                        <MultiSelectDropdown
                          label="Categories"
                          value={formData.content_categories}
                          options={contentCategoryOptions}
                          onChange={(value) => handleInputChange('content_categories', value)}
                          placeholder="Select categories..."
                          canAddNew={true}
                          onAddNew={handleAddContentCategory}
                          addNewLabel="Category"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button
                      type="submit"
                      disabled={false}
                    >
                      {isEditing ? "Update Content" : "Create Content"
                      }
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={isEditing ? handleCancelEditWithForm : () => setShowForm(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </Card>
            )}

            {/* Content List */}
            <Card className="p-6">
              <h2 className="text-xl font-semibold mb-4">
                {isSearchActive ? `Search Results${searchQuery ? ` for "${searchQuery}"` : ''}` : 'Manage Content'}
              </h2>

              {(isSearchActive ? (filteredContentQuery === undefined) : (contentQuery === undefined)) ? (
                <div className="space-y-4">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <ListItemSkeleton key={i} />
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {(isSearchActive ? filteredContentQuery?.data : contentQuery?.data)?.map((content: any) => (
                    <ContentCard
                      key={content._id}
                      content={content}
                      onEdit={handleEditWithForm}
                      onDelete={requestDelete}
                      isDeleting={pendingDeleteId === content._id}
                    />
                  ))}

                  {(isSearchActive ? filteredContentQuery?.data : contentQuery?.data)?.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      {isSearchActive
                        ? "No content found matching your search criteria."
                        : "No content found. Create your first entry!"
                      }
                    </div>
                  )}
                </div>
              )}

              {/* Pagination for non-search results */}
              {!isSearchActive && contentQuery && contentQuery.totalPages > 1 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={contentQuery.totalPages}
                  onPageChange={handlePageChange}
                  totalItems={contentQuery.total}
                  itemsPerPage={pageSize}
                />
              )}
            </Card>
          </>
        )}

        {/* Metadata Tab */}
        {activeTab === 'metadata' && (
          <MetadataManagement
            data={metadataQuery}
            isLoading={metadataLoading}
            onAdd={handleAddMetadataValue}
            onToggle={handleToggleMetadataValue}
            onDelete={handleDeleteMetadataValue}
          />
        )}
        <ConfirmDialog
          open={deleteDialogOpen}
          title="Delete this content?"
          description="This action cannot be undone and will permanently remove the content item."
          checkboxLabel="I am sure I want to delete this content"
          confirmLabel="Delete"
          loading={false}
          onConfirm={confirmDelete}
          onCancel={cancelDelete}
        />


        {/* Testimonials Tab */}
        {activeTab === 'testimonials' && (
          <TestimonialManagementTab />
        )}

        {/* Case Studies Tab */}
        {activeTab === 'case-studies' && (
          <CaseStudyManagementTab />
        )}

        {/* Sync Configs Tab */}
        {activeTab === 'sync-configs' && (
          <div className="space-y-8">
            <div className="w-full">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold mb-2">Spreadsheet Sync Configurations</h2>
                <p className="text-muted-foreground">
                  This feature has been moved to a dedicated page for better management. 
                  Click the button below to access the full sync configuration interface.
                </p>
              </div>

              <div className="flex justify-center">
                <Button
                  onClick={() => window.open('/admin/sync-configs', '_blank')}
                  className="flex items-center gap-2"
                  size="lg"
                >
                  <RefreshCw className="h-5 w-5" />
                  Open Sync Configuration Manager
                </Button>
              </div>
              
              <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-6 border rounded-lg">
                  <Database className="h-8 w-8 mx-auto mb-3 text-muted-foreground" />
                  <h3 className="font-semibold mb-2">Multiple Spreadsheets</h3>
                  <p className="text-sm text-muted-foreground">
                    Configure sync for multiple Google Sheets tabs simultaneously
                  </p>
                </div>
                
                <div className="text-center p-6 border rounded-lg">
                  <RefreshCw className="h-8 w-8 mx-auto mb-3 text-muted-foreground" />
                  <h3 className="font-semibold mb-2">Automated Sync</h3>
                  <p className="text-sm text-muted-foreground">
                    Runs automatically every 30 minutes to keep data up-to-date
                  </p>
                </div>
                
                <div className="text-center p-6 border rounded-lg">
                  <BarChart3 className="h-8 w-8 mx-auto mb-3 text-muted-foreground" />
                  <h3 className="font-semibold mb-2">Monitoring & Logs</h3>
                  <p className="text-sm text-muted-foreground">
                    Track sync status, view logs, and monitor performance
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Media Metrics Tab */}
        {activeTab === 'media-metrics' && <MediaMetricsManagement />}

        {/* Dashboard Settings Tab */}
        {activeTab === 'dashboard-settings' && (
          <div className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold mb-2">Dashboard Display Settings</h2>
              <p className="text-muted-foreground">
                Control which metrics are visible on the dashboard Twitter cards
              </p>
            </div>

            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-6 flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Metric Visibility Controls
              </h3>

              {dashboardSettingsQuery === undefined ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Skeleton className="h-5 w-5 rounded" />
                        <div>
                          <Skeleton className="h-4 w-40" />
                          <Skeleton className="h-3 w-64 mt-1" />
                        </div>
                      </div>
                      <Skeleton className="h-6 w-12 rounded-full" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="grid gap-4">
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <BarChart3 className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <div className="font-medium">Twitter Impressions</div>
                          <div className="text-sm text-muted-foreground">
                            Show impression counts on Twitter content cards
                          </div>
                        </div>
                      </div>
                      <Switch
                        checked={dashboardSettings.twitter_impressions_visible}
                        onCheckedChange={(checked) =>
                          handleDashboardSettingChange('twitter_impressions_visible', checked)
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <BarChart3 className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <div className="font-medium">Twitter Likes</div>
                          <div className="text-sm text-muted-foreground">
                            Show like counts on Twitter content cards
                          </div>
                        </div>
                      </div>
                      <Switch
                        checked={dashboardSettings.twitter_likes_visible}
                        onCheckedChange={(checked) =>
                          handleDashboardSettingChange('twitter_likes_visible', checked)
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <BarChart3 className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <div className="font-medium">Twitter Retweets</div>
                          <div className="text-sm text-muted-foreground">
                            Show retweet counts on Twitter content cards
                          </div>
                        </div>
                      </div>
                      <Switch
                        checked={dashboardSettings.twitter_retweets_visible}
                        onCheckedChange={(checked) =>
                          handleDashboardSettingChange('twitter_retweets_visible', checked)
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <BarChart3 className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <div className="font-medium">Content Views</div>
                          <div className="text-sm text-muted-foreground">
                            Show view counts on content cards
                          </div>
                        </div>
                      </div>
                      <Switch
                        checked={dashboardSettings.content_views_visible}
                        onCheckedChange={(checked) =>
                          handleDashboardSettingChange('content_views_visible', checked)
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <BarChart3 className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <div className="font-medium">Content Created Date</div>
                          <div className="text-sm text-muted-foreground">
                            Show creation date on content cards
                          </div>
                        </div>
                      </div>
                      <Switch
                        checked={dashboardSettings.content_created_date_visible}
                        onCheckedChange={(checked) =>
                          handleDashboardSettingChange('content_created_date_visible', checked)
                        }
                      />
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <p className="text-sm text-muted-foreground flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      Changes take effect immediately on the dashboard
                    </p>
                  </div>
                </div>
              )}
            </Card>
          </div>
        )}

      </div>
    </div>
  );
}

export default function AdminPage() {
  return (
    <AdminGuard>
      <Suspense fallback={<div className="min-h-screen flex items-center justify-center"><Loader /></div>}>
        <AdminPageContent />
      </Suspense>
    </AdminGuard>
  );
}
