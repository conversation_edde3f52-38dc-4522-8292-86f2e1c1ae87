"use client";

import { use<PERSON>emo, useState } from "react";
import { useQuery, useMutation } from "convex/react";
import type { Id } from "@/../../../convex/_generated/dataModel";
import { api } from "@/../../../convex/_generated/api";
import { AdminGuard } from "@/components/admin/guards/AdminGuard";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import Loader from "@/components/loader";
import { toast } from "sonner";
import {
  Plus,
  Settings,
  Play,
  Trash2,
  Edit,
  Clock,
  CheckCircle,
  XCircle,
  BarChart3,
  RefreshCw,
  ExternalLink,
  Database,
  History,
} from "lucide-react";
import { ConfirmDialog } from "@/components/ui/confirm-dialog";

interface FormState {
  name: string;
  spreadsheet_id: string;
  tab_name: string;
  is_active: boolean;
  sync_order: string;
  target_table: string;
}

const createInitialFormState = (): FormState => ({
  name: "",
  spreadsheet_id: "",
  tab_name: "Sheet1",
  is_active: true,
  sync_order: "0",
  target_table: "",
});

const formatTimestamp = (value?: number | null) => {
  if (!value) return "Never";
  return new Date(value).toLocaleString();
};

const formatDuration = (value?: number | null) => {
  if (!value) return "—";
  const seconds = Math.round(value / 1000);
  return `${seconds}s`;
};

export default function SyncConfigsPage() {
  const configs = useQuery(api.admin.getSyncConfigs, {});
  const logs = useQuery(api.admin.getSyncLogs, { limit: 20 });
  const stats = useQuery(api.admin.getSyncStats, { days: 7 });

  const createConfigMutation = useMutation(api.admin.createSyncConfig);
  const updateConfigMutation = useMutation(api.admin.updateSyncConfig);
  const deleteConfigMutation = useMutation(api.admin.deleteSyncConfig);
  const triggerSyncMutation = useMutation(api.admin.triggerGoogleSheetsSync);

  const [formState, setFormState] = useState<FormState>(createInitialFormState());
  const [showForm, setShowForm] = useState(false);
  const [showLogs, setShowLogs] = useState(false);
  const [showStats, setShowStats] = useState(false);
  const [editingId, setEditingId] = useState<Id<"spreadsheet_sync_configs"> | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [syncingId, setSyncingId] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [pendingDeleteId, setPendingDeleteId] = useState<Id<"spreadsheet_sync_configs"> | null>(null);

  const filteredLogs = useMemo(() => logs ?? [], [logs]);
  const activeConfigs = useMemo(() => configs ?? [], [configs]);

  const handleStartEditing = (config: any) => {
    setEditingId(config._id as Id<"spreadsheet_sync_configs">);
    setFormState({
      name: config.name ?? "",
      spreadsheet_id: config.spreadsheet_id ?? "",
      tab_name: config.tab_name ?? "",
      is_active: config.is_active !== false,
      sync_order: (config.sync_order ?? 0).toString(),
      target_table: config.target_table ?? "",
    });
    setShowForm(true);
  };

  const resetForm = () => {
    setFormState(createInitialFormState());
    setEditingId(null);
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!formState.name.trim() || !formState.spreadsheet_id.trim() || !formState.tab_name.trim()) {
      toast.error("Please complete all required fields");
      return;
    }

    setSubmitting(true);

    const payload = {
      name: formState.name.trim(),
      spreadsheet_id: formState.spreadsheet_id.trim(),
      tab_name: formState.tab_name.trim(),
      is_active: formState.is_active,
      sync_order: Number.parseInt(formState.sync_order, 10) || 0,
      target_table: formState.target_table.trim() || undefined,
    };

    try {
      if (editingId) {
        await updateConfigMutation({ id: editingId, ...payload });
        toast.success("Sync configuration updated");
      } else {
        await createConfigMutation(payload);
        toast.success("Sync configuration created");
      }
      resetForm();
      setShowForm(false);
    } catch (error) {
      console.error("Failed to save configuration", error);
      toast.error("Unable to save configuration.");
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = (configId: Id<"spreadsheet_sync_configs">) => {
    setPendingDeleteId(configId);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!pendingDeleteId) return;
    try {
      await deleteConfigMutation({ id: pendingDeleteId });
      toast.success("Configuration deleted");
      if (editingId === pendingDeleteId) {
        resetForm();
        setShowForm(false);
      }
    } catch (error) {
      console.error("Failed to delete configuration", error);
      toast.error("Unable to delete configuration.");
    } finally {
      setDeleteDialogOpen(false);
      setPendingDeleteId(null);
    }
  };

  const handleRunSync = async (configId?: Id<"spreadsheet_sync_configs">) => {
    setSyncingId(configId ?? "all");
    try {
      await triggerSyncMutation({ config_id: configId });
      toast.success("Sync has been queued");
    } catch (error) {
      console.error("Failed to trigger sync", error);
      toast.error("Unable to start sync.");
    } finally {
      setSyncingId(null);
    }
  };

  if (configs === undefined) {
    return (
      <AdminGuard>
        <div className="min-h-screen flex items-center justify-center">
          <Loader />
        </div>
      </AdminGuard>
    );
  }

  return (
    <AdminGuard>
      <div className="min-h-screen bg-background p-8">
        <div className="max-w-6xl mx-auto space-y-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold">Sync Configurations</h1>
              <p className="text-muted-foreground">
                Manage Google Sheets sync schedules and monitor their execution.
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={() => setShowStats((prev) => !prev)}
              >
                <BarChart3 className="h-4 w-4" />
                {showStats ? "Hide Stats" : "Show Stats"}
              </Button>
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={() => setShowLogs((prev) => !prev)}
              >
                <History className="h-4 w-4" />
                {showLogs ? "Hide Logs" : "Show Logs"}
              </Button>
              <Button
                className="flex items-center gap-2"
                onClick={() => {
                  resetForm();
                  setShowForm(true);
                }}
              >
                <Plus className="h-4 w-4" />
                Add Configuration
              </Button>
            </div>
          </div>

          {showStats && (
            <Card className="p-6 space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold">Sync Statistics (Last 7 Days)</h2>
                <Badge variant="outline">{stats ? stats.totalSyncs : "—"} total syncs</Badge>
              </div>
              {stats ? (
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="rounded-lg border p-4 text-center">
                    <div className="text-2xl font-bold">{stats.totalSyncs}</div>
                    <div className="text-sm text-muted-foreground">Total Runs</div>
                  </div>
                  <div className="rounded-lg border p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">{stats.successfulSyncs}</div>
                    <div className="text-sm text-muted-foreground">Successful</div>
                  </div>
                  <div className="rounded-lg border p-4 text-center">
                    <div className="text-2xl font-bold text-red-600">{stats.failedSyncs}</div>
                    <div className="text-sm text-muted-foreground">Failed</div>
                  </div>
                  <div className="rounded-lg border p-4 text-center">
                    <div className="text-2xl font-bold">{formatDuration(stats.averageDuration)}</div>
                    <div className="text-sm text-muted-foreground">Avg Duration</div>
                  </div>
                </div>
              ) : (
                <div className="flex justify-center py-8">
                  <Loader />
                </div>
              )}
              {stats?.recentErrors?.length ? (
                <div className="space-y-2">
                  <h3 className="text-sm font-medium uppercase text-muted-foreground">Recent Errors</h3>
                  <div className="space-y-2">
                    {stats.recentErrors.map((error, index) => (
                      <Card key={index} className="border-destructive/40 bg-destructive/5 p-3 text-sm text-destructive">
                        {error}
                      </Card>
                    ))}
                  </div>
                </div>
              ) : null}
            </Card>
          )}

          {showLogs && (
            <Card className="p-6 space-y-4">
              <h2 className="text-lg font-semibold">Recent Sync Logs</h2>
              {filteredLogs.length === 0 ? (
                <p className="text-sm text-muted-foreground">No sync logs found.</p>
              ) : (
                <div className="space-y-3">
                  {filteredLogs.map((log: any) => {
                    const statusIcon =
                      log.status === "completed"
                        ? CheckCircle
                        : log.status === "failed"
                        ? XCircle
                        : log.status === "running"
                        ? RefreshCw
                        : Clock;
                    const Icon = statusIcon;
                    const config = activeConfigs.find((cfg: any) => cfg._id === log.config_id);
                    const statusVariant =
                      log.status === "completed"
                        ? "secondary"
                        : log.status === "failed"
                        ? "destructive"
                        : "outline";

                    return (
                      <div key={log._id} className="rounded-lg border p-4">
                        <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
                          <div className="flex items-center gap-3">
                            <Icon className={`h-5 w-5 ${log.status === "running" ? "animate-spin" : ""}`} />
                            <div>
                              <div className="font-medium">
                                {config ? config.name : log.config_id}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                Started {formatTimestamp(log.started_at)} • Duration {formatDuration(log.duration_ms)}
                              </div>
                            </div>
                          </div>
                          <Badge variant={statusVariant}>{log.status}</Badge>
                        </div>
                        {log.errors && (
                          <pre className="mt-3 whitespace-pre-wrap rounded bg-destructive/10 p-3 text-xs text-destructive">
                            {typeof log.errors === "string" ? log.errors : JSON.stringify(log.errors, null, 2)}
                          </pre>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </Card>
          )}

          {(showForm || editingId) && (
            <Card className="p-6">
              <div className="mb-4 flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold">
                    {editingId ? "Edit Sync Configuration" : "Create Sync Configuration"}
                  </h2>
                  <p className="text-sm text-muted-foreground">
                    Provide the spreadsheet details and optional target table override.
                  </p>
                </div>
                <Settings className="h-5 w-5 text-muted-foreground" />
              </div>
              <form onSubmit={handleSubmit} className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={formState.name}
                    onChange={(event) => setFormState((prev) => ({ ...prev, name: event.target.value }))}
                    placeholder="Weekly metrics sync"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="spreadsheet_id">Spreadsheet ID</Label>
                  <Input
                    id="spreadsheet_id"
                    value={formState.spreadsheet_id}
                    onChange={(event) => setFormState((prev) => ({ ...prev, spreadsheet_id: event.target.value }))}
                    placeholder="Google Sheets ID"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="tab_name">Tab Name</Label>
                  <Input
                    id="tab_name"
                    value={formState.tab_name}
                    onChange={(event) => setFormState((prev) => ({ ...prev, tab_name: event.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="target_table">Target Table (optional)</Label>
                  <Input
                    id="target_table"
                    value={formState.target_table}
                    onChange={(event) => setFormState((prev) => ({ ...prev, target_table: event.target.value }))}
                    placeholder="sheet_custom_name"
                  />
                  <p className="text-xs text-muted-foreground">
                    Leave blank to auto-generate a target table name from the tab.
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sync_order">Sync Order</Label>
                  <Input
                    id="sync_order"
                    value={formState.sync_order}
                    onChange={(event) => setFormState((prev) => ({ ...prev, sync_order: event.target.value }))}
                  />
                  <p className="text-xs text-muted-foreground">Lower numbers run before higher numbers.</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="is_active">Active</Label>
                  <div className="flex items-center gap-2 rounded border p-3">
                    <Switch
                      id="is_active"
                      checked={formState.is_active}
                      onCheckedChange={(checked) => setFormState((prev) => ({ ...prev, is_active: checked }))}
                    />
                    <span className="text-sm text-muted-foreground">
                      {formState.is_active ? "This configuration will run on schedule" : "Configuration is paused"}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2 md:col-span-2">
                  <Button type="submit" disabled={submitting} className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    {editingId ? "Update Configuration" : "Create Configuration"}
                  </Button>
                  {(editingId || showForm) && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        resetForm();
                        setShowForm(false);
                      }}
                    >
                      Cancel
                    </Button>
                  )}
                </div>
              </form>
            </Card>
          )}

          <Card className="p-6 space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold">Configured Spreadsheets</h2>
                <p className="text-sm text-muted-foreground">
                  {activeConfigs.length} configuration{activeConfigs.length === 1 ? "" : "s"} connected.
                </p>
              </div>
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={() => handleRunSync(undefined)}
                disabled={syncingId === "all"}
              >
                {syncingId === "all" ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
                Run All Syncs
              </Button>
            </div>

            {activeConfigs.length === 0 ? (
              <div className="rounded-lg border border-dashed p-6 text-center text-sm text-muted-foreground">
                No sync configurations yet.
              </div>
            ) : (
              <div className="space-y-3">
                {activeConfigs.map((config: any) => (
                  <div
                    key={config._id}
                    className="flex flex-col gap-3 rounded-lg border p-4 md:flex-row md:items-center md:justify-between"
                  >
                    <div className="space-y-1">
                      <div className="flex flex-wrap items-center gap-2">
                        <span className="font-medium">{config.name}</span>
                        <Badge variant={config.is_active !== false ? "secondary" : "destructive"}>
                          {config.is_active !== false ? "Active" : "Paused"}
                        </Badge>
                      </div>
                      <div className="flex flex-wrap items-center gap-3 text-xs text-muted-foreground">
                        <span>
                          <ExternalLink className="mr-1 inline h-3 w-3" /> {config.spreadsheet_id}
                        </span>
                        <span>
                          <Database className="mr-1 inline h-3 w-3" /> Tab: {config.tab_name}
                        </span>
                        <span>
                          <Clock className="mr-1 inline h-3 w-3" /> Last sync: {formatTimestamp(config.last_sync_at)}
                        </span>
                      </div>
                      {config.last_sync_status && (
                        <p className="text-xs text-muted-foreground">
                          Last status: {config.last_sync_status}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-3">
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleRunSync(config._id as Id<'spreadsheet_sync_configs'>)}
                        disabled={syncingId === config._id}
                        title="Run now"
                      >
                        {syncingId === config._id ? (
                          <RefreshCw className="h-4 w-4 animate-spin" />
                        ) : (
                          <Play className="h-4 w-4" />
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleStartEditing(config)}
                        title="Edit configuration"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-destructive"
                        onClick={() => handleDelete(config._id as Id<'spreadsheet_sync_configs'>)}
                        title="Delete configuration"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Card>
        </div>

        <ConfirmDialog
          open={deleteDialogOpen}
          title="Delete this configuration?"
          description="This action cannot be undone and will remove the sync configuration permanently."
          checkboxLabel="I understand this cannot be undone"
          confirmLabel="Delete"
          loading={false}
          onConfirm={confirmDelete}
          onCancel={() => {
            setDeleteDialogOpen(false);
            setPendingDeleteId(null);
          }}
        />
      </div>
    </AdminGuard>
  );
}
