import { Webhook } from 'svix'
import { headers } from 'next/headers'
import type { WebhookEvent } from '@clerk/nextjs/server'
import { ConvexHttpClient } from "convex/browser"
import { api } from "convex/_generated/api"

function getConvexClient() {
  const url = process.env.NEXT_PUBLIC_CONVEX_URL
  if (!url) {
    throw new Error('NEXT_PUBLIC_CONVEX_URL environment variable is required')
  }
  return new ConvexHttpClient(url)
}

export async function POST(req: Request) {
  const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET

  if (!WEBHOOK_SECRET) {
    throw new Error('Please add CLERK_WEBHOOK_SECRET from Clerk Dashboard to .env or .env.local')
  }

  // Get the headers
  const headerPayload = await headers()
  const svix_id = headerPayload.get("svix-id")
  const svix_timestamp = headerPayload.get("svix-timestamp")
  const svix_signature = headerPayload.get("svix-signature")

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response('Error occured -- no svix headers', {
      status: 400
    })
  }

  // Get the body
  const payload = await req.text()
  const body = JSON.parse(payload)

  // Create a new Svix instance with your secret.
  const wh = new Webhook(WEBHOOK_SECRET)

  let evt: WebhookEvent

  // Verify the payload with the headers
  try {
    evt = wh.verify(payload, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent
  } catch (err) {
    console.error('Error verifying webhook:', err)
    return new Response('Error occured', {
      status: 400
    })
  }

  // Handle the webhook
  const { id } = evt.data
  const eventType = evt.type

  console.log(`Webhook with and ID of ${id} and type of ${eventType}`)
  console.log('Webhook body:', body)

  // Handle user creation
  if (eventType === 'user.created') {
    try {
      const convex = getConvexClient()
      await convex.mutation(api.auth.createOrUpdateUser, {
        user_id: evt.data.id,
        email: evt.data.email_addresses[0]?.email_address || '',
        full_name: `${evt.data.first_name || ''} ${evt.data.last_name || ''}`.trim() || undefined,
        role: 'user'
      })
    } catch (error) {
      console.error('Error creating user in Convex:', error)
      return new Response('Error creating user', { status: 500 })
    }
  }

  // Handle user updates
  if (eventType === 'user.updated') {
    try {
      const convex = getConvexClient()
      await convex.mutation(api.auth.createOrUpdateUser, {
        user_id: evt.data.id,
        email: evt.data.email_addresses[0]?.email_address || '',
        full_name: `${evt.data.first_name || ''} ${evt.data.last_name || ''}`.trim() || undefined,
      })
    } catch (error) {
      console.error('Error updating user in Convex:', error)
      return new Response('Error updating user', { status: 500 })
    }
  }

  // Handle user deletion
  if (eventType === 'user.deleted') {
    try {
      const convex = getConvexClient()
      await convex.mutation(api.auth.deleteUser, {
        user_id: evt.data.id || ''
      })
    } catch (error) {
      console.error('Error deleting user in Convex:', error)
      return new Response('Error deleting user', { status: 500 })
    }
  }

  return new Response('', { status: 200 })
}