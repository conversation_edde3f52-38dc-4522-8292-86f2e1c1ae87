"use client";

import { Suspense } from "react";
import Link from "next/link";
import { TestimonialsGrid } from "@/components/dashboard/TestimonialsGrid";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { TestimonialCardSkeleton } from "@/components/skeletons/TestimonialCardSkeleton";

export default function TestimonialsPage() {
  return (
    <div className="min-h-screen gradient-tech">
      <section className="content-container pt-24 pb-12 text-center">
        <div className="mx-auto max-w-4xl space-y-6">
          <span className="inline-flex rounded-full border border-border/40 bg-muted/20 px-4 py-2 text-xs font-semibold uppercase tracking-[0.4em] text-muted-foreground/80">
            Client Testimonials
          </span>
          <h1 className="text-4xl sm:text-5xl font-bold leading-tight text-foreground/95">
            Proof from founders who scaled with IBC
          </h1>
          <p className="text-base text-muted-foreground">
            Filter quotes, playbooks, and recorded outcomes from incubated launches and sponsor partners.
          </p>
          <div className="flex justify-center">
            <Link href="/dashboard">
              <Button size="lg" className="flex items-center gap-2">
                Back to dashboard
                <ArrowRight className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <section className="content-container pb-24">
        <div className="glass-effect rounded-3xl p-8 sm:p-10 lg:p-12 card-hover">
          <Suspense
            fallback={
              <div className="space-y-6">
                <div className="h-8 w-48 bg-muted animate-pulse rounded" />
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Array.from({ length: 9 }).map((_, i) => (
                    <TestimonialCardSkeleton key={i} />
                  ))}
                </div>
              </div>
            }
          >
            <TestimonialsGrid
              showFilters={true}
              showSearch={true}
              showPagination={true}
              itemsPerPage={9}
            />
          </Suspense>
        </div>
      </section>
    </div>
  );
}

