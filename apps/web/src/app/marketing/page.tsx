"use client";

import Link from "next/link";
import { MarketingCaseStudiesGrid } from "@/components/marketing/MarketingCaseStudiesGrid";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

export default function MarketingPage() {
  return (
    <div className="min-h-screen gradient-tech">
      <section className="content-container pt-24 pb-12 text-center">
        <div className="mx-auto max-w-4xl space-y-6">
          <span className="inline-flex rounded-full border border-border/40 bg-muted/20 px-4 py-2 text-xs font-semibold uppercase tracking-[0.38em] text-muted-foreground/80">
            Case Studies
          </span>
          <h1 className="text-4xl sm:text-5xl font-bold leading-tight text-foreground/95">
            Interactive growth stories from the IBC portfolio
          </h1>
          <p className="text-base text-muted-foreground">
            Drill into cohort progression, community expansion, and channel performance across funded launches.
          </p>
          <div className="flex justify-center">
            <Link href="/admin">
              <Button size="lg" variant="outline" className="flex items-center gap-2">
                Manage case studies
                <ArrowRight className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      <section className="content-container pb-24">
        <div className="glass-effect rounded-3xl p-8 sm:p-10 lg:p-12 card-hover">
          <MarketingCaseStudiesGrid initialPageSize={6} showLoadMore={true} />
        </div>
      </section>
    </div>
  );
}

