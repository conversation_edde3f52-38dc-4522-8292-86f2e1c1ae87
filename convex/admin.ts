import { query, mutation, internalQuery, internalMutation, internalAction } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";

// Helper function to check if user is admin
async function requireAdmin(ctx: any) {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error("Authentication required");
  }
  
  const user = await ctx.db
    .query("user_profiles")
    .withIndex("by_user_id", (q: any) => q.eq("user_id", identity.subject))
    .first();
  
  if (!user || user.role !== "admin") {
    throw new Error("Admin privileges required");
  }
  
  return user;
}

const DEFAULT_CONTENT_TYPES = ["twitter", "presskit", "marketing", "incubation", "testimonials"] as const;
const DEFAULT_CONTENT_CATEGORIES = [
  "ai",
  "defi",
  "gaming",
  "memecoin",
  "web2",
  "crypto",
  "politics",
  "news",
  "markets",
  "nft",
  "metaverse",
  "blockchain",
  "bitcoin",
  "ethereum",
  "solana",
  "regulation",
  "technology",
  "finance",
] as const;

const METADATA_ENUM_TYPES = {
  content_type: "content_type",
  content_category: "content_category",
} as const;

type MetadataEnumType = typeof METADATA_ENUM_TYPES[keyof typeof METADATA_ENUM_TYPES];

const sanitizeMetadataValue = (value: string) => value.trim().toLowerCase();

const buildDisabledLookup = (records: any[]) => {
  const lookup = new Map<string, Set<string>>();
  for (const record of records) {
    if (!lookup.has(record.enum_type)) {
      lookup.set(record.enum_type, new Set());
    }
    lookup.get(record.enum_type)!.add(record.enum_value);
  }
  return lookup;
};

// Get all content type mappings (internal)
export const getContentTypeMappings = internalQuery({
  args: {},
  returns: v.array(v.any()),
  handler: async (ctx) => {
    return await ctx.db
      .query("content_type_mappings")
      .withIndex("by_is_active", (q) => q.eq("is_active", true))
      .collect();
  }
});

export const listContentTypeMappings = query({
  args: {},
  returns: v.array(v.any()),
  handler: async (ctx) => {
    await requireAdmin(ctx);
    return await ctx.db.query("content_type_mappings").collect();
  },
});

// Create content type mapping (admin only)
export const createContentTypeMapping = mutation({
  args: {
    source_value: v.string(),
    target_content_type: v.string(),
    twitter_content_type: v.optional(v.string()),
    description: v.optional(v.string()),
    created_by: v.optional(v.string())
  },
  returns: v.id("content_type_mappings"),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    
    return await ctx.db.insert("content_type_mappings", {
      ...args,
      is_active: true
    });
  }
});

// Update content type mapping
export const updateContentTypeMapping = mutation({
  args: {
    id: v.id("content_type_mappings"),
    source_value: v.optional(v.string()),
    target_content_type: v.optional(v.string()),
    twitter_content_type: v.optional(v.string()),
    description: v.optional(v.string()),
    is_active: v.optional(v.boolean()),
    updated_by: v.optional(v.string())
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    const { id, ...updates } = args;
    await ctx.db.patch(id, updates);
    return null;
  }
});

// Delete content type mapping
export const deleteContentTypeMapping = mutation({
  args: {
    id: v.id("content_type_mappings")
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    await ctx.db.delete(args.id);
    return null;
  }
});

const ensureMetadataEnumType = (enumType: string): MetadataEnumType => {
  if (enumType !== METADATA_ENUM_TYPES.content_type && enumType !== METADATA_ENUM_TYPES.content_category) {
    throw new Error("Unsupported metadata enum type");
  }
  return enumType;
};

const metadataValueExists = async (ctx: any, enumType: MetadataEnumType, value: string) => {
  const normalized = sanitizeMetadataValue(value);
  const defaults = enumType === METADATA_ENUM_TYPES.content_type ? DEFAULT_CONTENT_TYPES : DEFAULT_CONTENT_CATEGORIES;
  if (defaults.some((entry) => entry === normalized)) {
    return true;
  }

  const existing = await ctx.db
    .query("custom_metadata_values")
    .withIndex("by_enum_type_value", (q: any) => q.eq("enum_type", enumType).eq("value", normalized))
    .first();

  return !!existing;
};

export const getMetadataOptions = query({
  args: {},
  returns: v.object({
    content_types: v.array(v.string()),
    content_categories: v.array(v.string()),
    contentTypesMeta: v.array(
      v.object({
        value: v.string(),
        isDefault: v.boolean(),
        isCustom: v.boolean(),
        isDisabled: v.boolean(),
        id: v.optional(v.id("custom_metadata_values")),
      }),
    ),
    contentCategoriesMeta: v.array(
      v.object({
        value: v.string(),
        isDefault: v.boolean(),
        isCustom: v.boolean(),
        isDisabled: v.boolean(),
        id: v.optional(v.id("custom_metadata_values")),
      }),
    ),
  }),
  handler: async (ctx) => {
    await requireAdmin(ctx);

    const disabledRecords = await ctx.db.query("disabled_enum_values").collect();
    const customRecords = await ctx.db.query("custom_metadata_values").collect();

    const disabledLookup = buildDisabledLookup(disabledRecords);

    const buildMeta = (enumType: MetadataEnumType, defaults: readonly string[]) => {
      const custom = customRecords.filter((record: any) => record.enum_type === enumType);
      const disabled = disabledLookup.get(enumType) ?? new Set<string>();

      const defaultMeta = defaults.map((value) => ({
        value,
        isDefault: true,
        isCustom: false,
        isDisabled: disabled.has(value),
        id: undefined,
      }));

      const customMeta = custom.map((record: any) => ({
        value: record.value,
        isDefault: false,
        isCustom: true,
        isDisabled: disabled.has(record.value),
        id: record._id,
      }));

      const activeDefaults = defaults.filter((value) => !disabled.has(value));
      const activeCustom = custom
        .filter((record: any) => !disabled.has(record.value))
        .map((record: any) => record.value)
        .sort();

      return {
        meta: [...defaultMeta, ...customMeta],
        activeValues: [...activeDefaults, ...activeCustom],
      };
    };

    const contentTypeData = buildMeta(METADATA_ENUM_TYPES.content_type, DEFAULT_CONTENT_TYPES);
    const contentCategoryData = buildMeta(METADATA_ENUM_TYPES.content_category, DEFAULT_CONTENT_CATEGORIES);

    return {
      content_types: contentTypeData.activeValues,
      content_categories: contentCategoryData.activeValues,
      contentTypesMeta: contentTypeData.meta,
      contentCategoriesMeta: contentCategoryData.meta,
    };
  },
});

export const addMetadataValue = mutation({
  args: {
    enum_type: v.union(v.literal("content_type"), v.literal("content_category")),
    value: v.string(),
    created_by: v.optional(v.string()),
  },
  returns: v.id("custom_metadata_values"),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    const enumType = ensureMetadataEnumType(args.enum_type);
    const sanitized = sanitizeMetadataValue(args.value);
    if (!sanitized) {
      throw new Error("Value cannot be empty");
    }

    const defaults = enumType === METADATA_ENUM_TYPES.content_type ? DEFAULT_CONTENT_TYPES : DEFAULT_CONTENT_CATEGORIES;
    if (defaults.some((entry) => entry === sanitized)) {
      throw new Error("This value already exists as a default option");
    }

    const existing = await ctx.db
      .query("custom_metadata_values")
      .withIndex("by_enum_type_value", (q: any) => q.eq("enum_type", enumType).eq("value", sanitized))
      .first();

    if (existing) {
      throw new Error("This value already exists");
    }

    return await ctx.db.insert("custom_metadata_values", {
      enum_type: enumType,
      value: sanitized,
      created_at: Date.now(),
      created_by: args.created_by,
    });
  },
});

export const setMetadataDisabled = mutation({
  args: {
    enum_type: v.union(v.literal("content_type"), v.literal("content_category")),
    value: v.string(),
    disabled: v.boolean(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    const enumType = ensureMetadataEnumType(args.enum_type);
    const sanitized = sanitizeMetadataValue(args.value);

    if (!(await metadataValueExists(ctx, enumType, sanitized))) {
      throw new Error("Metadata value does not exist");
    }

    const existing = await ctx.db
      .query("disabled_enum_values")
      .withIndex("by_enum_type", (q: any) => q.eq("enum_type", enumType))
      .filter((q: any) => q.eq(q.field("enum_value"), sanitized))
      .first();

    if (args.disabled) {
      if (!existing) {
        await ctx.db.insert("disabled_enum_values", {
          enum_type: enumType,
          enum_value: sanitized,
          disabled_at: Date.now(),
        });
      }
    } else if (existing) {
      await ctx.db.delete(existing._id);
    }

    return null;
  },
});

export const deleteMetadataValue = mutation({
  args: {
    id: v.id("custom_metadata_values"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    const record = await ctx.db.get(args.id);
    if (!record) {
      throw new Error("Metadata value not found");
    }

    await ctx.db.delete(args.id);

    const disabledRecord = await ctx.db
      .query("disabled_enum_values")
      .withIndex("by_enum_type", (q: any) => q.eq("enum_type", record.enum_type))
      .filter((q: any) => q.eq(q.field("enum_value"), record.value))
      .first();

    if (disabledRecord) {
      await ctx.db.delete(disabledRecord._id);
    }

    return null;
  },
});

// Get media account metrics
export const getMediaAccountMetrics = query({
  args: {},
  returns: v.array(v.any()),
  handler: async (ctx) => {
    let isAdmin = false;
    const identity = await ctx.auth.getUserIdentity();

    if (identity) {
      const currentUser = await ctx.db
        .query("user_profiles")
        .withIndex("by_user_id", (q: any) => q.eq("user_id", identity.subject))
        .first();

      isAdmin = currentUser?.role === "admin";
    }

    const metrics = await ctx.db
      .query("media_account_metrics")
      .withIndex("by_display_order")
      .order("asc")
      .collect();

    if (isAdmin) {
      return metrics;
    }

    return metrics.filter((metric: any) => metric.is_active ?? true);
  }
});

// Create media account metric
export const createMediaAccountMetric = mutation({
  args: {
    account_handle: v.string(),
    display_name: v.string(),
    description: v.optional(v.string()),
    profile_image_url: v.optional(v.string()),
    total_impressions: v.optional(v.number()),
    total_views: v.optional(v.number()),
    total_followers: v.optional(v.number()),
    display_order: v.optional(v.number()),
    created_by: v.optional(v.string())
  },
  returns: v.id("media_account_metrics"),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    return await ctx.db.insert("media_account_metrics", {
      ...args,
      is_active: true,
      display_order: args.display_order || 0
    });
  }
});

// Update media account metric
export const updateMediaAccountMetric = mutation({
  args: {
    id: v.id("media_account_metrics"),
    account_handle: v.optional(v.string()),
    display_name: v.optional(v.string()),
    description: v.optional(v.string()),
    profile_image_url: v.optional(v.string()),
    total_impressions: v.optional(v.number()),
    total_views: v.optional(v.number()),
    total_followers: v.optional(v.number()),
    is_active: v.optional(v.boolean()),
    display_order: v.optional(v.number())
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    const { id, ...updates } = args;
    await ctx.db.patch(id, updates);
    return null;
  }
});

export const deleteMediaAccountMetric = mutation({
  args: {
    id: v.id("media_account_metrics"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    await ctx.db.delete(args.id);
    return null;
  },
});

// Get spreadsheet sync configurations
export const getSyncConfigs = query({
  args: {},
  returns: v.array(v.any()),
  handler: async (ctx) => {
    await requireAdmin(ctx);
    return await ctx.db
      .query("spreadsheet_sync_configs")
      .withIndex("by_sync_order")
      .order("asc")
      .collect();
  }
});

// Create sync configuration
export const createSyncConfig = mutation({
  args: {
    name: v.string(),
    spreadsheet_id: v.string(),
    tab_name: v.string(),
    target_table: v.optional(v.string()),
    sync_order: v.optional(v.number()),
    created_by: v.optional(v.string())
  },
  returns: v.id("spreadsheet_sync_configs"),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    // Auto-generate target_table if not provided
    const target_table = args.target_table || `sheet_${args.tab_name.toLowerCase().replace(/[^a-z0-9]/g, '_')}`;
    
    return await ctx.db.insert("spreadsheet_sync_configs", {
      ...args,
      target_table,
      is_active: true,
      sync_order: args.sync_order || 0
    });
  }
});

// Update sync configuration
export const updateSyncConfig = mutation({
  args: {
    id: v.id("spreadsheet_sync_configs"),
    name: v.optional(v.string()),
    spreadsheet_id: v.optional(v.string()),
    tab_name: v.optional(v.string()),
    target_table: v.optional(v.string()),
    is_active: v.optional(v.boolean()),
    sync_order: v.optional(v.number()),
    last_sync_at: v.optional(v.number()),
    last_sync_status: v.optional(v.string()),
    last_sync_details: v.optional(v.string())
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    const { id, ...updates } = args;
    await ctx.db.patch(id, updates);
    return null;
  }
});

export const deleteSyncConfig = mutation({
  args: {
    id: v.id("spreadsheet_sync_configs"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    await ctx.db.delete(args.id);
    return null;
  },
});

// Get sync logs
export const getSyncLogs = query({
  args: {
    limit: v.optional(v.number())
  },
  returns: v.array(v.any()),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    const limit = args.limit || 50;
    return await ctx.db
      .query("sync_logs")
      .withIndex("by_started_at")
      .order("desc")
      .take(limit);
  }
});

// Trigger Google Sheets sync
export const triggerGoogleSheetsSync = mutation({
  args: {
    config_id: v.optional(v.id("spreadsheet_sync_configs"))
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    // Schedule the sync action
    await ctx.scheduler.runAfter(0, internal.googleSheetsSync.performGoogleSheetsSync, {
      config_id: args.config_id
    });
    return null;
  }
});

export const getSyncStats = query({
  args: {
    days: v.optional(v.number()),
  },
  returns: v.object({
    totalSyncs: v.number(),
    successfulSyncs: v.number(),
    failedSyncs: v.number(),
    averageDuration: v.number(),
    recentErrors: v.array(v.string()),
  }),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    const days = Math.max(1, Math.min(args.days ?? 7, 90));
    const since = Date.now() - days * 24 * 60 * 60 * 1000;

    const relevantLogs = await ctx.db
      .query("sync_logs")
      .withIndex("by_started_at", (q: any) => q.gte("started_at", since))
      .order("desc")
      .collect();

    let totalDuration = 0;
    let completedCount = 0;
    let successfulSyncs = 0;
    let failedSyncs = 0;
    const recentErrors: string[] = [];

    for (const log of relevantLogs) {
      if (log.status === "completed") {
        successfulSyncs += 1;
      } else if (log.status === "failed") {
        failedSyncs += 1;
      }

      if (typeof log.duration_ms === "number") {
        totalDuration += log.duration_ms;
        completedCount += 1;
      }

      if (log.errors) {
        try {
          const parsed = JSON.parse(log.errors);
          const errorsArray = Array.isArray(parsed) ? parsed : [parsed];
          for (const entry of errorsArray) {
            if (typeof entry === "string") {
              recentErrors.push(entry);
            } else if (entry && typeof entry.message === "string") {
              recentErrors.push(entry.message);
            }
          }
        } catch {
          if (typeof log.errors === "string") {
            recentErrors.push(log.errors);
          }
        }
      }
    }

    const averageDuration = completedCount > 0 ? Math.round(totalDuration / completedCount) : 0;

    return {
      totalSyncs: relevantLogs.length,
      successfulSyncs,
      failedSyncs,
      averageDuration,
      recentErrors: recentErrors.slice(0, 10),
    };
  },
});

// Internal queries and mutations for sync operations
export const getSyncConfigById = internalQuery({
  args: {
    config_id: v.id("spreadsheet_sync_configs")
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    return await ctx.db.get(args.config_id);
  }
});

export const getActiveSyncConfigs = internalQuery({
  args: {},
  returns: v.array(v.any()),
  handler: async (ctx) => {
    return await ctx.db
      .query("spreadsheet_sync_configs")
      .withIndex("by_is_active", (q) => q.eq("is_active", true))
      .collect();
  }
});

export const createSyncLog = internalMutation({
  args: {
    config_id: v.optional(v.id("spreadsheet_sync_configs")),
    started_at: v.number(),
    status: v.string()
  },
  returns: v.id("sync_logs"),
  handler: async (ctx, args) => {
    return await ctx.db.insert("sync_logs", {
      ...args,
      records_created: 0,
      records_updated: 0,
      records_skipped: 0,
      error_count: 0
    });
  }
});

export const completeSyncLog = internalMutation({
  args: {
    log_id: v.id("sync_logs"),
    completed_at: v.number(),
    status: v.string(),
    duration_ms: v.number(),
    records_created: v.number(),
    records_updated: v.number(),
    records_skipped: v.number(),
    error_count: v.number(),
    errors: v.optional(v.string())
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const { log_id, ...updates } = args;
    await ctx.db.patch(log_id, updates);
    return null;
  }
});

export const updateSyncConfigLastSync = internalMutation({
  args: {
    config_id: v.id("spreadsheet_sync_configs"),
    last_sync_at: v.number(),
    last_sync_status: v.string(),
    last_sync_details: v.optional(v.string())
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const { config_id, ...updates } = args;
    await ctx.db.patch(config_id, updates);
    return null;
  }
});

// Test sync preview
export const previewContentMapping = query({
  args: {
    source_value: v.string()
  },
  returns: v.object({
    target_content_type: v.optional(v.string()),
    twitter_content_type: v.optional(v.string()),
    found: v.boolean()
  }),
  handler: async (ctx, args) => {
    const mapping = await ctx.db
      .query("content_type_mappings")
      .withIndex("by_source_value", (q) => q.eq("source_value", args.source_value))
      .first();
    
    if (mapping && mapping.is_active) {
      return {
        target_content_type: mapping.target_content_type,
        twitter_content_type: mapping.twitter_content_type,
        found: true
      };
    }
    
    return {
      found: false
    };
  }
});

// Update stats mutation
export const updateStats = mutation({
  args: {},
  returns: v.null(),
  handler: async (ctx) => {
    await requireAdmin(ctx);
    
    // Get all content to calculate stats
    const allContent = await ctx.db.query("content_pieces").collect();
    
    const totalImpressions = allContent.reduce((sum, content) => 
      sum + (content.twitter_impressions || 0), 0);
    const totalContent = allContent.length;
    const totalViews = allContent.reduce((sum, content) => 
      sum + (content.content_views || 0), 0);
    
    // Store updated stats
    const now = Date.now();
    
    // Update or create total impressions stat
    await ctx.db.insert("stats", {
      stat_type: "total_impressions",
      stat_value: totalImpressions,
      calculated_at: now
    });
    
    // Update or create total content stat
    await ctx.db.insert("stats", {
      stat_type: "total_content",
      stat_value: totalContent,
      calculated_at: now
    });
    
    // Update or create total views stat
    await ctx.db.insert("stats", {
      stat_type: "total_views",
      stat_value: totalViews,
      calculated_at: now
    });
    
    return null;
  }
});

// Sheet table operations (internal functions for sync)
export const getSheetRowByLink = internalQuery({
  args: {
    table_name: v.string(),
    content_link: v.string()
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    // For now, we'll hardcode to sheet_shows, but this could be made dynamic
    if (args.table_name === "sheet_shows") {
      return await ctx.db
        .query("sheet_shows")
        .withIndex("by_content_link", (q) => q.eq("content_link", args.content_link))
        .first();
    }
    return null;
  }
});

export const createSheetRow = internalMutation({
  args: {
    table_name: v.string(),
    row_id: v.string(),
    content_link: v.string(), // REQUIRED - rows without this should be skipped before calling this function
    client_name: v.optional(v.union(v.string(), v.null())),
    date: v.optional(v.union(v.string(), v.null())),
    show_title: v.optional(v.union(v.string(), v.null())),
    show_topic: v.optional(v.union(v.string(), v.null())),
    show_type: v.optional(v.union(v.string(), v.null())),
    views_listeners: v.optional(v.union(v.string(), v.null())),
    impressions: v.optional(v.union(v.string(), v.null())),
    follow_increase: v.optional(v.union(v.string(), v.null())),
    report_link: v.optional(v.union(v.string(), v.null())),
    raw_data: v.optional(v.string()),
    last_updated: v.number()
  },
  returns: v.any(),
  handler: async (ctx, args) => {
    const { table_name, ...rowData } = args;
    
    // For now, we'll hardcode to sheet_shows, but this could be made dynamic
    if (table_name === "sheet_shows") {
      return await ctx.db.insert("sheet_shows", rowData);
    }
    
    throw new Error(`Unsupported table: ${table_name}`);
  }
});

export const updateSheetRow = internalMutation({
  args: {
    table_name: v.string(),
    id: v.any(),
    row_id: v.optional(v.string()),
    content_link: v.optional(v.string()), // Optional for updates, but if provided must be valid
    client_name: v.optional(v.union(v.string(), v.null())),
    date: v.optional(v.union(v.string(), v.null())),
    show_title: v.optional(v.union(v.string(), v.null())),
    show_topic: v.optional(v.union(v.string(), v.null())),
    show_type: v.optional(v.union(v.string(), v.null())),
    views_listeners: v.optional(v.union(v.string(), v.null())),
    impressions: v.optional(v.union(v.string(), v.null())),
    follow_increase: v.optional(v.union(v.string(), v.null())),
    report_link: v.optional(v.union(v.string(), v.null())),
    raw_data: v.optional(v.string()),
    last_updated: v.optional(v.number())
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const { table_name, id, ...updates } = args;
    
    // For now, we'll hardcode to sheet_shows, but this could be made dynamic
    if (table_name === "sheet_shows") {
      await ctx.db.patch(id, updates);
      return null;
    }
    
    throw new Error(`Unsupported table: ${table_name}`);
  }
});
