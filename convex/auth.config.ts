const domain = process.env.CLERK_JWT_ISSUER_DOMAIN;

if (!domain) {
  throw new Error(
    "Missing CLERK_JWT_ISSUER_DOMAIN environment variable. " +
      "Set it to your Clerk issuer (e.g. https://your-app.clerk.accounts.dev) " +
      "via `convex env set dev CLERK_JWT_ISSUER_DOMAIN <issuer>` for each environment."
  );
}

export default {
  providers: [
    {
      domain,
      applicationID: "convex",
    },
  ]
};
