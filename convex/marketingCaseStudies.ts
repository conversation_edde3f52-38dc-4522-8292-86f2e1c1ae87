import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import type { Infer } from "convex/values";

const chartPointValidator = v.object({
  date: v.string(),
  value: v.number(),
});

const chartSeriesValidator = v.object({
  id: v.string(),
  label: v.string(),
  color_token: v.optional(v.string()),
  stroke_color: v.optional(v.string()),
  variant: v.optional(v.union(v.literal("line"), v.literal("area"))),
  fill_opacity: v.optional(v.number()),
  points: v.array(chartPointValidator),
});

const legendItemValidator = v.object({
  label: v.string(),
  color_token: v.optional(v.string()),
  stroke_color: v.optional(v.string()),
});

const chartSeriesArrayValidator = v.array(chartSeriesValidator);
const legendArrayValidator = v.array(legendItemValidator);

const caseStudyValidator = v.object({
  _id: v.id("marketing_case_studies"),
  _creationTime: v.number(),
  slug: v.string(),
  title: v.string(),
  summary: v.optional(v.string()),
  cta_label: v.optional(v.string()),
  cta_url: v.string(),
  chart_series: chartSeriesArrayValidator,
  legend: v.optional(legendArrayValidator),
  tags: v.optional(v.array(v.string())),
  is_featured: v.optional(v.boolean()),
  is_published: v.optional(v.boolean()),
  display_order: v.optional(v.number()),
  created_at: v.number(),
  updated_at: v.number(),
  created_by: v.optional(v.string()),
  updated_by: v.optional(v.string()),
  background_style: v.optional(v.string()),
});

async function requireAdmin(ctx: any) {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error("Authentication required");
  }

  const user = await ctx.db
    .query("user_profiles")
    .withIndex("by_user_id", (q: any) => q.eq("user_id", identity.subject))
    .first();

  if (!user || user.role !== "admin") {
    throw new Error("Admin privileges required");
  }

  return { user, identity };
}

function normalizeSlug(rawSlug: string): string {
  const sanitized = rawSlug
    .trim()
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/^-+|-+$/g, "");

  if (!sanitized) {
    throw new Error("Slug must contain alphanumeric characters");
  }

  return sanitized;
}

type ChartSeriesInput = Infer<typeof chartSeriesValidator>;

function clampFillOpacity(value: number | undefined): number | undefined {
  if (value === undefined) return undefined;
  if (Number.isNaN(value)) return undefined;
  return Math.min(1, Math.max(0, value));
}

function normalizeSeries(series: ChartSeriesInput): ChartSeriesInput {
  const sortedPoints = [...series.points].sort((a, b) => {
    const aTime = Date.parse(a.date);
    const bTime = Date.parse(b.date);

    if (Number.isNaN(aTime) || Number.isNaN(bTime)) {
      return a.date.localeCompare(b.date);
    }

    return aTime - bTime;
  });

  return {
    ...series,
    points: sortedPoints,
    fill_opacity: clampFillOpacity(series.fill_opacity),
  };
}

function deriveLegend(seriesList: ChartSeriesInput[]) {
  return seriesList.map((series) => ({
    label: series.label,
    color_token: series.color_token,
    stroke_color: series.stroke_color,
  }));
}

export const getMarketingCaseStudies = query({
  args: {
    limit: v.optional(v.number()),
    featuredOnly: v.optional(v.boolean()),
    includeUnpublished: v.optional(v.boolean()),
  },
  returns: v.array(caseStudyValidator),
  handler: async (ctx, args) => {
    const limit = args.limit ?? 6;
    const featuredOnly = args.featuredOnly ?? false;
    const includeUnpublished = args.includeUnpublished ?? false;

    const baseQuery = ctx.db.query("marketing_case_studies");

    const indexedQuery = featuredOnly
      ? baseQuery.withIndex("by_is_featured", (q) => q.eq("is_featured", true))
      : baseQuery.withIndex("by_display_order");

    let caseStudies = featuredOnly
      ? await indexedQuery.collect()
      : await indexedQuery.order("asc").collect();

    if (!includeUnpublished) {
      caseStudies = caseStudies.filter((item) => item.is_published !== false);
    }

    if (!featuredOnly) {
      caseStudies = caseStudies.sort((a, b) => {
        const orderA = a.display_order ?? Number.MAX_SAFE_INTEGER;
        const orderB = b.display_order ?? Number.MAX_SAFE_INTEGER;
        if (orderA === orderB) {
          return b.created_at - a.created_at;
        }
        return orderA - orderB;
      });
    }

    return caseStudies.slice(0, limit);
  },
});

export const getMarketingCaseStudyBySlug = query({
  args: {
    slug: v.string(),
  },
  returns: v.union(caseStudyValidator, v.null()),
  handler: async (ctx, args) => {
    const slug = normalizeSlug(args.slug);

    const result = await ctx.db
      .query("marketing_case_studies")
      .withIndex("by_slug", (q) => q.eq("slug", slug))
      .first();

    return result ?? null;
  },
});

export const listCaseStudies = query({
  args: {
    page: v.optional(v.number()),
    limit: v.optional(v.number()),
    includeUnpublished: v.optional(v.boolean()),
  },
  returns: v.object({
    data: v.array(caseStudyValidator),
    total: v.number(),
    page: v.number(),
    totalPages: v.number(),
  }),
  handler: async (ctx, args) => {
    const page = args.page ?? 1;
    const limit = args.limit ?? 10;
    const includeUnpublished = args.includeUnpublished ?? true;
    const offset = (page - 1) * limit;

    let caseStudies = await ctx.db
      .query("marketing_case_studies")
      .withIndex("by_display_order")
      .order("asc")
      .collect();

    if (!includeUnpublished) {
      caseStudies = caseStudies.filter((item) => item.is_published !== false);
    }

    const total = caseStudies.length;
    const data = caseStudies.slice(offset, offset + limit);

    return {
      data,
      total,
      page,
      totalPages: Math.max(1, Math.ceil(total / limit)),
    };
  },
});

export const createCaseStudy = mutation({
  args: {
    slug: v.string(),
    title: v.string(),
    summary: v.optional(v.string()),
    cta_label: v.optional(v.string()),
    cta_url: v.string(),
    chart_series: chartSeriesArrayValidator,
    legend: v.optional(legendArrayValidator),
    tags: v.optional(v.array(v.string())),
    is_featured: v.optional(v.boolean()),
    is_published: v.optional(v.boolean()),
    display_order: v.optional(v.number()),
    background_style: v.optional(v.string()),
  },
  returns: v.id("marketing_case_studies"),
  handler: async (ctx, args) => {
    const { user, identity } = await requireAdmin(ctx);
    const slug = normalizeSlug(args.slug);

    const existing = await ctx.db
      .query("marketing_case_studies")
      .withIndex("by_slug", (q) => q.eq("slug", slug))
      .first();

    if (existing) {
      throw new Error(`Case study with slug "${slug}" already exists`);
    }

    const preparedSeries = args.chart_series.map(normalizeSeries);
    const legend = args.legend ?? deriveLegend(preparedSeries);
    const timestamp = Date.now();
    const author = user?.email || identity.subject;

    return await ctx.db.insert("marketing_case_studies", {
      slug,
      title: args.title.trim(),
      summary: args.summary?.trim(),
      cta_label: args.cta_label?.trim(),
      cta_url: args.cta_url.trim(),
      chart_series: preparedSeries,
      legend,
      tags: args.tags,
      is_featured: args.is_featured ?? false,
      is_published: args.is_published ?? true,
      display_order: args.display_order,
      background_style: args.background_style,
      created_at: timestamp,
      updated_at: timestamp,
      created_by: author,
      updated_by: author,
    });
  },
});

export const updateCaseStudy = mutation({
  args: {
    id: v.id("marketing_case_studies"),
    slug: v.optional(v.string()),
    title: v.optional(v.string()),
    summary: v.optional(v.string()),
    cta_label: v.optional(v.string()),
    cta_url: v.optional(v.string()),
    chart_series: v.optional(chartSeriesArrayValidator),
    legend: v.optional(legendArrayValidator),
    tags: v.optional(v.array(v.string())),
    is_featured: v.optional(v.boolean()),
    is_published: v.optional(v.boolean()),
    display_order: v.optional(v.number()),
    background_style: v.optional(v.string()),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const { user, identity } = await requireAdmin(ctx);
    const { id, ...updates } = args;

    const current = await ctx.db.get(id);
    if (!current) {
      throw new Error("Case study not found");
    }

    const nextUpdate: any = { updated_at: Date.now(), updated_by: user?.email || identity.subject };

    if (updates.slug !== undefined) {
      const slug = normalizeSlug(updates.slug);
      const conflict = await ctx.db
        .query("marketing_case_studies")
        .withIndex("by_slug", (q) => q.eq("slug", slug))
        .first();

      if (conflict && conflict._id !== id) {
        throw new Error(`Case study with slug "${slug}" already exists`);
      }

      nextUpdate.slug = slug;
    }

    if (updates.title !== undefined) {
      nextUpdate.title = updates.title.trim();
    }

    if (updates.summary !== undefined) {
      nextUpdate.summary = updates.summary?.trim();
    }

    if (updates.cta_label !== undefined) {
      nextUpdate.cta_label = updates.cta_label?.trim();
    }

    if (updates.cta_url !== undefined) {
      nextUpdate.cta_url = updates.cta_url.trim();
    }

    if (updates.chart_series !== undefined) {
      nextUpdate.chart_series = updates.chart_series.map(normalizeSeries);
      if (updates.legend === undefined) {
        nextUpdate.legend = deriveLegend(nextUpdate.chart_series);
      }
    }

    if (updates.legend !== undefined) {
      nextUpdate.legend = updates.legend;
    }

    if (updates.tags !== undefined) {
      nextUpdate.tags = updates.tags;
    }

    if (updates.is_featured !== undefined) {
      nextUpdate.is_featured = updates.is_featured;
    }

    if (updates.is_published !== undefined) {
      nextUpdate.is_published = updates.is_published;
    }

    if (updates.display_order !== undefined) {
      nextUpdate.display_order = updates.display_order;
    }

    if (updates.background_style !== undefined) {
      nextUpdate.background_style = updates.background_style;
    }

    await ctx.db.patch(id, nextUpdate);
    return null;
  },
});

export const deleteCaseStudy = mutation({
  args: {
    id: v.id("marketing_case_studies"),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);

    const existing = await ctx.db.get(args.id);
    if (!existing) {
      throw new Error("Case study not found");
    }

    await ctx.db.delete(args.id);
    return null;
  },
});
