import { internalMutation, mutation } from "./_generated/server";
import { internal } from "./_generated/api";
import { v } from "convex/values";

// Migration to convert existing sheet_shows data to content_pieces
export const migrateSheetShowsToContentPieces = internalMutation({
  args: {},
  returns: v.object({
    created: v.number(),
    skipped: v.number(),
    errors: v.number()
  }),
  handler: async (ctx): Promise<{ created: number; skipped: number; errors: number }> => {
    console.log("🚀 Starting migration: sheet_shows -> content_pieces");
    
    // Get all sheet_shows records
    const sheetShows = await ctx.db.query("sheet_shows").collect();
    console.log(`📊 Found ${sheetShows.length} sheet_shows records to migrate`);
    
    let created = 0;
    let skipped = 0;
    let errors = 0;
    
    // Get content type mappings for proper transformation
    const mappings = await ctx.db.query("content_type_mappings").collect();
    const mappingMap = new Map();
    mappings.forEach(mapping => {
      if (mapping.is_active !== false) {
        mappingMap.set(mapping.source_value, mapping);
      }
    });
    
    for (const show of sheetShows) {
      try {
        // Check if already exists in content_pieces
        const existing = await ctx.db
          .query("content_pieces")
          .filter((q) => q.eq(q.field("content_link"), show.content_link))
          .first();
        
        if (existing) {
          console.log(`⏭️ Skipping existing content: ${show.content_link}`);
          skipped++;
          continue;
        }
        
        // Transform sheet_shows record to content_pieces format
        const contentPiece = transformSheetShowToContentPiece(show, mappingMap);
        if (!contentPiece) {
          console.warn(`⚠️ Failed to transform show: ${show.content_link}`);
          skipped++;
          continue;
        }
        
        // Insert into content_pieces
        await ctx.db.insert("content_pieces", contentPiece);
        console.log(`✅ Migrated: ${show.content_link}`);
        created++;
        
      } catch (error: any) {
        console.error(`❌ Migration error for ${show.content_link}:`, error);
        errors++;
      }
    }
    
    console.log(`✅ Migration completed: ${created} created, ${skipped} skipped, ${errors} errors`);
    return { created, skipped, errors };
  }
});

// Helper function to transform sheet_shows record to content_pieces format
function transformSheetShowToContentPiece(show: any, mappingMap: Map<string, any>): any | null {
  try {
    if (!show.content_link) {
      console.warn("⚠️ Skipping show without content_link");
      return null;
    }

    // Extract host from URL
    const host = extractHostFromUrl(show.content_link, show.client_name);
    
    // Map show type to content types using database mappings
    const showType = show.show_type?.trim();
    const dbMapping = mappingMap.get(showType);
    
    let contentTypes: string[];
    let twitterContentType: string | null = null;
    
    if (dbMapping) {
      contentTypes = [dbMapping.target_content_type];
      twitterContentType = dbMapping.twitter_content_type;
    } else {
      // Use fallback mappings
      contentTypes = mapShowTypeToContentType(showType);
      twitterContentType = getTwitterContentType(showType);
    }
    
    const result: any = {
      content_uuid: generateUUID(),
      content_link: show.content_link,
      host,
      content_account: show.client_name ? [show.client_name] : [],
      content_created_date: parseDate(show.date || ""),
      content_types: contentTypes,
      content_categories: mapTopicToCategories(show.show_topic),
      screenshot_urls: show.report_link ? [show.report_link] : [],
      content_views: parseNumber(show.views_listeners || ""),
      twitter_impressions: parseNumber(show.impressions || ""),
      content_follow_increase: parseNumber(show.follow_increase || ""),
      content_tags: [],
      content_listeners: parseNumber(show.views_listeners || ""),
      twitter_likes: 0,
      twitter_retweets: 0,
      fts: buildFtsString(show.show_title, show.client_name, show.show_topic)
    };

    // Only add optional fields if they have values (undefined = omitted in Convex)
    if (twitterContentType) {
      result.twitter_content_type = twitterContentType;
    }
    if (show.show_title) {
      result.content_title = show.show_title;
      result.content_description = show.show_title; // Use title as description for now
    }

    return result;
  } catch (error) {
    console.error("❌ Failed to transform show:", error, show);
    return null;
  }
}

// Helper functions (copied from googleSheetsSync.ts)
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

function extractHostFromUrl(url: string, clientName: string): string {
  try {
    const match = url.match(/(?:twitter\.com|x\.com)\/(@?[\w]+)(?:\/|$)/);
    if (match && match[1] && match[1] !== 'i') {
      return match[1].replace('@', '').toLowerCase();
    }
  } catch (error) {
    console.warn(`Failed to extract host from URL: ${url}`, error);
  }
  
  return clientName?.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '') || 'unknown';
}

function parseNumber(value: string): number {
  if (!value || value === '-' || value === '') return 0;
  
  value = value.replace('~', '').trim();
  
  if (value.includes('M')) {
    const num = parseFloat(value.replace('M', '').trim());
    return Math.round(num * 1000000);
  }
  
  if (value.toLowerCase().includes('k')) {
    const num = parseFloat(value.replace(/k/i, '').trim());
    return Math.round(num * 1000);
  }
  
  value = value.replace(/,/g, '');
  const parsed = parseFloat(value);
  return isNaN(parsed) ? 0 : Math.round(parsed);
}

function parseDate(dateStr: string): number {
  try {
    if (!dateStr) return Date.now();
    const [day, month, year] = dateStr.split('/');
    const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    return date.getTime();
  } catch (error) {
    console.warn(`Failed to parse date: ${dateStr}`, error);
    return Date.now();
  }
}

function mapTopicToCategories(topic: string): string[] {
  const normalized = topic?.toLowerCase().trim();
  if (!normalized) return [];
  
  const mapping: Record<string, string> = {
    'ai': 'ai',
    'defi': 'defi', 
    'gaming': 'gaming',
    'memecoin': 'memecoin',
    'web3': 'web3',
    'web2': 'web2',
    'rwa': 'blockchain',
    'socialfi': 'web3',
    'multi-chain': 'blockchain',
    'countdown': 'news',
    'cth': 'news'
  };
  
  const category = mapping[normalized];
  return category ? [category] : [];
}

function mapShowTypeToContentType(showType: string): string[] {
  const normalized = showType?.toLowerCase().trim();
  if (!normalized) return ['marketing'];
  
  const mapping: Record<string, string[]> = {
    'interview': ['interview'],
    'space': ['space'],
    'podcast': ['podcast'],
    'video': ['video'],
    'article': ['article'],
    'thread': ['thread'],
    'tweet': ['tweet'],
    'webinar': ['webinar'],
    'ama': ['ama'],
    'demo': ['demo']
  };
  
  return mapping[normalized] || ['marketing'];
}

function getTwitterContentType(showType: string): string | null {
  const normalized = showType?.toLowerCase().trim();
  if (!normalized) return null;
  
  const mapping: Record<string, string> = {
    'space': 'space',
    'interview': 'interview', 
    'tweet': 'tweet',
    'thread': 'thread'
  };
  
  return mapping[normalized] || null;
}

function buildFtsString(title?: string, clientName?: string, topic?: string): string {
  return [
    title || "",
    clientName || "",
    topic || ""
  ].filter(Boolean).join(" ").toLowerCase();
}

// Public wrapper to trigger migration
export const runMigration = mutation({
  args: {},
  returns: v.object({
    created: v.number(),
    skipped: v.number(),
    errors: v.number()
  }),
  handler: async (ctx): Promise<{ created: number; skipped: number; errors: number }> => {
    console.log("🚀 Triggering migration from public endpoint");
    const result = await ctx.runMutation(internal.migrations.migrateSheetShowsToContentPieces, {});
    return result;
  }
});