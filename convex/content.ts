import { query, mutation, internalQuery, internalMutation } from "./_generated/server";
import { v } from "convex/values";
import type { Id } from "./_generated/dataModel";

// Helper function to check if user is admin
async function requireAdmin(ctx: any) {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error("Authentication required");
  }
  
  const user = await ctx.db
    .query("user_profiles")
    .withIndex("by_user_id", (q: any) => q.eq("user_id", identity.subject))
    .first();
  
  if (!user || user.role !== "admin") {
    throw new Error("Admin privileges required");
  }
  
  return user;
}

// Get content filters with usage counts
export const getContentFilters = query({
  args: {},
  returns: v.object({
    contentTypes: v.array(v.string()),
    categories: v.array(v.string()),
    contentTypesWithCounts: v.array(v.object({
      type: v.string(),
      count: v.number()
    })),
    categoriesWithCounts: v.array(v.object({
      category: v.string(),
      count: v.number()
    }))
  }),
  handler: async (ctx) => {
    // Note: This query needs to collect all content to count unique values
    // This is acceptable for filter counts as it's a metadata operation
    const allContent = await ctx.db.query("content_pieces").collect();

    // Count content types
    const contentTypesMap = new Map<string, number>();
    const categoriesMap = new Map<string, number>();

    allContent.forEach(content => {
      // Count content types
      content.content_types.forEach(type => {
        contentTypesMap.set(type, (contentTypesMap.get(type) || 0) + 1);
      });

      // Count categories
      content.content_categories.forEach(category => {
        categoriesMap.set(category, (categoriesMap.get(category) || 0) + 1);
      });
    });

    // Sort by count and then by name
    const contentTypes = Array.from(contentTypesMap.entries())
      .sort((a, b) => b[1] - a[1] || a[0].localeCompare(b[0]))
      .map(([type]) => type);

    const categories = Array.from(categoriesMap.entries())
      .sort((a, b) => b[1] - a[1] || a[0].localeCompare(b[0]))
      .map(([category]) => category);

    return {
      contentTypes,
      categories,
      contentTypesWithCounts: Array.from(contentTypesMap.entries()).map(([type, count]) => ({
        type,
        count
      })),
      categoriesWithCounts: Array.from(categoriesMap.entries()).map(([category, count]) => ({
        category,
        count
      }))
    };
  }
});

// Get Twitter Spaces sorted by impressions
export const getTwitterSpaces = query({
  args: {
    page: v.number(),
    limit: v.number()
  },
  returns: v.object({
    data: v.array(v.any()),
    total: v.number(),
    page: v.number(),
    totalPages: v.number()
  }),
  handler: async (ctx, args) => {
    const offset = (args.page - 1) * args.limit;

    // Get Twitter spaces sorted by impressions using compound index
    const spaces = await ctx.db
      .query("content_pieces")
      .withIndex("by_twitter_content_type_and_impressions", (q) =>
        q.eq("twitter_content_type", "space")
      )
      .order("desc")
      .collect();

    // Apply pagination
    const paginatedSpaces = spaces.slice(offset, offset + args.limit);

    return {
      data: paginatedSpaces,
      total: spaces.length,
      page: args.page,
      totalPages: Math.ceil(spaces.length / args.limit)
    };
  }
});

// Get Marketing Case Studies
export const getMarketingCaseStudies = query({
  args: {
    page: v.number(),
    limit: v.number()
  },
  returns: v.object({
    data: v.array(v.any()),
    total: v.number(),
    page: v.number(),
    totalPages: v.number()
  }),
  handler: async (ctx, args) => {
    const offset = (args.page - 1) * args.limit;

    // Get marketing content using content types index then filter and sort
    const allContent = await ctx.db
      .query("content_pieces")
      .withIndex("by_twitter_impressions")
      .order("desc")
      .collect();

    const marketingContent = allContent.filter(content =>
      content.content_types.includes("marketing")
    );

    // Apply pagination
    const paginatedContent = marketingContent.slice(offset, offset + args.limit);

    return {
      data: paginatedContent,
      total: marketingContent.length,
      page: args.page,
      totalPages: Math.ceil(marketingContent.length / args.limit)
    };
  }
});

// Get Testimonials
export const getTestimonials = query({
  args: {
    page: v.number(),
    limit: v.number()
  },
  returns: v.object({
    data: v.array(v.any()),
    total: v.number(),
    page: v.number(),
    totalPages: v.number()
  }),
  handler: async (ctx, args) => {
    const offset = (args.page - 1) * args.limit;

    // Get testimonials using impressions index then filter
    const allContent = await ctx.db
      .query("content_pieces")
      .withIndex("by_twitter_impressions")
      .order("desc")
      .collect();

    const testimonials = allContent.filter(content =>
      content.content_types.includes("testimonials")
    );

    // Apply pagination
    const paginatedTestimonials = testimonials.slice(offset, offset + args.limit);

    return {
      data: paginatedTestimonials,
      total: testimonials.length,
      page: args.page,
      totalPages: Math.ceil(testimonials.length / args.limit)
    };
  }
});

// Search content using full-text search
export const searchContent = query({
  args: {
    query: v.string(),
    limit: v.number()
  },
  returns: v.array(v.any()),
  handler: async (ctx, args) => {
    if (!args.query || args.query.trim().length === 0) {
      return [];
    }
    
    // Use search index for full-text search
    const results = await ctx.db
      .query("content_pieces")
      .withSearchIndex("search_content", (q) => 
        q.search("fts", args.query)
      )
      .take(args.limit);
    
    return results;
  }
});

// Get all content with pagination
export const getAllContent = query({
  args: {
    page: v.number(),
    limit: v.number()
  },
  returns: v.object({
    data: v.array(v.any()),
    total: v.number(),
    page: v.number(),
    totalPages: v.number()
  }),
  handler: async (ctx, args) => {
    const offset = (args.page - 1) * args.limit;

    // Get content ordered by creation date using proper index
    const allContent = await ctx.db
      .query("content_pieces")
      .withIndex("by_created_date")
      .order("desc")
      .collect();

    // Apply pagination
    const paginatedContent = allContent.slice(offset, offset + args.limit);

    return {
      data: paginatedContent,
      total: allContent.length,
      page: args.page,
      totalPages: Math.ceil(allContent.length / args.limit)
    };
  }
});

// Get Twitter Tweets
export const getTwitterTweets = query({
  args: {},
  returns: v.array(v.any()),
  handler: async (ctx) => {
    // Use compound index to get tweets sorted by impressions directly
    const tweets = await ctx.db
      .query("content_pieces")
      .withIndex("by_twitter_content_type_and_impressions", (q) =>
        q.eq("twitter_content_type", "tweet")
      )
      .order("desc")
      .take(20);

    return tweets;
  }
});

// Get all Twitter content (Spaces + Tweets)
export const getAllTwitterContent = query({
  args: {
    page: v.number(),
    limit: v.number()
  },
  returns: v.object({
    data: v.array(v.any()),
    total: v.number(),
    page: v.number(),
    totalPages: v.number()
  }),
  handler: async (ctx, args) => {
    // Use targeted queries with indexes instead of loading all data into memory

    // Query 1: Get content with explicit twitter_content_type using compound index
    const contentWithType = await ctx.db
      .query("content_pieces")
      .withIndex("by_twitter_content_type", q =>
        q.eq("twitter_content_type", "space")
      )
      .collect();

    const tweetContent = await ctx.db
      .query("content_pieces")
      .withIndex("by_twitter_content_type", q =>
        q.eq("twitter_content_type", "tweet")
      )
      .collect();

    // Query 2: Get content with twitter_impressions > 0 using index (fallback for content without type)
    const contentWithImpressions = await ctx.db
      .query("content_pieces")
      .withIndex("by_twitter_impressions")
      .filter(q => q.gt(q.field("twitter_impressions"), 0))
      .collect();

    // Merge results and deduplicate by _id
    const seen = new Set<string>();
    const allTwitterContent: any[] = [];

    // Add content with explicit types first
    [...contentWithType, ...tweetContent].forEach(content => {
      if (!seen.has(content._id)) {
        seen.add(content._id);
        allTwitterContent.push(content);
      }
    });

    // Add content with impressions that wasn't already included
    contentWithImpressions.forEach(content => {
      if (!seen.has(content._id)) {
        // Additional filtering for content without explicit type
        const hasTwitterUrl = content.content_link && (
          content.content_link.includes('x.com/i/broadcasts/') ||
          content.content_link.includes('x.com/i/spaces/') ||
          content.content_link.includes('twitter.com/i/broadcasts/') ||
          content.content_link.includes('twitter.com/i/spaces/')
        );

        if (hasTwitterUrl || (content.twitter_impressions && content.twitter_impressions > 0)) {
          seen.add(content._id);
          allTwitterContent.push(content);
        }
      }
    });

    // Sort by impressions (descending) - this is still in-memory but on a much smaller dataset
    allTwitterContent.sort((a, b) => (b.twitter_impressions || 0) - (a.twitter_impressions || 0));

    // Apply pagination
    const offset = (args.page - 1) * args.limit;
    const paginatedContent = allTwitterContent.slice(offset, offset + args.limit);

    return {
      data: paginatedContent,
      total: allTwitterContent.length,
      page: args.page,
      totalPages: Math.ceil(allTwitterContent.length / args.limit)
    };
  }
});


// Get content by category
export const getContentByCategory = query({
  args: {
    category: v.string(),
    limit: v.number()
  },
  returns: v.array(v.any()),
  handler: async (ctx, args) => {
    // Get all content sorted by impressions then filter by category
    const allContent = await ctx.db
      .query("content_pieces")
      .withIndex("by_twitter_impressions")
      .order("desc")
      .collect();

    const categoryContent = allContent
      .filter(content => content.content_categories.includes(args.category))
      .slice(0, args.limit);

    return categoryContent;
  }
});

// Get content by type
export const getContentByType = query({
  args: {
    type: v.string(),
    limit: v.number()
  },
  returns: v.array(v.any()),
  handler: async (ctx, args) => {
    // Get all content sorted by impressions then filter by type
    const allContent = await ctx.db
      .query("content_pieces")
      .withIndex("by_twitter_impressions")
      .order("desc")
      .collect();

    const typeContent = allContent
      .filter(content => content.content_types.includes(args.type))
      .slice(0, args.limit);

    return typeContent;
  }
});

// Advanced filtered content with sorting and pagination
export const getFilteredContent = query({
  args: {
    search: v.string(),
    contentTypes: v.array(v.string()),
    categories: v.array(v.string()),
    sortBy: v.union(v.literal("impressions"), v.literal("date")),
    sortOrder: v.union(v.literal("desc"), v.literal("asc")),
    page: v.number(),
    limit: v.number()
  },
  returns: v.object({
    data: v.array(v.any()),
    total: v.number(),
    page: v.number(),
    totalPages: v.number()
  }),
  handler: async (ctx, args) => {
    const offset = (args.page - 1) * args.limit;

    // Handle search queries using search index
    if (args.search && args.search.trim().length > 0) {
      let searchResults = await ctx.db
        .query("content_pieces")
        .withSearchIndex("search_content", (q) =>
          q.search("fts", args.search)
        )
        .collect();

      // Apply filters to search results
      searchResults = applyFilters(searchResults, args.contentTypes, args.categories);

      // Apply sorting
      searchResults = applySorting(searchResults, args.sortBy, args.sortOrder);

      // Apply pagination
      const total = searchResults.length;
      const paginatedResults = searchResults.slice(offset, offset + args.limit);

      return {
        data: paginatedResults,
        total,
        page: args.page,
        totalPages: Math.ceil(total / args.limit)
      };
    }

    // No search - use optimized index queries
    let results: any[] = [];

    // If we have both content types and categories, use compound filtering
    if (args.contentTypes.length > 0 && args.categories.length > 0) {
      // Get results for each combination and merge
      const allResults = new Set();

      for (const contentType of args.contentTypes) {
        for (const category of args.categories) {
          const typeResults = await getContentByTypeAndCategory(ctx, contentType, category, args.sortBy, args.sortOrder);
          typeResults.forEach((result: any) => allResults.add(result));
        }
      }

      results = Array.from(allResults);
    }
    // If we only have content types, use content type filtering
    else if (args.contentTypes.length > 0) {
      for (const contentType of args.contentTypes) {
        const typeResults = await getContentByTypeOptimized(ctx, contentType, args.sortBy, args.sortOrder);
        results = results.concat(typeResults);
      }
    }
    // If we only have categories, use category filtering
    else if (args.categories.length > 0) {
      for (const category of args.categories) {
        const categoryResults = await getContentByCategoryOptimized(ctx, category, args.sortBy, args.sortOrder);
        results = results.concat(categoryResults);
      }
    }
    // No filters - get all content sorted
    else {
      results = await getAllContentSorted(ctx, args.sortBy, args.sortOrder);
    }

    // Remove duplicates and apply final sorting
    const uniqueResults = Array.from(new Map(results.map(r => [r._id, r])).values());
    const sortedResults = applySorting(uniqueResults, args.sortBy, args.sortOrder);

    // Apply pagination
    const total = sortedResults.length;
    const paginatedResults = sortedResults.slice(offset, offset + args.limit);

    return {
      data: paginatedResults,
      total,
      page: args.page,
      totalPages: Math.ceil(total / args.limit)
    };
  }
});

// Helper function to apply filters
function applyFilters(content: any[], contentTypes: string[], categories: string[]) {
  let filtered = content;

  // Apply content type filters
  if (contentTypes.length > 0) {
    filtered = filtered.filter(content => {
      return contentTypes.some(filterType => {
        switch (filterType) {
          case "spaces":
            return content.twitter_content_type === "space";
          case "tweets":
            return content.twitter_content_type === "tweet";
          case "twitter":
            return content.content_link?.includes('x.com') ||
                   content.content_link?.includes('twitter.com') ||
                   (content.twitter_impressions && content.twitter_impressions > 0);
          case "marketing":
          case "presskit":
          case "incubation":
          case "testimonials":
            return content.content_types.includes(filterType);
          default:
            return false;
        }
      });
    });
  }

  // Apply category filters
  if (categories.length > 0) {
    filtered = filtered.filter(content =>
      categories.some(category => content.content_categories.includes(category))
    );
  }

  return filtered;
}

// Helper function to apply sorting
function applySorting(content: any[], sortBy: "impressions" | "date", sortOrder: "asc" | "desc") {
  return content.sort((a, b) => {
    if (sortBy === "date") {
      const dateA = a.content_created_date || 0;
      const dateB = b.content_created_date || 0;
      return sortOrder === "asc" ? dateA - dateB : dateB - dateA;
    } else {
      const impressionsA = a.twitter_impressions || 0;
      const impressionsB = b.twitter_impressions || 0;
      return sortOrder === "asc" ? impressionsA - impressionsB : impressionsB - impressionsA;
    }
  });
}

// Optimized query for content by type using indexes
async function getContentByTypeOptimized(ctx: any, contentType: string, sortBy: "impressions" | "date", sortOrder: "asc" | "desc") {
  const order = sortOrder === "asc" ? "asc" : "desc";

  switch (contentType) {
    case "spaces":
      return await ctx.db
        .query("content_pieces")
        .withIndex(
          sortBy === "impressions"
            ? "by_twitter_content_type_and_impressions"
            : "by_twitter_content_type_and_date",
          (q: any) => q.eq("twitter_content_type", "space")
        )
        .order(order)
        .collect();

    case "tweets":
      return await ctx.db
        .query("content_pieces")
        .withIndex(
          sortBy === "impressions"
            ? "by_twitter_content_type_and_impressions"
            : "by_twitter_content_type_and_date",
          (q: any) => q.eq("twitter_content_type", "tweet")
        )
        .order(order)
        .collect();

    case "marketing":
    case "presskit":
    case "incubation":
    case "testimonials":
      const allContentForType = await ctx.db
        .query("content_pieces")
        .withIndex(sortBy === "impressions" ? "by_twitter_impressions" : "by_created_date")
        .order(order)
        .collect();

      return allContentForType.filter((content: any) => content.content_types.includes(contentType));

    case "twitter":
      // For twitter content, we need to get all and filter by link
      const allTwitterContent = await ctx.db
        .query("content_pieces")
        .withIndex(sortBy === "impressions" ? "by_twitter_impressions" : "by_created_date")
        .order(order)
        .collect();

      return allTwitterContent.filter((content: any) =>
        content.content_link?.includes('x.com') ||
        content.content_link?.includes('twitter.com') ||
        (content.twitter_impressions && content.twitter_impressions > 0)
      );

    default:
      return [];
  }
}

// Optimized query for content by category using indexes
async function getContentByCategoryOptimized(ctx: any, category: string, sortBy: "impressions" | "date", sortOrder: "asc" | "desc") {
  const order = sortOrder === "asc" ? "asc" : "desc";

  const allContent = await ctx.db
    .query("content_pieces")
    .withIndex(sortBy === "impressions" ? "by_twitter_impressions" : "by_created_date")
    .order(order)
    .collect();

  return allContent.filter((content: any) => content.content_categories.includes(category));
}

// Optimized query for content by type and category combination
async function getContentByTypeAndCategory(ctx: any, contentType: string, category: string, sortBy: "impressions" | "date", sortOrder: "asc" | "desc") {
  // Get content by type first, then filter by category
  const typeContent = await getContentByTypeOptimized(ctx, contentType, sortBy, sortOrder);

  return typeContent.filter((content: any) =>
    content.content_categories.includes(category)
  );
}

// Get all content sorted by the specified field
async function getAllContentSorted(ctx: any, sortBy: "impressions" | "date", sortOrder: "asc" | "desc") {
  const order = sortOrder === "asc" ? "asc" : "desc";
  const indexName = sortBy === "impressions" ? "by_twitter_impressions" : "by_created_date";

  return await ctx.db
    .query("content_pieces")
    .withIndex(indexName)
    .order(order)
    .collect();
}

// Get featured content for landing page
export const getFeaturedContent = query({
  args: {},
  returns: v.array(v.any()),
  handler: async (ctx) => {
    // Use impressions index to get top content efficiently
    return await ctx.db
      .query("content_pieces")
      .withIndex("by_twitter_impressions")
      .order("desc")
      .take(6);
  }
});

// Create new content piece
export const createContent = mutation({
  args: {
    content_uuid: v.string(),
    content_title: v.optional(v.string()),
    content_description: v.optional(v.string()),
    content_link: v.string(), // REQUIRED - fixed to match schema
    content_created_date: v.number(),
    content_types: v.array(v.string()),
    content_categories: v.array(v.string()),
    content_tags: v.optional(v.array(v.string())),
    content_account: v.array(v.string()), // REQUIRED - fixed to match schema
    host: v.optional(v.string()),
    twitter_content_type: v.optional(v.string()),
    twitter_impressions: v.optional(v.number()),
    twitter_likes: v.optional(v.number()),
    twitter_retweets: v.optional(v.number()),
    content_views: v.optional(v.number()),
    content_listeners: v.optional(v.number()),
    content_follow_increase: v.optional(v.number()),
    screenshot_urls: v.optional(v.array(v.string()))
  },
  returns: v.id("content_pieces"),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    
    // Generate full-text search string
    const fts = [
      args.content_title || "",
      args.content_description || "",
      args.content_tags?.join(" ") || "",
      args.content_account?.join(" ") || "",
      args.host || ""
    ].filter(Boolean).join(" ").toLowerCase();
    
    return await ctx.db.insert("content_pieces", {
      ...args,
      content_tags: args.content_tags || [],
      screenshot_urls: args.screenshot_urls || [],
      fts
    });
  }
});

// Update content piece
export const updateContent = mutation({
  args: {
    id: v.id("content_pieces"),
    content_title: v.optional(v.string()),
    content_description: v.optional(v.string()),
    content_link: v.optional(v.string()),
    content_created_date: v.optional(v.number()),
    content_types: v.optional(v.array(v.string())),
    content_categories: v.optional(v.array(v.string())),
    content_tags: v.optional(v.array(v.string())),
    content_account: v.optional(v.array(v.string())),
    host: v.optional(v.string()),
    twitter_content_type: v.optional(v.string()),
    twitter_impressions: v.optional(v.number()),
    twitter_likes: v.optional(v.number()),
    twitter_retweets: v.optional(v.number()),
    content_views: v.optional(v.number()),
    content_listeners: v.optional(v.number()),
    content_follow_increase: v.optional(v.number()),
    screenshot_urls: v.optional(v.array(v.string()))
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    const { id, ...updates } = args;
    
    // Get current content to preserve unchanged fields for FTS
    const current = await ctx.db.get(id);
    if (!current) {
      throw new Error("Content not found");
    }
    
    // Update FTS if text fields changed
    let fts = current.fts;
    if (updates.content_title !== undefined || 
        updates.content_description !== undefined || 
        updates.content_tags !== undefined ||
        updates.content_account !== undefined ||
        updates.host !== undefined) {
      
      fts = [
        updates.content_title ?? current.content_title ?? "",
        updates.content_description ?? current.content_description ?? "",
        (updates.content_tags ?? current.content_tags)?.join(" ") ?? "",
        (updates.content_account ?? current.content_account)?.join(" ") ?? "",
        updates.host ?? current.host ?? ""
      ].filter(Boolean).join(" ").toLowerCase();
    }
    
    await ctx.db.patch(id, { ...updates, fts });
    return null;
  }
});

// Delete content piece
export const deleteContent = mutation({
  args: {
    id: v.id("content_pieces")
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    await ctx.db.delete(args.id);
    return null;
  }
});

// Internal functions for Google Sheets sync

// Get content by link (internal)
export const getContentByLink = internalQuery({
  args: {
    content_link: v.string()
  },
  returns: v.union(v.any(), v.null()),
  handler: async (ctx, args) => {
    const content = await ctx.db
      .query("content_pieces")
      .filter((q) => q.eq(q.field("content_link"), args.content_link))
      .first();
    
    return content || null;
  }
});

// Create content piece (internal)
export const createContentPiece = internalMutation({
  args: {
    content_uuid: v.string(),
    content_title: v.optional(v.string()),
    content_description: v.optional(v.string()),
    content_link: v.string(),
    content_created_date: v.number(),
    content_types: v.array(v.string()),
    content_categories: v.array(v.string()),
    content_tags: v.array(v.string()),
    content_account: v.array(v.string()),
    host: v.optional(v.string()),
    twitter_content_type: v.optional(v.string()),
    twitter_impressions: v.optional(v.number()),
    twitter_likes: v.optional(v.number()),
    twitter_retweets: v.optional(v.number()),
    content_views: v.optional(v.number()),
    content_listeners: v.optional(v.number()),
    content_follow_increase: v.optional(v.number()),
    screenshot_urls: v.array(v.string())
  },
  returns: v.id("content_pieces"),
  handler: async (ctx, args) => {
    // Generate full-text search string
    const fts = [
      args.content_title || "",
      args.content_description || "",
      args.content_tags?.join(" ") || "",
      args.content_account?.join(" ") || "",
      args.host || ""
    ].filter(Boolean).join(" ").toLowerCase();
    
    return await ctx.db.insert("content_pieces", {
      ...args,
      fts
    });
  }
});

// Update content piece (internal)
export const updateContentPiece = internalMutation({
  args: {
    id: v.id("content_pieces"),
    content_uuid: v.optional(v.string()),
    content_title: v.optional(v.string()),
    content_description: v.optional(v.string()),
    content_link: v.optional(v.string()),
    content_created_date: v.optional(v.number()),
    content_types: v.optional(v.array(v.string())),
    content_categories: v.optional(v.array(v.string())),
    content_tags: v.optional(v.array(v.string())),
    content_account: v.optional(v.array(v.string())),
    host: v.optional(v.string()),
    twitter_content_type: v.optional(v.string()),
    twitter_impressions: v.optional(v.number()),
    twitter_likes: v.optional(v.number()),
    twitter_retweets: v.optional(v.number()),
    content_views: v.optional(v.number()),
    content_listeners: v.optional(v.number()),
    content_follow_increase: v.optional(v.number()),
    screenshot_urls: v.optional(v.array(v.string()))
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const { id, ...updates } = args;
    
    // Get current content to preserve unchanged fields for FTS
    const current = await ctx.db.get(id);
    if (!current) {
      throw new Error("Content not found");
    }
    
    // Update FTS if text fields changed
    let fts = current.fts;
    if (updates.content_title !== undefined || 
        updates.content_description !== undefined || 
        updates.content_tags !== undefined ||
        updates.content_account !== undefined ||
        updates.host !== undefined) {
      
      fts = [
        updates.content_title ?? current.content_title ?? "",
        updates.content_description ?? current.content_description ?? "",
        (updates.content_tags ?? current.content_tags)?.join(" ") ?? "",
        (updates.content_account ?? current.content_account)?.join(" ") ?? "",
        updates.host ?? current.host ?? ""
      ].filter(Boolean).join(" ").toLowerCase();
    }
    
    await ctx.db.patch(id, { ...updates, fts });
    return null;
  }
});