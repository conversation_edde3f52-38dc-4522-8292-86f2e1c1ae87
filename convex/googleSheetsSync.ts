"use node";

import { internalAction } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import { google } from 'googleapis';

interface CsvRow {
  [key: string]: string;
}

// Internal action to perform Google Sheets sync
export const performGoogleSheetsSync = internalAction({
  args: {
    config_id: v.optional(v.id("spreadsheet_sync_configs"))
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const startTime = Date.now();
    
    try {
      // Create sync log entry
      const logId = await ctx.runMutation(internal.admin.createSyncLog, {
        config_id: args.config_id,
        started_at: startTime,
        status: "running"
      });
      
      // Get sync configs to process
      let configs: any[] = [];
      if (args.config_id) {
        const config = await ctx.runQuery(internal.admin.getSyncConfigById, {
          config_id: args.config_id
        });
        if (config) configs = [config];
      } else {
        configs = await ctx.runQuery(internal.admin.getActiveSyncConfigs, {});
      }
      
      let totalCreated = 0;
      let totalUpdated = 0;
      let totalSkipped = 0;
      let totalErrors = 0;
      const errors: string[] = [];
      
      // Load content type mappings once
      // Load content type mappings - using internal query
      const contentTypeMappings = await ctx.runQuery(internal.admin.getContentTypeMappings, {});
      const mappingMap = new Map();
      contentTypeMappings.forEach((mapping: any) => {
        mappingMap.set(mapping.source_value, mapping);
      });
      
      // Process each config
      for (const config of configs) {
        try {
          console.log(`🔄 [CONVEX-SYNC] Syncing ${config.name} (${config.spreadsheet_id}/${config.tab_name})`);
          
          // Fetch data from Google Sheets
          const rows = await fetchGoogleSheetsData(config.spreadsheet_id, config.tab_name);
          console.log(`📊 [CONVEX-SYNC] Fetched ${rows.length} rows from Google Sheets`);
          
          // Process each row
          for (const row of rows) {
            try {
              // Transform row for sheet-specific table
              const sheetRow = transformRowForSheetTable(row, config);
              if (!sheetRow) {
                totalSkipped++;
                continue;
              }
              
              // Check if row already exists in sheet table
              const existing = await ctx.runQuery(internal.admin.getSheetRowByLink, {
                table_name: config.target_table || "sheet_shows",
                content_link: sheetRow.content_link
              });
              
              if (existing) {
                // Update existing row
                await ctx.runMutation(internal.admin.updateSheetRow, {
                  table_name: config.target_table || "sheet_shows",
                  id: existing._id,
                  ...sheetRow
                });
                totalUpdated++;
                console.log(`✅ [CONVEX-SYNC] Updated in ${config.target_table}: ${sheetRow.content_link}`);
              } else {
                // Create new row
                await ctx.runMutation(internal.admin.createSheetRow, {
                  table_name: config.target_table || "sheet_shows",
                  ...sheetRow
                });
                totalCreated++;
                console.log(`🆕 [CONVEX-SYNC] Created in ${config.target_table}: ${sheetRow.content_link}`);
              }
              
              // Also create/update in content_pieces for backwards compatibility
              try {
                const contentPiece = transformRowToContentPiece(row, mappingMap);
                if (contentPiece) {
                  const existingContent = await ctx.runQuery(internal.content.getContentByLink, {
                    content_link: contentPiece.content_link
                  });
                  
                  if (existingContent) {
                    await ctx.runMutation(internal.content.updateContentPiece, {
                      id: existingContent._id,
                      ...contentPiece
                    });
                  } else {
                    await ctx.runMutation(internal.content.createContentPiece, contentPiece);
                  }
                }
              } catch (contentError: any) {
                console.warn(`⚠️ [CONVEX-SYNC] Failed to sync to content_pieces: ${contentError.message}`);
              }
              
            } catch (rowError: any) {
              totalErrors++;
              errors.push(`Row error: ${rowError.message}`);
              console.error(`❌ [CONVEX-SYNC] Row processing failed:`, rowError);
            }
          }
          
          // Update config last sync time
          await ctx.runMutation(internal.admin.updateSyncConfigLastSync, {
            config_id: config._id,
            last_sync_at: Date.now(),
            last_sync_status: "completed",
            last_sync_details: JSON.stringify({
              created: totalCreated,
              updated: totalUpdated,
              skipped: totalSkipped,
              errors: totalErrors
            })
          });
          
        } catch (error: any) {
          totalErrors++;
          errors.push(`Config ${config.name}: ${error.message}`);
          console.error(`❌ [CONVEX-SYNC] Config sync failed:`, error);
          
          // Update config with error status
          await ctx.runMutation(internal.admin.updateSyncConfigLastSync, {
            config_id: config._id,
            last_sync_at: Date.now(),
            last_sync_status: "failed",
            last_sync_details: JSON.stringify({ error: error.message })
          });
        }
      }
      
      // Update sync log with results
      await ctx.runMutation(internal.admin.completeSyncLog, {
        log_id: logId,
        completed_at: Date.now(),
        status: totalErrors > 0 ? "failed" : "completed",
        duration_ms: Date.now() - startTime,
        records_created: totalCreated,
        records_updated: totalUpdated,
        records_skipped: totalSkipped,
        error_count: totalErrors,
        errors: errors.length > 0 ? JSON.stringify(errors) : undefined
      });
      
      console.log(`✅ [CONVEX-SYNC] Sync completed. Created: ${totalCreated}, Updated: ${totalUpdated}, Skipped: ${totalSkipped}, Errors: ${totalErrors}`);
      
    } catch (error: any) {
      console.error("❌ [CONVEX-SYNC] Sync failed:", error);
      // Could also update sync log with failure here
    }
    
    return null;
  }
});

// Helper function to get Google Sheets authentication
async function getGoogleSheetsAuth() {
  const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
  let rawKey = process.env.GOOGLE_PRIVATE_KEY;

  // Normalize private key across env providers (Vercel, local):
  // - Strip surrounding quotes
  // - Convert escaped newlines (\n) to real newlines
  // - Trim whitespace
  let privateKey = rawKey
    ?.replace(/^"|"$/g, '')
    ?.replace(/^'|'$/g, '')
    ?.replace(/\\n/g, '\n')
    ?.trim();

  if (!serviceAccountEmail || !privateKey) {
    throw new Error('Missing Google Sheets credentials (GOOGLE_SERVICE_ACCOUNT_EMAIL, GOOGLE_PRIVATE_KEY)');
  }

  console.log(`🔑 [CONVEX-SYNC] Using service account: ${serviceAccountEmail}`);

  try {
    const auth = new google.auth.JWT({
      email: serviceAccountEmail,
      key: privateKey,
      scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly']
    });

    await auth.authorize();
    return auth;
  } catch (e: any) {
    console.error('❌ [CONVEX-SYNC] Service account authorization failed:', {
      message: e?.message,
      code: e?.code,
      status: e?.status
    });
    throw e;
  }
}

// Helper function to fetch data from Google Sheets
async function fetchGoogleSheetsData(sheetsId: string, tabName: string = 'Sheet1'): Promise<CsvRow[]> {
  const auth = await getGoogleSheetsAuth();
  const sheets = google.sheets({ version: 'v4', auth });
  
  const response = await sheets.spreadsheets.values.get({
    spreadsheetId: sheetsId,
    range: `${tabName}!A:J`, // Fetch ALL rows with data in columns A-J
    // Ensure values are returned as formatted strings (avoid numeric/date type issues)
    valueRenderOption: 'FORMATTED_VALUE',
    dateTimeRenderOption: 'FORMATTED_STRING'
  });
  
  const rows = response.data.values;
  if (!rows || rows.length === 0) {
    throw new Error('No data found in sheet');
  }
  
  // First row is headers
  const headers = rows[0];
  console.log(`🔍 [CONVEX-SYNC] Headers: ${headers.join(', ')}`);
  
  // Filter out completely empty rows but keep rows with any data
  const dataRows = rows.slice(1).filter((row) => {
    // Check if row has any non-empty cells
    return row && row.some(cell => cell && String(cell).trim() !== '');
  });
  
  console.log(`📊 [CONVEX-SYNC] Found ${dataRows.length} data rows (after filtering empty rows)`);
  
  // Convert rows to objects
  const result = dataRows.map((row) => {
    const obj: any = {};
    headers.forEach((header, index) => {
      obj[header] = row[index] || '';
    });
    return obj as CsvRow;
  });
  
  return result;
}

// Helper function to transform row for sheet-specific table
function transformRowForSheetTable(row: CsvRow, config: any): any | null {
  try {
    // Skip rows without Show Link - this is required
    const showLink = row['Show Link']?.trim();
    if (!showLink || showLink === '') {
      console.warn(`⚠️ [CONVEX-SYNC] Skipping row without Show Link: Client="${row['Client Name']}", Title="${row['Show Title']?.substring(0, 30) || 'no title'}"`);
      return null;
    }

    return {
      row_id: generateRowId(showLink),
      content_link: showLink, // REQUIRED field - guaranteed to exist at this point
      client_name: row['Client Name'] || null,
      date: row['Date'] || null,
      show_title: row['Show Title'] || null,
      show_topic: row['Show Topic'] || null,
      show_type: row['Show Type'] || null,
      views_listeners: row['Views / Listeners'] || null,
      impressions: row['Impressions'] || null,
      follow_increase: row['Follow Increase'] || null,
      report_link: row['Report Link'] || null,
      raw_data: JSON.stringify(row),
      last_updated: Date.now()
    };
  } catch (error) {
    console.error('❌ [CONVEX-SYNC] Failed to transform row for sheet table:', error, row);
    return null;
  }
}

// Helper function to transform a row to a content piece
function transformRowToContentPiece(row: CsvRow, mappingMap: Map<string, any>): any | null {
  try {
    // Skip rows without a show link
    if (!row['Show Link']) {
      console.warn(`⚠️ [CONVEX-SYNC] Skipping row without Show Link: Client="${row['Client Name']}", Title="${row['Show Title']?.substring(0, 30) || 'no title'}"`);
      return null;
    }
    
    const host = extractHostFromUrl(row['Show Link'], row['Client Name']);
    
    // Use database mappings or fallback to hardcoded values
    const showType = row['Show Type']?.trim();
    const dbMapping = mappingMap.get(showType);
    
    let contentTypes: string[];
    let twitterContentType: string | null = null;
    
    if (dbMapping) {
      contentTypes = [dbMapping.target_content_type];
      twitterContentType = dbMapping.twitter_content_type;
      console.log(`✅ [CONVEX-SYNC] Using DB mapping for "${showType}" → ${dbMapping.target_content_type}`);
    } else {
      // Use fallback mappings
      contentTypes = mapShowTypeToContentType(showType);
      twitterContentType = getTwitterContentType(showType);
      console.log(`🔄 [CONVEX-SYNC] Using fallback mapping for "${showType}" → ${contentTypes.join(', ')}`);
    }
    
    return {
      content_uuid: generateUUID(),
      content_link: row['Show Link'],
      host,
      content_account: row['Client Name'] ? [row['Client Name']] : [],
      content_created_date: parseDate(row['Date']),
      content_types: contentTypes,
      twitter_content_type: twitterContentType,
      content_categories: mapTopicToCategories(row['Show Topic']),
      content_title: row['Show Title'] || null,
      content_description: row['Show Title'] || null,
      screenshot_urls: row['Report Link'] ? [row['Report Link']] : [],
      content_views: parseNumber(row['Views / Listeners']),
      twitter_impressions: parseNumber(row['Impressions']),
      content_follow_increase: parseNumber(row['Follow Increase']),
      // Default values
      content_tags: [],
      content_listeners: parseNumber(row['Views / Listeners']),
      twitter_likes: 0,
      twitter_retweets: 0
    };
  } catch (error) {
    console.error('❌ [CONVEX-SYNC] Failed to transform row:', error, row);
    return null;
  }
}

// Helper functions
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

function generateRowId(contentLink: string): string {
  // Create a consistent row ID based on the content link
  return contentLink.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase() + '_' + Date.now().toString().slice(-6);
}

function extractHostFromUrl(url: string, clientName: string): string {
  try {
    const match = url.match(/(?:twitter\.com|x\.com)\/(@?[\w]+)(?:\/|$)/);
    if (match && match[1] && match[1] !== 'i') {
      return match[1].replace('@', '').toLowerCase();
    }
  } catch (error) {
    console.warn(`Failed to extract host from URL: ${url}`, error);
  }
  
  return clientName.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '');
}

function parseNumber(value: string): number {
  if (!value || value === '-' || value === '') return 0;
  
  value = value.replace('~', '').trim();
  
  if (value.includes('M')) {
    const num = parseFloat(value.replace('M', '').trim());
    return Math.round(num * 1000000);
  }
  
  if (value.toLowerCase().includes('k')) {
    const num = parseFloat(value.replace(/k/i, '').trim());
    return Math.round(num * 1000);
  }
  
  value = value.replace(/,/g, '');
  const parsed = parseFloat(value);
  return isNaN(parsed) ? 0 : Math.round(parsed);
}

function parseDate(dateStr: string): number {
  try {
    const [day, month, year] = dateStr.split('/');
    const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    return date.getTime(); // Return timestamp for Convex
  } catch (error) {
    console.warn(`Failed to parse date: ${dateStr}`, error);
    return Date.now(); // Return current timestamp as fallback
  }
}

function mapTopicToCategories(topic: string): string[] {
  const normalized = topic?.toLowerCase().trim();
  if (!normalized) return [];
  
  const mapping: Record<string, string> = {
    'ai': 'ai',
    'defi': 'defi', 
    'gaming': 'gaming',
    'memecoin': 'memecoin',
    'web3': 'web3',
    'web2': 'web2',
    'rwa': 'blockchain',
    'socialfi': 'web3',
    'multi-chain': 'blockchain',
    'countdown': 'news',
    'cth': 'news'
  };
  
  const category = mapping[normalized];
  return category ? [category] : [];
}

function mapShowTypeToContentType(showType: string): string[] {
  const normalized = showType?.toLowerCase().trim();
  if (!normalized) return ['marketing'];
  
  const mapping: Record<string, string[]> = {
    'interview': ['interview'],
    'space': ['space'],
    'podcast': ['podcast'],
    'video': ['video'],
    'article': ['article'],
    'thread': ['thread'],
    'tweet': ['tweet'],
    'webinar': ['webinar'],
    'ama': ['ama'],
    'demo': ['demo']
  };
  
  return mapping[normalized] || ['marketing'];
}

function getTwitterContentType(showType: string): string | null {
  const normalized = showType?.toLowerCase().trim();
  if (!normalized) return null;
  
  const mapping: Record<string, string> = {
    'space': 'space',
    'interview': 'interview',
    'tweet': 'tweet',
    'thread': 'thread'
  };
  
  return mapping[normalized] || null;
}