import { query, mutation, internalQuery, internalMutation } from "./_generated/server";
import { v } from "convex/values";

// Get platform stats (overall metrics)
export const getPlatformStats = query({
  args: {},
  returns: v.object({
    total_content: v.number(),
    total_impressions: v.number(),
    total_views: v.number(),
    total_followers: v.number(),
    avg_engagement_rate: v.number(),
    top_performing_content: v.array(v.any()),
    recent_content_count: v.number()
  }),
  handler: async (ctx) => {
    // Get all content pieces
    const allContent = await ctx.db.query("content_pieces").collect();
    
    // Calculate totals
    const totalContent = allContent.length;
    const totalImpressions = allContent.reduce((sum, content) => 
      sum + (content.twitter_impressions || 0), 0
    );
    const totalViews = allContent.reduce((sum, content) => 
      sum + (content.content_views || 0), 0
    );
    
    // Get media account metrics
    const mediaAccounts = await ctx.db.query("media_account_metrics").collect();
    const totalFollowers = mediaAccounts.reduce((sum, account) => 
      sum + (account.total_followers || 0), 0
    );
    
    // Calculate engagement rate (simplified)
    const totalEngagements = allContent.reduce((sum, content) => 
      sum + (content.twitter_likes || 0) + (content.twitter_retweets || 0), 0
    );
    const avgEngagementRate = totalImpressions > 0 ? 
      (totalEngagements / totalImpressions) * 100 : 0;
    
    // Get top performing content (top 5 by impressions)
    const topContent = allContent
      .sort((a, b) => (b.twitter_impressions || 0) - (a.twitter_impressions || 0))
      .slice(0, 5);
    
    // Recent content (last 30 days)
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const recentContent = allContent.filter(content => 
      content.content_created_date > thirtyDaysAgo
    );
    
    return {
      total_content: totalContent,
      total_impressions: totalImpressions,
      total_views: totalViews,
      total_followers: totalFollowers,
      avg_engagement_rate: Math.round(avgEngagementRate * 100) / 100,
      top_performing_content: topContent,
      recent_content_count: recentContent.length
    };
  }
});

// Get content type breakdown
export const getContentTypeStats = query({
  args: {},
  returns: v.array(v.object({
    type: v.string(),
    count: v.number(),
    total_impressions: v.number(),
    avg_impressions: v.number()
  })),
  handler: async (ctx) => {
    const allContent = await ctx.db.query("content_pieces").collect();
    const typeStats = new Map();
    
    allContent.forEach(content => {
      content.content_types.forEach(type => {
        if (!typeStats.has(type)) {
          typeStats.set(type, {
            type,
            count: 0,
            total_impressions: 0,
            impressions: []
          });
        }
        
        const stats = typeStats.get(type);
        stats.count++;
        stats.total_impressions += content.twitter_impressions || 0;
        stats.impressions.push(content.twitter_impressions || 0);
      });
    });
    
    return Array.from(typeStats.values()).map(stats => ({
      type: stats.type,
      count: stats.count,
      total_impressions: stats.total_impressions,
      avg_impressions: Math.round(stats.total_impressions / stats.count)
    }));
  }
});

// Get category breakdown
export const getCategoryStats = query({
  args: {},
  returns: v.array(v.object({
    category: v.string(),
    count: v.number(),
    total_impressions: v.number(),
    avg_impressions: v.number()
  })),
  handler: async (ctx) => {
    const allContent = await ctx.db.query("content_pieces").collect();
    const categoryStats = new Map();
    
    allContent.forEach(content => {
      content.content_categories.forEach(category => {
        if (!categoryStats.has(category)) {
          categoryStats.set(category, {
            category,
            count: 0,
            total_impressions: 0
          });
        }
        
        const stats = categoryStats.get(category);
        stats.count++;
        stats.total_impressions += content.twitter_impressions || 0;
      });
    });
    
    return Array.from(categoryStats.values()).map(stats => ({
      category: stats.category,
      count: stats.count,
      total_impressions: stats.total_impressions,
      avg_impressions: Math.round(stats.total_impressions / stats.count)
    }));
  }
});

// Get time-based stats (monthly breakdown)
export const getTimeBasedStats = query({
  args: {
    months: v.optional(v.number())
  },
  returns: v.array(v.object({
    period: v.string(),
    content_count: v.number(),
    total_impressions: v.number(),
    total_views: v.number()
  })),
  handler: async (ctx, args) => {
    const monthsBack = args.months || 12;
    const allContent = await ctx.db.query("content_pieces").collect();
    
    // Group by month
    const monthlyStats = new Map();
    const now = new Date();
    
    // Initialize months
    for (let i = 0; i < monthsBack; i++) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const period = date.toISOString().substring(0, 7); // YYYY-MM format
      monthlyStats.set(period, {
        period,
        content_count: 0,
        total_impressions: 0,
        total_views: 0
      });
    }
    
    // Aggregate content by month
    allContent.forEach(content => {
      const contentDate = new Date(content.content_created_date);
      const period = contentDate.toISOString().substring(0, 7);
      
      if (monthlyStats.has(period)) {
        const stats = monthlyStats.get(period);
        stats.content_count++;
        stats.total_impressions += content.twitter_impressions || 0;
        stats.total_views += content.content_views || 0;
      }
    });
    
    return Array.from(monthlyStats.values())
      .sort((a, b) => a.period.localeCompare(b.period));
  }
});

// Get media account performance
export const getMediaAccountStats = query({
  args: {},
  returns: v.array(v.any()),
  handler: async (ctx) => {
    return await ctx.db
      .query("media_account_metrics")
      .withIndex("by_display_order")
      .order("asc")
      .collect();
  }
});

// Get historical stats records
export const getHistoricalStats = query({
  args: {
    stat_type: v.optional(v.string()),
    limit: v.optional(v.number())
  },
  returns: v.array(v.any()),
  handler: async (ctx, args) => {
    const limit = args.limit || 50;
    
    if (args.stat_type) {
      return await ctx.db
        .query("stats")
        .withIndex("by_stat_type", (q) => q.eq("stat_type", args.stat_type!))
        .order("desc")
        .take(limit);
    } else {
      return await ctx.db
        .query("stats")
        .withIndex("by_calculated_at")
        .order("desc")
        .take(limit);
    }
  }
});

// Save stats snapshot
export const saveStatsSnapshot = mutation({
  args: {
    stat_type: v.string(),
    stat_value: v.number(),
    period_start: v.optional(v.number()),
    period_end: v.optional(v.number())
  },
  returns: v.id("stats"),
  handler: async (ctx, args) => {
    return await ctx.db.insert("stats", {
      ...args,
      calculated_at: Date.now()
    });
  }
});

// Calculate and save daily stats
export const calculateDailyStats = internalMutation({
  args: {},
  returns: v.null(),
  handler: async (ctx) => {
    const now = Date.now();
    const dayStart = new Date();
    dayStart.setHours(0, 0, 0, 0);
    const dayEnd = new Date();
    dayEnd.setHours(23, 59, 59, 999);
    
    // Get all content
    const allContent = await ctx.db.query("content_pieces").collect();
    
    // Calculate total impressions
    const totalImpressions = allContent.reduce((sum, content) => 
      sum + (content.twitter_impressions || 0), 0
    );
    
    // Calculate total content count
    const totalContent = allContent.length;
    
    // Calculate average engagement rate
    const totalEngagements = allContent.reduce((sum, content) => 
      sum + (content.twitter_likes || 0) + (content.twitter_retweets || 0), 0
    );
    const avgEngagementRate = totalImpressions > 0 ? 
      (totalEngagements / totalImpressions) * 100 : 0;
    
    // Save stats
    await ctx.db.insert("stats", {
      stat_type: "total_impressions",
      stat_value: totalImpressions,
      period_start: dayStart.getTime(),
      period_end: dayEnd.getTime(),
      calculated_at: now
    });
    
    await ctx.db.insert("stats", {
      stat_type: "total_content",
      stat_value: totalContent,
      period_start: dayStart.getTime(),
      period_end: dayEnd.getTime(),
      calculated_at: now
    });
    
    await ctx.db.insert("stats", {
      stat_type: "avg_engagement_rate",
      stat_value: avgEngagementRate,
      period_start: dayStart.getTime(),
      period_end: dayEnd.getTime(),
      calculated_at: now
    });
    
    return null;
  }
});

// Get performance insights
export const getPerformanceInsights = query({
  args: {},
  returns: v.object({
    best_performing_types: v.array(v.object({
      type: v.string(),
      avg_impressions: v.number()
    })),
    best_performing_categories: v.array(v.object({
      category: v.string(),
      avg_impressions: v.number()
    })),
    trending_topics: v.array(v.string()),
    growth_metrics: v.object({
      content_growth_rate: v.number(),
      impressions_growth_rate: v.number()
    })
  }),
  handler: async (ctx) => {
    const allContent = await ctx.db.query("content_pieces").collect();
    
    // Calculate best performing content types
    const typePerformance = new Map();
    const categoryPerformance = new Map();
    const topics = new Map();
    
    allContent.forEach(content => {
      // Content types
      content.content_types.forEach(type => {
        if (!typePerformance.has(type)) {
          typePerformance.set(type, { impressions: [], count: 0 });
        }
        typePerformance.get(type).impressions.push(content.twitter_impressions || 0);
        typePerformance.get(type).count++;
      });
      
      // Categories
      content.content_categories.forEach(category => {
        if (!categoryPerformance.has(category)) {
          categoryPerformance.set(category, { impressions: [], count: 0 });
        }
        categoryPerformance.get(category).impressions.push(content.twitter_impressions || 0);
        categoryPerformance.get(category).count++;
      });
      
      // Topics (from tags)
      content.content_tags.forEach(tag => {
        topics.set(tag, (topics.get(tag) || 0) + 1);
      });
    });
    
    // Calculate averages and sort
    const bestTypes = Array.from(typePerformance.entries())
      .map(([type, data]) => ({
        type,
        avg_impressions: Math.round(
          data.impressions.reduce((a: number, b: number) => a + b, 0) / data.count
        )
      }))
      .sort((a, b) => b.avg_impressions - a.avg_impressions)
      .slice(0, 5);
    
    const bestCategories = Array.from(categoryPerformance.entries())
      .map(([category, data]) => ({
        category,
        avg_impressions: Math.round(
          data.impressions.reduce((a: number, b: number) => a + b, 0) / data.count
        )
      }))
      .sort((a, b) => b.avg_impressions - a.avg_impressions)
      .slice(0, 5);
    
    // Trending topics (most used tags)
    const trendingTopics = Array.from(topics.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([tag]) => tag);
    
    // Growth metrics (simplified - compare last 30 days to previous 30 days)
    const now = Date.now();
    const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);
    const sixtyDaysAgo = now - (60 * 24 * 60 * 60 * 1000);
    
    const recentContent = allContent.filter(c => c.content_created_date > thirtyDaysAgo);
    const previousContent = allContent.filter(c => 
      c.content_created_date > sixtyDaysAgo && c.content_created_date <= thirtyDaysAgo
    );
    
    const contentGrowthRate = previousContent.length > 0 ? 
      ((recentContent.length - previousContent.length) / previousContent.length) * 100 : 0;
    
    const recentImpressions = recentContent.reduce((sum, c) => sum + (c.twitter_impressions || 0), 0);
    const previousImpressions = previousContent.reduce((sum, c) => sum + (c.twitter_impressions || 0), 0);
    const impressionsGrowthRate = previousImpressions > 0 ? 
      ((recentImpressions - previousImpressions) / previousImpressions) * 100 : 0;
    
    return {
      best_performing_types: bestTypes,
      best_performing_categories: bestCategories,
      trending_topics: trendingTopics,
      growth_metrics: {
        content_growth_rate: Math.round(contentGrowthRate * 100) / 100,
        impressions_growth_rate: Math.round(impressionsGrowthRate * 100) / 100
      }
    };
  }
});