import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // Content pieces table - core content data
  content_pieces: defineTable({
    content_uuid: v.string(),
    content_title: v.optional(v.string()),
    content_description: v.optional(v.string()),
    content_link: v.string(), // REQUIRED - fixed schema mismatch
    content_created_date: v.number(), // timestamp
    content_types: v.array(v.string()), // content type enum values
    content_categories: v.array(v.string()), // category enum values
    content_tags: v.array(v.string()),
    content_account: v.array(v.string()), // REQUIRED - fixed schema mismatch
    host: v.optional(v.string()),
    
    // Twitter-specific fields
    twitter_content_type: v.optional(v.string()), // space, interview, tweet, thread, retweet
    twitter_impressions: v.optional(v.number()),
    twitter_likes: v.optional(v.number()),
    twitter_retweets: v.optional(v.number()),
    
    // Media metrics
    content_views: v.optional(v.number()),
    content_listeners: v.optional(v.number()),
    content_follow_increase: v.optional(v.number()),
    
    // Screenshots stored as URLs or file IDs
    screenshot_urls: v.array(v.string()),
    
    // Full-text search field (computed on server)
    fts: v.optional(v.string()),
  })
    .index("by_content_uuid", ["content_uuid"])
    .index("by_created_date", ["content_created_date"])
    .index("by_twitter_impressions", ["twitter_impressions"])
    .index("by_content_types", ["content_types"])
    .index("by_content_categories", ["content_categories"])
    .index("by_twitter_content_type", ["twitter_content_type"])
    // Compound indexes for efficient filtering and sorting
    .index("by_twitter_content_type_and_impressions", ["twitter_content_type", "twitter_impressions"])
    .index("by_twitter_content_type_and_date", ["twitter_content_type", "content_created_date"])
    .searchIndex("search_content", {
      searchField: "fts",
      filterFields: ["content_types", "content_categories", "twitter_content_type"]
    }),

  // User profiles for authentication and permissions
  user_profiles: defineTable({
    user_id: v.string(), // External auth ID
    email: v.string(),
    full_name: v.optional(v.string()),
    role: v.string(), // admin, user
  })
    .index("by_user_id", ["user_id"])
    .index("by_email", ["email"])
    .index("by_role", ["role"]),

  // Groups for content organization
  groups: defineTable({
    name: v.string(),
    user_id: v.string(),
    description: v.optional(v.string()),
    is_public: v.optional(v.boolean()),
  })
    .index("by_user_id", ["user_id"])
    .index("by_is_public", ["is_public"]),

  // Group content relationships
  group_content: defineTable({
    group_id: v.id("groups"),
    content_id: v.id("content_pieces"),
    position: v.optional(v.number()),
  })
    .index("by_group_id", ["group_id"])
    .index("by_content_id", ["content_id"])
    .index("by_group_and_position", ["group_id", "position"]),

  // Statistics tracking
  stats: defineTable({
    stat_type: v.string(), // total_impressions, total_content, avg_engagement_rate
    stat_value: v.number(),
    period_start: v.optional(v.number()),
    period_end: v.optional(v.number()),
    calculated_at: v.number(),
  })
    .index("by_stat_type", ["stat_type"])
    .index("by_calculated_at", ["calculated_at"])
    .index("by_period", ["period_start", "period_end"]),

  // Content type mappings for Google Sheets sync
  content_type_mappings: defineTable({
    source_value: v.string(),
    target_content_type: v.string(),
    twitter_content_type: v.optional(v.string()),
    description: v.optional(v.string()),
    is_active: v.optional(v.boolean()),
    created_by: v.optional(v.string()),
    updated_by: v.optional(v.string()),
  })
    .index("by_source_value", ["source_value"])
    .index("by_target_content_type", ["target_content_type"])
    .index("by_is_active", ["is_active"]),

  // Media account metrics
  media_account_metrics: defineTable({
    account_handle: v.string(),
    display_name: v.string(),
    description: v.optional(v.string()),
    profile_image_url: v.optional(v.string()),
    total_impressions: v.optional(v.number()),
    total_views: v.optional(v.number()),
    total_followers: v.optional(v.number()),
    is_active: v.optional(v.boolean()),
    display_order: v.optional(v.number()),
    created_by: v.optional(v.string()),
  })
    .index("by_account_handle", ["account_handle"])
    .index("by_is_active", ["is_active"])
    .index("by_display_order", ["display_order"]),

  // Spreadsheet sync configurations
  spreadsheet_sync_configs: defineTable({
    name: v.string(),
    spreadsheet_id: v.string(),
    tab_name: v.string(),
    target_table: v.optional(v.string()), // Generated table name like "sheet_shows"
    is_active: v.optional(v.boolean()),
    sync_order: v.optional(v.number()),
    last_sync_at: v.optional(v.number()),
    last_sync_status: v.optional(v.string()),
    last_sync_details: v.optional(v.string()), // JSON string
    created_by: v.optional(v.string()),
  })
    .index("by_is_active", ["is_active"])
    .index("by_sync_order", ["sync_order"])
    .index("by_last_sync_at", ["last_sync_at"])
    .index("by_target_table", ["target_table"]),

  // Dynamic sheet tables (Shows tab example)
  sheet_shows: defineTable({
    row_id: v.string(), // Unique identifier for the row
    content_link: v.string(), // Show Link - REQUIRED (rows without this are skipped)
    client_name: v.optional(v.union(v.string(), v.null())), // Client Name
    date: v.optional(v.union(v.string(), v.null())), // Date
    show_title: v.optional(v.union(v.string(), v.null())), // Show Title
    show_topic: v.optional(v.union(v.string(), v.null())), // Show Topic  
    show_type: v.optional(v.union(v.string(), v.null())), // Show Type
    views_listeners: v.optional(v.union(v.string(), v.null())), // Views / Listeners
    impressions: v.optional(v.union(v.string(), v.null())), // Impressions
    follow_increase: v.optional(v.union(v.string(), v.null())), // Follow Increase
    report_link: v.optional(v.union(v.string(), v.null())), // Report Link
    // Store original row data as JSON for flexibility
    raw_data: v.optional(v.string()),
    last_updated: v.number(),
  })
    .index("by_content_link", ["content_link"])
    .index("by_client_name", ["client_name"])
    .index("by_show_type", ["show_type"])
    .index("by_last_updated", ["last_updated"]),

  // Sync logs for tracking sync operations
  sync_logs: defineTable({
    config_id: v.optional(v.id("spreadsheet_sync_configs")),
    started_at: v.number(),
    completed_at: v.optional(v.number()),
    status: v.string(), // running, completed, failed, cancelled
    errors: v.optional(v.string()), // JSON string
    details: v.optional(v.string()), // JSON string
    duration_ms: v.optional(v.number()),
    records_created: v.optional(v.number()),
    records_updated: v.optional(v.number()),
    records_skipped: v.optional(v.number()),
    error_count: v.optional(v.number()),
  })
    .index("by_config_id", ["config_id"])
    .index("by_started_at", ["started_at"])
    .index("by_status", ["status"]),

  // Disabled enum values (for managing content types/categories)
  disabled_enum_values: defineTable({
    enum_type: v.string(),
    enum_value: v.string(),
    disabled_at: v.number(),
  })
    .index("by_enum_type", ["enum_type"])
    .index("by_enum_value", ["enum_value"])
    .index("by_disabled_at", ["disabled_at"]),

  // Custom metadata values that admins create through the dashboard
  custom_metadata_values: defineTable({
    enum_type: v.string(),
    value: v.string(),
    created_at: v.number(),
    created_by: v.optional(v.string()),
  })
    .index("by_enum_type", ["enum_type"])
    .index("by_enum_type_value", ["enum_type", "value"])
    .index("by_value", ["value"]),

  // Marketing case studies for landing & admin management
  marketing_case_studies: defineTable({
    slug: v.string(),
    title: v.string(),
    summary: v.optional(v.string()),
    cta_label: v.optional(v.string()),
    cta_url: v.string(),
    chart_series: v.array(
      v.object({
        id: v.string(),
        label: v.string(),
        color_token: v.optional(v.string()),
        stroke_color: v.optional(v.string()),
        variant: v.optional(v.union(v.literal("line"), v.literal("area"))),
        fill_opacity: v.optional(v.number()),
        points: v.array(
          v.object({
            date: v.string(),
            value: v.number(),
          }),
        ),
      }),
    ),
    legend: v.optional(
      v.array(
        v.object({
          label: v.string(),
          color_token: v.optional(v.string()),
          stroke_color: v.optional(v.string()),
        }),
      ),
    ),
    tags: v.optional(v.array(v.string())),
    is_featured: v.optional(v.boolean()),
    is_published: v.optional(v.boolean()),
    display_order: v.optional(v.number()),
    created_at: v.number(),
    updated_at: v.number(),
    created_by: v.optional(v.string()),
    updated_by: v.optional(v.string()),
    background_style: v.optional(v.string()),
  })
    .index("by_slug", ["slug"])
    .index("by_is_featured", ["is_featured"])
    .index("by_is_published", ["is_published"])
    .index("by_display_order", ["display_order"]),

  // Dashboard display settings (admin controlled visibility)
  dashboard_settings: defineTable({
    setting_key: v.string(),
    setting_value: v.boolean(),
    updated_by: v.optional(v.string()),
    updated_at: v.number(),
  })
    .index("by_key", ["setting_key"])
    .index("by_updated_at", ["updated_at"]),
});
