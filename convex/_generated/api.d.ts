/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as admin from "../admin.js";
import type * as auth from "../auth.js";
import type * as content from "../content.js";
import type * as crons from "../crons.js";
import type * as dashboardSettings from "../dashboardSettings.js";
import type * as files from "../files.js";
import type * as googleSheetsSync from "../googleSheetsSync.js";
import type * as groups from "../groups.js";
import type * as marketingCaseStudies from "../marketingCaseStudies.js";
import type * as migrations from "../migrations.js";
import type * as stats from "../stats.js";
import type * as testimonials from "../testimonials.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  admin: typeof admin;
  auth: typeof auth;
  content: typeof content;
  crons: typeof crons;
  dashboardSettings: typeof dashboardSettings;
  files: typeof files;
  googleSheetsSync: typeof googleSheetsSync;
  groups: typeof groups;
  marketingCaseStudies: typeof marketingCaseStudies;
  migrations: typeof migrations;
  stats: typeof stats;
  testimonials: typeof testimonials;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
