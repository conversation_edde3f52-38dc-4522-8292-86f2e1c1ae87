import { cronJobs } from "convex/server";
import { internal } from "./_generated/api";
import { internalAction, internalQuery, internalMutation } from "./_generated/server";
import { v } from "convex/values";

// Daily stats calculation action
export const calculateDailyStatsAction = internalAction({
  args: {},
  returns: v.null(),
  handler: async (ctx, args) => {
    console.log("Running daily stats calculation...");
    
    // Calculate daily stats using the stats mutation
    await ctx.runMutation(internal.stats.calculateDailyStats, {});
    
    console.log("Daily stats calculation completed");
    return null;
  },
});

// Google Sheets sync action
export const syncGoogleSheetsAction = internalAction({
  args: {},
  returns: v.null(),
  handler: async (ctx, args) => {
    console.log("Running Google Sheets sync...");
    
    // Trigger the Google Sheets sync
    await ctx.runAction(internal.googleSheetsSync.performGoogleSheetsSync, {});
    
    console.log("Google Sheets sync completed");
    return null;
  },
});

// Cleanup old sync logs action
export const cleanupSyncLogsAction = internalAction({
  args: {},
  returns: v.null(),
  handler: async (ctx, args) => {
    console.log("Cleaning up old sync logs...");
    
    // Delete sync logs older than 30 days
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const oldLogs = await ctx.runQuery(internal.crons.getOldSyncLogs, {
      before: thirtyDaysAgo
    });
    
    for (const log of oldLogs) {
      await ctx.runMutation(internal.crons.deleteSyncLog, {
        log_id: log._id
      });
    }
    
    console.log(`Cleaned up ${oldLogs.length} old sync logs`);
    return null;
  },
});

// Set up cron jobs
const crons = cronJobs();

// Daily stats calculation at 1 AM every day
crons.interval(
  "calculate daily stats",
  { hours: 24 }, // Every 24 hours
  internal.crons.calculateDailyStatsAction,
  {}
);

// Google Sheets sync every 6 hours
crons.interval(
  "sync google sheets",
  { hours: 6 }, // Every 6 hours
  internal.crons.syncGoogleSheetsAction,
  {}
);

// Cleanup old logs weekly (every 7 days)
crons.interval(
  "cleanup old logs",
  { hours: 24 * 7 }, // Every 7 days
  internal.crons.cleanupSyncLogsAction,
  {}
);

// Internal helper functions
export const getOldSyncLogs = internalQuery({
  args: {
    before: v.number()
  },
  returns: v.array(v.object({
    _id: v.id("sync_logs"),
    started_at: v.number(),
    config_id: v.optional(v.id("spreadsheet_sync_configs")),
    status: v.string(),
    _creationTime: v.number()
  })),
  handler: async (ctx, args) => {
    return await ctx.db
      .query("sync_logs")
      .withIndex("by_started_at", (q: any) => q.lt("started_at", args.before))
      .collect();
  }
});

export const deleteSyncLog = internalMutation({
  args: {
    log_id: v.id("sync_logs")
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await ctx.db.delete(args.log_id);
    return null;
  }
});

export default crons;