import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Generate upload URL for file storage
export const generateUploadUrl = mutation({
  args: {},
  returns: v.string(),
  handler: async (ctx) => {
    return await ctx.storage.generateUploadUrl();
  }
});

// Store file metadata after upload
export const saveFileMetadata = mutation({
  args: {
    storageId: v.id("_storage"),
    filename: v.string(),
    contentType: v.optional(v.string()),
    size: v.optional(v.number()),
    contentId: v.optional(v.id("content_pieces"))
  },
  returns: v.id("_storage"),
  handler: async (ctx, args) => {
    // The file is already stored in Convex storage
    // We can add metadata to our content_pieces table if needed
    if (args.contentId) {
      const content = await ctx.db.get(args.contentId);
      if (content) {
        // Add the storage ID to screenshot_urls array
        const updatedUrls = [...content.screenshot_urls, args.storageId];
        await ctx.db.patch(args.contentId, {
          screenshot_urls: updatedUrls
        });
      }
    }
    
    return args.storageId;
  }
});

// Get file URL
export const getFileUrl = query({
  args: {
    storageId: v.id("_storage")
  },
  returns: v.union(v.string(), v.null()),
  handler: async (ctx, args) => {
    return await ctx.storage.getUrl(args.storageId);
  }
});

// Delete file
export const deleteFile = mutation({
  args: {
    storageId: v.id("_storage"),
    contentId: v.optional(v.id("content_pieces"))
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Remove from content_pieces if specified
    if (args.contentId) {
      const content = await ctx.db.get(args.contentId);
      if (content) {
        const updatedUrls = content.screenshot_urls.filter(url => url !== args.storageId);
        await ctx.db.patch(args.contentId, {
          screenshot_urls: updatedUrls
        });
      }
    }
    
    // Delete from storage
    await ctx.storage.delete(args.storageId);
    return null;
  }
});

// Get multiple file URLs
export const getMultipleFileUrls = query({
  args: {
    storageIds: v.array(v.id("_storage"))
  },
  returns: v.array(v.object({
    storageId: v.id("_storage"),
    url: v.union(v.string(), v.null())
  })),
  handler: async (ctx, args) => {
    const results = [];
    for (const storageId of args.storageIds) {
      const url = await ctx.storage.getUrl(storageId);
      results.push({
        storageId,
        url
      });
    }
    return results;
  }
});