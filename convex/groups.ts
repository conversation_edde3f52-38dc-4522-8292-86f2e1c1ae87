import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import type { Id } from "./_generated/dataModel";

// Get all groups for a user
export const getGroups = query({
  args: {},
  returns: v.array(v.any()),
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    const groups = await ctx.db
      .query("groups")
      .withIndex("by_user_id", (q) => q.eq("user_id", identity.subject))
      .collect();

    // Add content count for each group
    const groupsWithCount = [];
    for (const group of groups) {
      const contentCount = await ctx.db
        .query("group_content")
        .withIndex("by_group_id", (q) => q.eq("group_id", group._id))
        .collect();

      groupsWithCount.push({
        ...group,
        content_count: contentCount.length
      });
    }

    return groupsWithCount;
  }
});

// Get public groups
export const getPublicGroups = query({
  args: {},
  returns: v.array(v.any()),
  handler: async (ctx) => {
    return await ctx.db
      .query("groups")
      .withIndex("by_is_public", (q) => q.eq("is_public", true))
      .collect();
  }
});

// Get group by ID (with authentication check)
export const getGroupById = query({
  args: {
    group_id: v.id("groups")
  },
  returns: v.union(v.any(), v.null()),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User not authenticated");
    }

    const group = await ctx.db.get(args.group_id);
    if (!group) {
      return null;
    }

    // User can access their own groups or public groups
    if (group.user_id !== identity.subject && !group.is_public) {
      throw new Error("Access denied");
    }

    // Add content count and content details
    const content = await ctx.db
      .query("group_content")
      .withIndex("by_group_and_position", (q) => q.eq("group_id", args.group_id))
      .order("asc")
      .collect();

    const contentWithDetails = [];
    for (const gc of content) {
      const contentPiece = await ctx.db.get(gc.content_id);
      if (contentPiece) {
        contentWithDetails.push({
          ...contentPiece,
          position: gc.position,
          group_content_id: gc._id,
          added_at: gc._creationTime
        });
      }
    }

    return {
      ...group,
      content: contentWithDetails,
      content_count: contentWithDetails.length
    };
  }
});

// Get public group by ID (no authentication required)
export const getPublicGroupById = query({
  args: {
    group_id: v.id("groups")
  },
  returns: v.union(v.any(), v.null()),
  handler: async (ctx, args) => {
    const group = await ctx.db.get(args.group_id);
    if (!group || !group.is_public) {
      return null;
    }

    // Add content count and content details
    const content = await ctx.db
      .query("group_content")
      .withIndex("by_group_and_position", (q) => q.eq("group_id", args.group_id))
      .order("asc")
      .collect();

    const contentWithDetails = [];
    for (const gc of content) {
      const contentPiece = await ctx.db.get(gc.content_id);
      if (contentPiece) {
        contentWithDetails.push({
          ...contentPiece,
          position: gc.position,
          group_content_id: gc._id,
          added_at: gc._creationTime
        });
      }
    }

    return {
      ...group,
      content: contentWithDetails,
      content_count: contentWithDetails.length
    };
  }
});

// Get group content with details
export const getGroupContent = query({
  args: {
    group_id: v.id("groups")
  },
  returns: v.array(v.any()),
  handler: async (ctx, args) => {
    // Get group content relationships ordered by position
    const groupContent = await ctx.db
      .query("group_content")
      .withIndex("by_group_and_position", (q) => q.eq("group_id", args.group_id))
      .order("asc")
      .collect();
    
    // Get full content details for each item
    const contentWithDetails = [];
    for (const gc of groupContent) {
      const content = await ctx.db.get(gc.content_id);
      if (content) {
        contentWithDetails.push({
          ...content,
          position: gc.position,
          group_content_id: gc._id,
          added_at: gc._creationTime
        });
      }
    }
    
    return contentWithDetails;
  }
});

// Get group content count
export const getGroupContentCount = query({
  args: {
    group_id: v.id("groups")
  },
  returns: v.number(),
  handler: async (ctx, args) => {
    const groupContent = await ctx.db
      .query("group_content")
      .withIndex("by_group_id", (q) => q.eq("group_id", args.group_id))
      .collect();
    
    return groupContent.length;
  }
});

// Create a new group
export const createGroup = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    is_public: v.optional(v.boolean())
  },
  returns: v.id("groups"),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User not authenticated");
    }

    return await ctx.db.insert("groups", {
      name: args.name,
      user_id: identity.subject,
      description: args.description,
      is_public: args.is_public || false
    });
  }
});

// Update a group
export const updateGroup = mutation({
  args: {
    group_id: v.id("groups"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    is_public: v.optional(v.boolean())
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User not authenticated");
    }

    const group = await ctx.db.get(args.group_id);
    if (!group) {
      throw new Error("Group not found");
    }

    // Only the owner can update the group
    if (group.user_id !== identity.subject) {
      throw new Error("Access denied");
    }

    const { group_id, ...updates } = args;
    await ctx.db.patch(group_id, updates);
    return null;
  }
});

// Delete a group
export const deleteGroup = mutation({
  args: {
    group_id: v.id("groups")
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User not authenticated");
    }

    const group = await ctx.db.get(args.group_id);
    if (!group) {
      throw new Error("Group not found");
    }

    // Only the owner can delete the group
    if (group.user_id !== identity.subject) {
      throw new Error("Access denied");
    }

    // First, delete all group content relationships
    const groupContent = await ctx.db
      .query("group_content")
      .withIndex("by_group_id", (q) => q.eq("group_id", args.group_id))
      .collect();

    for (const gc of groupContent) {
      await ctx.db.delete(gc._id);
    }

    // Then delete the group
    await ctx.db.delete(args.group_id);
    return null;
  }
});

// Add content to a group
export const addContentToGroup = mutation({
  args: {
    group_id: v.id("groups"),
    content_id: v.id("content_pieces"),
    position: v.optional(v.number())
  },
  returns: v.id("group_content"),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User not authenticated");
    }

    // Verify user owns the group
    const group = await ctx.db.get(args.group_id);
    if (!group) {
      throw new Error("Group not found");
    }

    if (group.user_id !== identity.subject) {
      throw new Error("Access denied");
    }

    // Check if content is already in the group
    const existing = await ctx.db
      .query("group_content")
      .withIndex("by_group_id", (q) => q.eq("group_id", args.group_id))
      .filter((q) => q.eq(q.field("content_id"), args.content_id))
      .first();

    if (existing) {
      // Idempotent behaviour: if caller provides a new position, update it, otherwise return the existing entry
      if (args.position !== undefined && existing.position !== args.position) {
        await ctx.db.patch(existing._id, { position: args.position });
      }
      return existing._id;
    }

    // Get current max position if no position specified
    let position = args.position;
    if (position === undefined) {
      const groupContent = await ctx.db
        .query("group_content")
        .withIndex("by_group_id", (q) => q.eq("group_id", args.group_id))
        .collect();

      position = Math.max(...groupContent.map(gc => gc.position || 0), -1) + 1;
    }

    return await ctx.db.insert("group_content", {
      group_id: args.group_id,
      content_id: args.content_id,
      position
    });
  }
});

// Remove content from a group
export const removeContentFromGroup = mutation({
  args: {
    group_content_id: v.id("group_content")
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await ctx.db.delete(args.group_content_id);
    return null;
  }
});

// Remove content from group by content and group IDs
export const removeContentFromGroupByIds = mutation({
  args: {
    group_id: v.id("groups"),
    content_id: v.id("content_pieces")
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("User not authenticated");
    }

    // Verify user owns the group
    const group = await ctx.db.get(args.group_id);
    if (!group) {
      throw new Error("Group not found");
    }

    if (group.user_id !== identity.subject) {
      throw new Error("Access denied");
    }

    const groupContent = await ctx.db
      .query("group_content")
      .withIndex("by_group_id", (q) => q.eq("group_id", args.group_id))
      .filter((q) => q.eq(q.field("content_id"), args.content_id))
      .first();

    if (groupContent) {
      await ctx.db.delete(groupContent._id);
    }

    return null;
  }
});

// Reorder content in a group
export const reorderGroupContent = mutation({
  args: {
    group_content_id: v.id("group_content"),
    new_position: v.number()
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await ctx.db.patch(args.group_content_id, {
      position: args.new_position
    });
    return null;
  }
});

// Bulk reorder group content
export const bulkReorderGroupContent = mutation({
  args: {
    updates: v.array(v.object({
      group_content_id: v.id("group_content"),
      position: v.number()
    }))
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    for (const update of args.updates) {
      await ctx.db.patch(update.group_content_id, {
        position: update.position
      });
    }
    return null;
  }
});

// Check if content is in a group
export const isContentInGroup = query({
  args: {
    group_id: v.id("groups"),
    content_id: v.id("content_pieces")
  },
  returns: v.boolean(),
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("group_content")
      .withIndex("by_group_id", (q) => q.eq("group_id", args.group_id))
      .filter((q) => q.eq(q.field("content_id"), args.content_id))
      .first();
    
    return !!existing;
  }
});

// Get groups that contain specific content
export const getGroupsContainingContent = query({
  args: {
    content_id: v.id("content_pieces")
  },
  returns: v.array(v.any()),
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    const userGroups = await ctx.db
      .query("groups")
      .withIndex("by_user_id", (q) => q.eq("user_id", identity.subject))
      .collect();

    if (userGroups.length === 0) {
      return [];
    }

    const userGroupsById = new Map(userGroups.map((group) => [group._id, group]));

    // Get group content relationships for this content
    const groupContent = await ctx.db
      .query("group_content")
      .withIndex("by_content_id", (q) => q.eq("content_id", args.content_id))
      .collect();

    // Get full group details
    const groups = [];
    for (const gc of groupContent) {
      const group = userGroupsById.get(gc.group_id);
      if (group) {
        groups.push({
          ...group,
          position: gc.position,
          added_at: gc._creationTime
        });
      }
    }

    return groups;
  }
});

// Get all content IDs pinned by the current user across their groups
export const getPinnedContentIds = query({
  args: {},
  returns: v.array(v.id("content_pieces")),
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return [];
    }

    const groups = await ctx.db
      .query("groups")
      .withIndex("by_user_id", (q) => q.eq("user_id", identity.subject))
      .collect();

    const pinned = new Set<Id<"content_pieces">>();

    for (const group of groups) {
      const groupContent = await ctx.db
        .query("group_content")
        .withIndex("by_group_id", (q) => q.eq("group_id", group._id))
        .collect();

      for (const gc of groupContent) {
        pinned.add(gc.content_id);
      }
    }

    return Array.from(pinned.values()) as Id<"content_pieces">[];
  }
});

// Get group statistics
export const getGroupStats = query({
  args: {
    group_id: v.id("groups")
  },
  returns: v.object({
    total_content: v.number(),
    total_impressions: v.number(),
    total_views: v.number(),
    content_types: v.array(v.string()),
    categories: v.array(v.string()),
    latest_added: v.optional(v.number())
  }),
  handler: async (ctx, args) => {
    const groupContent = await ctx.db
      .query("group_content")
      .withIndex("by_group_id", (q) => q.eq("group_id", args.group_id))
      .collect();
    
    let totalImpressions = 0;
    let totalViews = 0;
    const contentTypes = new Set<string>();
    const categories = new Set<string>();
    let latestAdded = 0;
    
    for (const gc of groupContent) {
      const content = await ctx.db.get(gc.content_id);
      if (content) {
        totalImpressions += content.twitter_impressions || 0;
        totalViews += content.content_views || 0;
        
        content.content_types.forEach(type => contentTypes.add(type));
        content.content_categories.forEach(cat => categories.add(cat));
        
        if (gc._creationTime && gc._creationTime > latestAdded) {
          latestAdded = gc._creationTime;
        }
      }
    }
    
    return {
      total_content: groupContent.length,
      total_impressions: totalImpressions,
      total_views: totalViews,
      content_types: Array.from(contentTypes),
      categories: Array.from(categories),
      latest_added: latestAdded || undefined
    };
  }
});
