import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Get current authenticated user
export const getCurrentUser = query({
  args: {},
  returns: v.union(v.object({
    _id: v.id("user_profiles"),
    user_id: v.string(),
    email: v.string(),
    full_name: v.optional(v.string()),
    role: v.string(),
    _creationTime: v.number()
  }), v.null()),
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }
    
    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_user_id", (q: any) => q.eq("user_id", identity.subject))
      .first();
    
    return user;
  }
});

// Get current user profile (for frontend compatibility)
export const getUserProfile = query({
  args: {},
  returns: v.union(v.object({
    _id: v.id("user_profiles"),
    user_id: v.string(),
    email: v.string(),
    full_name: v.optional(v.string()),
    role: v.string(),
    _creationTime: v.number()
  }), v.null()),
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }
    
    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_user_id", (q: any) => q.eq("user_id", identity.subject))
      .first();
    
    return user;
  }
});

// Get user profile by user_id
export const getUserProfileById = query({
  args: {
    user_id: v.string()
  },
  returns: v.union(v.object({
    _id: v.id("user_profiles"),
    user_id: v.string(),
    email: v.string(),
    full_name: v.optional(v.string()),
    role: v.string(),
    _creationTime: v.number()
  }), v.null()),
  handler: async (ctx, args) => {
    return await ctx.db
      .query("user_profiles")
      .withIndex("by_user_id", (q) => q.eq("user_id", args.user_id))
      .first();
  }
});

// Get user profile by email
export const getUserByEmail = query({
  args: {
    email: v.string()
  },
  returns: v.union(v.object({
    _id: v.id("user_profiles"),
    user_id: v.string(),
    email: v.string(),
    full_name: v.optional(v.string()),
    role: v.string(),
    _creationTime: v.number()
  }), v.null()),
  handler: async (ctx, args) => {
    return await ctx.db
      .query("user_profiles")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();
  }
});

// Create or update user profile
export const createOrUpdateUser = mutation({
  args: {
    user_id: v.string(),
    email: v.string(),
    full_name: v.optional(v.string()),
    role: v.optional(v.string())
  },
  returns: v.id("user_profiles"),
  handler: async (ctx, args) => {
    // Check if user already exists
    const existingUser = await ctx.db
      .query("user_profiles")
      .withIndex("by_user_id", (q) => q.eq("user_id", args.user_id))
      .first();
    
    if (existingUser) {
      // Update existing user
      await ctx.db.patch(existingUser._id, {
        email: args.email,
        full_name: args.full_name,
        role: args.role || existingUser.role
      });
      return existingUser._id;
    } else {
      // Create new user
      return await ctx.db.insert("user_profiles", {
        user_id: args.user_id,
        email: args.email,
        full_name: args.full_name,
        role: args.role || "user"
      });
    }
  }
});

// Update user role (admin only)
export const updateUserRole = mutation({
  args: {
    user_id: v.string(),
    new_role: v.string()
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_user_id", (q) => q.eq("user_id", args.user_id))
      .first();
    
    if (!user) {
      throw new Error("User not found");
    }
    
    await ctx.db.patch(user._id, {
      role: args.new_role
    });
    
    return null;
  }
});

// Check if user is admin
export const isUserAdmin = query({
  args: {
    user_id: v.string()
  },
  returns: v.boolean(),
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_user_id", (q) => q.eq("user_id", args.user_id))
      .first();
    
    return user?.role === "admin" || false;
  }
});

// Get all users (admin only)
export const getAllUsers = query({
  args: {},
  returns: v.array(v.object({
    _id: v.id("user_profiles"),
    user_id: v.string(),
    email: v.string(),
    full_name: v.optional(v.string()),
    role: v.string(),
    _creationTime: v.number()
  })),
  handler: async (ctx) => {
    return await ctx.db.query("user_profiles").collect();
  }
});

// Get users by role
export const getUsersByRole = query({
  args: {
    role: v.string()
  },
  returns: v.array(v.object({
    _id: v.id("user_profiles"),
    user_id: v.string(),
    email: v.string(),
    full_name: v.optional(v.string()),
    role: v.string(),
    _creationTime: v.number()
  })),
  handler: async (ctx, args) => {
    return await ctx.db
      .query("user_profiles")
      .withIndex("by_role", (q) => q.eq("role", args.role))
      .collect();
  }
});

// Delete user profile
export const deleteUser = mutation({
  args: {
    user_id: v.string()
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_user_id", (q) => q.eq("user_id", args.user_id))
      .first();
    
    if (user) {
      await ctx.db.delete(user._id);
    }
    
    return null;
  }
});

// Update user profile
export const updateUserProfile = mutation({
  args: {
    user_id: v.string(),
    full_name: v.optional(v.string()),
    email: v.optional(v.string())
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("user_profiles")
      .withIndex("by_user_id", (q) => q.eq("user_id", args.user_id))
      .first();
    
    if (!user) {
      throw new Error("User not found");
    }
    
    const updates: any = {};
    if (args.full_name !== undefined) updates.full_name = args.full_name;
    if (args.email !== undefined) updates.email = args.email;
    
    if (Object.keys(updates).length > 0) {
      await ctx.db.patch(user._id, updates);
    }
    
    return null;
  }
});