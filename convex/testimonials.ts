import { query, mutation, internalMutation } from "./_generated/server";
import { v } from "convex/values";

// Helper function to check if user is admin
async function requireAdmin(ctx: any) {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    throw new Error("Authentication required");
  }
  
  const user = await ctx.db
    .query("user_profiles")
    .withIndex("by_user_id", (q: any) => q.eq("user_id", identity.subject))
    .first();
  
  if (!user || user.role !== "admin") {
    throw new Error("Admin privileges required");
  }
  
  return user;
}

// Get all testimonials with pagination
export const getTestimonials = query({
  args: {
    page: v.optional(v.number()),
    limit: v.optional(v.number())
  },
  returns: v.object({
    data: v.array(v.any()),
    total: v.number(),
    page: v.number(),
    totalPages: v.number()
  }),
  handler: async (ctx, args) => {
    const page = args.page || 1;
    const limit = args.limit || 12;
    const offset = (page - 1) * limit;
    
    // Get all content pieces and filter for testimonials
    const allContent = await ctx.db.query("content_pieces").collect();
    const testimonials = allContent
      .filter(content => content.content_types.includes("testimonials"))
      .sort((a, b) => b.content_created_date - a.content_created_date);
    
    // Apply pagination
    const paginatedTestimonials = testimonials.slice(offset, offset + limit);
    
    return {
      data: paginatedTestimonials,
      total: testimonials.length,
      page,
      totalPages: Math.ceil(testimonials.length / limit)
    };
  }
});

// Get testimonials for dashboard display (limited)
export const getFeaturedTestimonials = query({
  args: { 
    limit: v.optional(v.number()) 
  },
  returns: v.array(v.any()),
  handler: async (ctx, args) => {
    const limit = args.limit || 6;
    
    // Get all content pieces and filter for testimonials
    const allContent = await ctx.db.query("content_pieces").collect();
    const testimonials = allContent
      .filter(content => content.content_types.includes("testimonials"))
      .sort((a, b) => b.content_created_date - a.content_created_date)
      .slice(0, limit);
    
    return testimonials;
  }
});

// Create new testimonial
export const createTestimonial = mutation({
  args: {
    content_title: v.string(), // Project name
    content_description: v.string(), // Testimonial text
    client_name: v.string(), // Client name
    content_link: v.optional(v.string()), // Project URL
    screenshot_urls: v.optional(v.array(v.string())), // Screenshot storage IDs
    satisfaction_rating: v.optional(v.number()), // 1-5 rating
    project_value: v.optional(v.number()) // Project value/impact
  },
  returns: v.id("content_pieces"),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    
    // Build full-text search string
    const fts = [
      args.content_title,
      args.content_description,
      args.client_name
    ].filter(Boolean).join(" ").toLowerCase();
    
    const testimonialData = {
      content_uuid: crypto.randomUUID(),
      content_title: args.content_title,
      content_description: args.content_description,
      content_link: args.content_link || "",
      content_account: [args.client_name],
      content_created_date: Date.now(),
      content_types: ["testimonials"],
      content_categories: ["client-work"],
      content_tags: [],
      screenshot_urls: args.screenshot_urls || [],
      content_views: args.satisfaction_rating || 0, // Store rating in views field
      twitter_impressions: args.project_value || 0, // Store project value
      content_listeners: 0,
      content_follow_increase: 0,
      twitter_likes: 0,
      twitter_retweets: 0,
      fts
    };
    
    return await ctx.db.insert("content_pieces", testimonialData);
  }
});

// Update testimonial
export const updateTestimonial = mutation({
  args: {
    id: v.id("content_pieces"),
    content_title: v.optional(v.string()),
    content_description: v.optional(v.string()),
    client_name: v.optional(v.string()),
    content_link: v.optional(v.string()),
    screenshot_urls: v.optional(v.array(v.string())),
    satisfaction_rating: v.optional(v.number()),
    project_value: v.optional(v.number())
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    
    const { id, client_name, satisfaction_rating, project_value, ...updates } = args;
    
    // Get current testimonial
    const current = await ctx.db.get(id);
    if (!current || !current.content_types.includes("testimonials")) {
      throw new Error("Testimonial not found");
    }
    
    // Prepare updates
    const testimonialUpdates: any = { ...updates };
    
    if (client_name !== undefined) {
      testimonialUpdates.content_account = [client_name];
    }
    
    if (satisfaction_rating !== undefined) {
      testimonialUpdates.content_views = satisfaction_rating;
    }
    
    if (project_value !== undefined) {
      testimonialUpdates.twitter_impressions = project_value;
    }
    
    // Update FTS if text fields changed
    if (updates.content_title !== undefined || 
        updates.content_description !== undefined || 
        client_name !== undefined) {
      
      testimonialUpdates.fts = [
        updates.content_title || current.content_title || "",
        updates.content_description || current.content_description || "",
        client_name || current.content_account?.[0] || ""
      ].filter(Boolean).join(" ").toLowerCase();
    }
    
    await ctx.db.patch(id, testimonialUpdates);
    return null;
  }
});

// Delete testimonial
export const deleteTestimonial = mutation({
  args: {
    id: v.id("content_pieces")
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    
    // Verify it's a testimonial
    const testimonial = await ctx.db.get(args.id);
    if (!testimonial || !testimonial.content_types.includes("testimonials")) {
      throw new Error("Testimonial not found");
    }
    
    // Delete the testimonial
    await ctx.db.delete(args.id);
    
    // Note: Screenshot files in Convex storage are not automatically deleted
    // They'll be cleaned up by Convex's garbage collection if not referenced elsewhere
    
    return null;
  }
});

// Add screenshot to testimonial
export const addScreenshotToTestimonial = mutation({
  args: {
    testimonialId: v.id("content_pieces"),
    storageId: v.string()
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    
    const testimonial = await ctx.db.get(args.testimonialId);
    if (!testimonial || !testimonial.content_types.includes("testimonials")) {
      throw new Error("Testimonial not found");
    }
    
    // Add the storage ID to screenshot_urls array
    const updatedUrls = [...testimonial.screenshot_urls, args.storageId];
    await ctx.db.patch(args.testimonialId, {
      screenshot_urls: updatedUrls
    });
    
    return null;
  }
});

// Remove screenshot from testimonial
export const removeScreenshotFromTestimonial = mutation({
  args: {
    testimonialId: v.id("content_pieces"),
    storageId: v.string()
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    await requireAdmin(ctx);
    
    const testimonial = await ctx.db.get(args.testimonialId);
    if (!testimonial || !testimonial.content_types.includes("testimonials")) {
      throw new Error("Testimonial not found");
    }
    
    // Remove the storage ID from screenshot_urls array
    const updatedUrls = testimonial.screenshot_urls.filter(url => url !== args.storageId);
    await ctx.db.patch(args.testimonialId, {
      screenshot_urls: updatedUrls
    });
    
    return null;
  }
});

// Search testimonials
export const searchTestimonials = query({
  args: {
    searchQuery: v.string(),
    limit: v.optional(v.number())
  },
  returns: v.array(v.any()),
  handler: async (ctx, args) => {
    const limit = args.limit || 20;
    const query = args.searchQuery.toLowerCase();
    
    if (!query.trim()) return [];
    
    // Get all testimonials and filter by search
    const allContent = await ctx.db.query("content_pieces").collect();
    const testimonials = allContent
      .filter(content => content.content_types.includes("testimonials"))
      .filter(content => 
        content.fts?.includes(query) ||
        content.content_title?.toLowerCase().includes(query) ||
        content.content_description?.toLowerCase().includes(query) ||
        content.content_account?.some(account => 
          account.toLowerCase().includes(query)
        )
      )
      .sort((a, b) => b.content_created_date - a.content_created_date)
      .slice(0, limit);
    
    return testimonials;
  }
});