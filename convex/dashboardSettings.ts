import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

export const getDashboardSettings = query({
  args: {},
  handler: async (ctx) => {
    const settings = await ctx.db.query("dashboard_settings").collect();
    const settingsMap: Record<string, boolean> = {};
    
    for (const setting of settings) {
      settingsMap[setting.setting_key] = setting.setting_value;
    }
    
    // Default values if not set
    return {
      twitter_impressions_visible: settingsMap.twitter_impressions_visible ?? true,
      twitter_likes_visible: settingsMap.twitter_likes_visible ?? true,
      twitter_retweets_visible: settingsMap.twitter_retweets_visible ?? true,
      content_views_visible: settingsMap.content_views_visible ?? true,
      content_created_date_visible: settingsMap.content_created_date_visible ?? true,
    };
  },
});

export const updateDashboardSetting = mutation({
  args: {
    setting_key: v.string(),
    setting_value: v.boolean(),
    updated_by: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("dashboard_settings")
      .filter((q) => q.eq(q.field("setting_key"), args.setting_key))
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, {
        setting_value: args.setting_value,
        updated_by: args.updated_by,
        updated_at: Date.now(),
      });
    } else {
      await ctx.db.insert("dashboard_settings", {
        setting_key: args.setting_key,
        setting_value: args.setting_value,
        updated_by: args.updated_by,
        updated_at: Date.now(),
      });
    }
  },
});