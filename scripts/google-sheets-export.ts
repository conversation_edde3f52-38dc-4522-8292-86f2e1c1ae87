import { google } from 'googleapis';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables from the web app
dotenv.config({ path: path.join(__dirname, '../apps/web/.env.local') });

async function exportSheetsToCSV() {
  try {
    // Get environment variables
    const serviceAccountEmail = process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL;
    let privateKey = process.env.GOOGLE_PRIVATE_KEY
      ?.replace(/^"|"$/g, '')
      ?.replace(/^'|'$/g, '')
      ?.replace(/\\n/g, '\n')
      ?.trim();

    const sheetsId = process.env.GOOGLE_SHEETS_ID;
    const tabName = process.env.GOOGLE_SHEETS_TAB || 'Shows';

    console.log('🔍 Exporting Google Sheets to CSV...');
    console.log(`Sheet ID: ${sheetsId}`);
    console.log(`Tab: ${tabName}`);

    if (!serviceAccountEmail || !privateKey || !sheetsId) {
      throw new Error('Missing required environment variables');
    }

    // Authenticate
    const auth = new google.auth.JWT({
      email: serviceAccountEmail,
      key: privateKey,
      scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly']
    });

    await auth.authorize();
    console.log('✅ Authenticated with Google Sheets API');

    const sheets = google.sheets({ version: 'v4', auth });

    // Fetch all data from the sheet (A to J columns, all rows)
    const response = await sheets.spreadsheets.values.get({
      spreadsheetId: sheetsId,
      range: `${tabName}!A:J`
    });

    const rows = response.data.values || [];
    console.log(`📊 Found ${rows.length} rows (including header)`);

    if (rows.length === 0) {
      throw new Error('No data found in sheet');
    }

    // Convert to CSV format
    const csvContent = rows.map(row => {
      // Ensure each row has 10 columns (pad with empty strings if needed)
      const paddedRow = [...row];
      while (paddedRow.length < 10) {
        paddedRow.push('');
      }
      
      // Escape CSV values (wrap in quotes if they contain commas, quotes, or newlines)
      return paddedRow.map(cell => {
        const cellStr = String(cell || '');
        if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
          // Escape quotes by doubling them and wrap in quotes
          return `"${cellStr.replace(/"/g, '""')}"`;
        }
        return cellStr;
      }).join(',');
    }).join('\n');

    // Create exports directory if it doesn't exist
    const exportsDir = path.join(__dirname, 'exports');
    if (!fs.existsSync(exportsDir)) {
      fs.mkdirSync(exportsDir, { recursive: true });
    }

    // Save to CSV file with timestamp in the exports folder
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const fileName = `google-sheets-export-${timestamp}.csv`;
    const filePath = path.join(exportsDir, fileName);
    
    fs.writeFileSync(filePath, csvContent, 'utf8');
    
    console.log(`✅ Successfully exported ${rows.length} rows to: scripts/exports/${fileName}`);
    
    // Show first few rows for verification
    console.log('\n📋 First 3 rows:');
    rows.slice(0, 3).forEach((row, index) => {
      console.log(`Row ${index + 1}: [${row.join(', ')}]`);
    });

    // Show last few rows
    if (rows.length > 3) {
      console.log('\n📋 Last 3 rows:');
      rows.slice(-3).forEach((row, index) => {
        const actualRowNum = rows.length - 3 + index + 1;
        console.log(`Row ${actualRowNum}: [${row.join(', ')}]`);
      });
    }

    // Show stats
    console.log(`\n📈 Export Statistics:`);
    console.log(`  • Total rows: ${rows.length} (${rows.length - 1} data rows + 1 header)`);
    console.log(`  • Columns: ${rows[0]?.length || 0}`);
    console.log(`  • File size: ${(fs.statSync(filePath).size / 1024).toFixed(2)} KB`);
    console.log(`  • Saved to: scripts/exports/${fileName}`);

    return { fileName, filePath, rowCount: rows.length };

  } catch (error: any) {
    console.error('❌ Error:', error.message);
    throw error;
  }
}

// Run the export if this script is executed directly
if (require.main === module) {
  exportSheetsToCSV()
    .then(({ fileName, rowCount }) => {
      console.log(`\n🎉 Export complete! ${rowCount} rows saved to scripts/exports/${fileName}`);
    })
    .catch(error => {
      console.error('💥 Export failed:', error.message);
      process.exit(1);
    });
}

// Export the function for programmatic use
export default exportSheetsToCSV;